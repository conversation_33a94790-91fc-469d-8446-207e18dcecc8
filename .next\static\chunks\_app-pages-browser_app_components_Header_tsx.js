"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_Header_tsx"],{

/***/ "(app-pages-browser)/./app/components/DropdownMenu.tsx":
/*!*****************************************!*\
  !*** ./app/components/DropdownMenu.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DropdownMenu; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n/**\n * 通用下拉菜单组件，用于渲染导航栏的下拉菜单\n *\n * @param props 下拉菜单组件的属性\n * @returns 下拉菜单Portal组件\n */ function DropdownMenu(param) {\n    let { items, navItem, isActive, dropdownName, onMouseEnter, onMouseLeave, locale, basePath, extraItems, closeMenu } = param;\n    _s();\n    // 添加客户端渲染标志\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 客户端挂载后设置标志\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        return ()=>setMounted(false);\n    }, []);\n    // 确保只在客户端渲染\n    if (!mounted) return null;\n    // 如果菜单项不存在或菜单不处于激活状态，则不渲染\n    if (!navItem || !isActive) return null;\n    // 获取导航项的位置和尺寸\n    const rect = navItem.getBoundingClientRect();\n    // 获取窗口尺寸\n    const windowWidth = window.innerWidth;\n    // 计算下拉菜单位置 - 确保不会超出视口右侧边缘\n    let left = rect.left;\n    const minWidth = 240;\n    if (left + minWidth > windowWidth) {\n        // 如果下拉菜单会超出右侧边缘，则右对齐\n        left = Math.max(0, rect.right - minWidth);\n    }\n    // 使用Portal渲染下拉菜单\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"dropdown-portal\",\n        style: {\n            position: \"fixed\",\n            top: \"\".concat(rect.bottom, \"px\"),\n            left: \"\".concat(left, \"px\"),\n            minWidth: \"\".concat(minWidth, \"px\"),\n            zIndex: 100000,\n            backgroundColor: \"white\",\n            borderRadius: \"4px\",\n            boxShadow: \"0 4px 20px rgba(0, 0, 0, 0.15)\",\n            padding: \"10px 0\",\n            animation: \"fadeInMenu 0.3s ease forwards\"\n        },\n        onMouseEnter: ()=>onMouseEnter(dropdownName),\n        onMouseLeave: onMouseLeave,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n            style: {\n                listStyle: \"none\",\n                margin: 0,\n                padding: 0\n            },\n            children: [\n                items.map((item)=>{\n                    var _item_translations_locale, _item_translations;\n                    // 使用item.url如果存在，否则使用传统的basePath/slug格式\n                    const href = item.url ? \"/\".concat(locale).concat(item.url) : \"/\".concat(locale, \"/\").concat(basePath, \"/\").concat(item.slug);\n                    // 获取翻译后的名称，优先使用translations，回退到name\n                    const displayName = ((_item_translations = item.translations) === null || _item_translations === void 0 ? void 0 : (_item_translations_locale = _item_translations[locale]) === null || _item_translations_locale === void 0 ? void 0 : _item_translations_locale.name) || item.name;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        style: {\n                            padding: \"5px 0\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: href,\n                            style: {\n                                display: \"block\",\n                                padding: \"8px 20px\",\n                                color: \"#333\",\n                                textDecoration: \"none\",\n                                fontSize: \"14px\",\n                                transition: \"background-color 0.2s ease, color 0.2s ease\"\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.backgroundColor = \"#f5f5f5\";\n                                e.currentTarget.style.color = \"#0a59f7\";\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.backgroundColor = \"transparent\";\n                                e.currentTarget.style.color = \"#333\";\n                            },\n                            onClick: closeMenu,\n                            children: displayName\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\DropdownMenu.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 15\n                        }, this)\n                    }, item._id, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\DropdownMenu.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 13\n                    }, this);\n                }),\n                extraItems\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\DropdownMenu.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\DropdownMenu.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this), document.body);\n}\n_s(DropdownMenu, \"LrrVfNW3d1raFE0BNzCTILYmIfo=\");\n_c = DropdownMenu;\nvar _c;\n$RefreshReg$(_c, \"DropdownMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/DropdownMenu.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/Header.tsx":
/*!***********************************!*\
  !*** ./app/components/Header.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _LanguageProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LanguageProvider */ \"(app-pages-browser)/./app/components/LanguageProvider.tsx\");\n/* harmony import */ var _utils_i18n__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/i18n */ \"(app-pages-browser)/./app/utils/i18n.ts\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _DropdownMenu__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DropdownMenu */ \"(app-pages-browser)/./app/components/DropdownMenu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// 是否为开发环境\nconst isDev = \"development\" === \"development\";\n/**\n * 网站顶部导航组件\n * 实现了响应式导航栏、下拉菜单和移动端菜单\n *\n * @returns 导航栏组件\n */ function Header() {\n    _s();\n    const { locale, changeLanguage, t, isHydrated } = (0,_LanguageProvider__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    const [mobileMenuActive, setMobileMenuActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSticky, setIsSticky] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastScrollTop, setLastScrollTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [headerVisible, setHeaderVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [languageDropdownOpen, setLanguageDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 添加导航数据状态\n    const [navigationData, setNavigationData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [productCategories, setProductCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [serviceCategories, setServiceCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [solutionCategories, setSolutionCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // 移动菜单子菜单状态\n    const [activeSubmenus, setActiveSubmenus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // 添加下拉菜单显示状态 - 默认为null\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 添加导航项引用\n    const productsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const servicesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const solutionsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 添加防抖定时器引用\n    const dropdownTimerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 添加语言选择器的引用\n    const languageSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    /**\n   * 处理导航项鼠标进入事件\n   * 清除任何现有的隐藏定时器并显示对应的下拉菜单\n   *\n   * @param dropdownName 要显示的下拉菜单名称\n   */ const handleMouseEnter = (dropdownName)=>{\n        // 清除之前的定时器\n        if (dropdownTimerRef.current) {\n            clearTimeout(dropdownTimerRef.current);\n            dropdownTimerRef.current = null;\n        }\n        // 立即设置活动下拉菜单\n        setActiveDropdown(dropdownName);\n    };\n    /**\n   * 处理导航项鼠标离开事件\n   * 设置定时器延迟隐藏下拉菜单，防止菜单闪烁\n   */ const handleMouseLeave = ()=>{\n        // 添加延时以防止菜单闪烁\n        dropdownTimerRef.current = setTimeout(()=>{\n            setActiveDropdown(null);\n        }, 150);\n    };\n    // 在组件卸载时清除定时器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        return ()=>{\n            if (dropdownTimerRef.current) {\n                clearTimeout(dropdownTimerRef.current);\n            }\n        };\n    }, []);\n    // 确保组件已挂载，避免水合错误\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n        // 确保初始状态下下拉菜单是关闭的 - 修复白色区块问题\n        setActiveDropdown(null);\n    }, []);\n    // 额外添加一个延迟初始化的useEffect，确保下拉菜单不会在初始加载时显示\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 页面加载后，立即将activeDropdown设为null\n        if (mounted) {\n            setActiveDropdown(null);\n        }\n    }, [\n        mounted\n    ]);\n    // 获取导航数据\n    const fetchNavigationData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (!mounted) return;\n        try {\n            // 添加缓存逻辑，避免频繁请求\n            const cacheKey = \"navigation_data_cache\";\n            const cacheTimeKey = \"navigation_data_cache_time\";\n            const cacheExpiry = 30 * 1000; // 30秒缓存，开发环境下更快更新\n            // 在开发环境下强制刷新，清除缓存\n            const isDev = \"development\" === \"development\";\n            if ((isDev || forceRefresh) && \"object\" !== \"undefined\") {\n                localStorage.removeItem(cacheKey);\n                localStorage.removeItem(cacheTimeKey);\n            }\n            if (!forceRefresh && !isDev && \"object\" !== \"undefined\") {\n                const cachedData = localStorage.getItem(cacheKey);\n                const cacheTime = localStorage.getItem(cacheTimeKey);\n                if (cachedData && cacheTime) {\n                    const timeDiff = Date.now() - parseInt(cacheTime);\n                    if (timeDiff < cacheExpiry) {\n                        // 使用缓存数据\n                        const data = JSON.parse(cachedData);\n                        setNavigationData(data);\n                        // 提取各类型的子菜单，只显示活跃的导航项\n                        const productsNav = data.find((nav)=>nav.slug === \"products\" && nav.isActive);\n                        const servicesNav = data.find((nav)=>nav.slug === \"services\" && nav.isActive);\n                        const solutionsNav = data.find((nav)=>nav.slug === \"solutions\" && nav.isActive);\n                        setProductCategories((productsNav === null || productsNav === void 0 ? void 0 : productsNav.subItems) || []);\n                        setServiceCategories((servicesNav === null || servicesNav === void 0 ? void 0 : servicesNav.subItems) || []);\n                        setSolutionCategories((solutionsNav === null || solutionsNav === void 0 ? void 0 : solutionsNav.subItems) || []);\n                        return;\n                    }\n                }\n            }\n            // 获取导航数据，添加时间戳强制刷新\n            const response = await fetch(\"/api/navigation?t=\".concat(Date.now()), {\n                cache: \"no-cache\",\n                headers: {\n                    \"Cache-Control\": \"no-cache, no-store, must-revalidate\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setNavigationData(data.data);\n                    // 提取各类型的子菜单，只显示活跃的导航项\n                    const productsNav = data.data.find((nav)=>nav.slug === \"products\" && nav.isActive);\n                    const servicesNav = data.data.find((nav)=>nav.slug === \"services\" && nav.isActive);\n                    const solutionsNav = data.data.find((nav)=>nav.slug === \"solutions\" && nav.isActive);\n                    setProductCategories((productsNav === null || productsNav === void 0 ? void 0 : productsNav.subItems) || []);\n                    setServiceCategories((servicesNav === null || servicesNav === void 0 ? void 0 : servicesNav.subItems) || []);\n                    setSolutionCategories((solutionsNav === null || solutionsNav === void 0 ? void 0 : solutionsNav.subItems) || []);\n                    // 更新缓存（开发环境下不缓存）\n                    if ( true && !isDev) {\n                        localStorage.setItem(cacheKey, JSON.stringify(data.data));\n                        localStorage.setItem(cacheTimeKey, Date.now().toString());\n                    }\n                }\n            }\n        } catch (error) {\n            console.error(\"Failed to fetch navigation data:\", error);\n        }\n    }, [\n        mounted\n    ]);\n    // 获取导航数据 - 初始加载和定期刷新\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 首次加载时获取数据\n        fetchNavigationData();\n        // 设置定时器每30秒刷新一次（开发环境下更频繁）\n        const refreshInterval = setInterval(()=>{\n            fetchNavigationData();\n        }, 30000); // 30秒刷新一次\n        // 清理函数\n        return ()=>clearInterval(refreshInterval);\n    }, [\n        fetchNavigationData\n    ]);\n    // 切换移动菜单\n    const toggleMobileMenu = ()=>{\n        setMobileMenuActive(!mobileMenuActive);\n        document.body.classList.toggle(\"menu-open\");\n    };\n    // 切换子菜单\n    const toggleSubmenu = (id)=>{\n        setActiveSubmenus((prev)=>({\n                ...prev,\n                [id]: !prev[id]\n            }));\n    };\n    // 监听滚动实现粘性头部\n    const handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (!mounted) return;\n        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n        // 滚动超过50像素时激活粘性导航（降低阈值使导航栏更快变为不透明）\n        if (scrollTop > 50) {\n            setIsSticky(true);\n            // 向下滚动时隐藏导航，向上滚动时显示导航\n            if (scrollTop > lastScrollTop + 10) {\n                // 添加一点阈值，避免小幅度滚动触发\n                setHeaderVisible(false);\n            } else if (scrollTop < lastScrollTop - 10) {\n                setHeaderVisible(true);\n            }\n        } else {\n            // 回到顶部时重置状态，变为透明\n            setIsSticky(false);\n            setHeaderVisible(true);\n        }\n        setLastScrollTop(scrollTop);\n    }, [\n        mounted,\n        lastScrollTop,\n        setIsSticky,\n        setHeaderVisible,\n        setLastScrollTop\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, [\n        mounted,\n        handleScroll\n    ]);\n    // 点击外部关闭移动菜单\n    const handleClickOutsideMobileMenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (!mounted) return;\n        const mobileMenu = document.querySelector(\".mobile-menu\");\n        const mobileMenuToggle = document.querySelector(\".mobile-menu-toggle\");\n        if (mobileMenuActive && mobileMenu && mobileMenuToggle) {\n            if (!mobileMenu.contains(e.target) && e.target !== mobileMenuToggle && !mobileMenuToggle.contains(e.target)) {\n                setMobileMenuActive(false);\n                document.body.classList.remove(\"menu-open\");\n            }\n        }\n    }, [\n        mounted,\n        mobileMenuActive,\n        setMobileMenuActive\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        document.addEventListener(\"click\", handleClickOutsideMobileMenu);\n        return ()=>document.removeEventListener(\"click\", handleClickOutsideMobileMenu);\n    }, [\n        mounted,\n        handleClickOutsideMobileMenu\n    ]);\n    // 点击外部关闭语言下拉菜单\n    const handleClickOutsideLanguageDropdown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (!mounted) return;\n        if (languageDropdownOpen && languageSelectorRef.current && !languageSelectorRef.current.contains(e.target)) {\n            setLanguageDropdownOpen(false);\n        }\n    }, [\n        mounted,\n        languageDropdownOpen,\n        languageSelectorRef,\n        setLanguageDropdownOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        document.addEventListener(\"click\", handleClickOutsideLanguageDropdown);\n        return ()=>document.removeEventListener(\"click\", handleClickOutsideLanguageDropdown);\n    }, [\n        mounted,\n        handleClickOutsideLanguageDropdown\n    ]);\n    // 计算语言切换下拉菜单的位置\n    const getLanguageDropdownPosition = ()=>{\n        if (!languageSelectorRef.current || !mounted) return {\n            top: 0,\n            left: 0\n        };\n        const rect = languageSelectorRef.current.getBoundingClientRect();\n        const windowWidth =  true ? window.innerWidth : 0;\n        // 计算下拉菜单位置\n        let left = rect.left;\n        const minWidth = 120;\n        // 防止菜单超出视口右侧\n        if (left + minWidth > windowWidth) {\n            left = Math.max(0, rect.right - minWidth);\n        }\n        return {\n            top: rect.bottom,\n            left\n        };\n    };\n    /**\n   * 为产品导航生成额外的菜单项\n   * 现在菜单项通过导航API提供，不需要额外项目\n   *\n   * @returns 额外的菜单项节点\n   */ const generateProductExtraItems = ()=>{\n        return null; // 产品菜单项现在通过导航API提供，避免重复\n    };\n    /**\n   * 为服务导航生成额外的菜单项\n   *\n   * @returns 额外的菜单项节点\n   */ const generateServiceExtraItems = ()=>{\n        return null; // 服务菜单没有额外项目\n    };\n    /**\n   * 为解决方案导航生成额外的菜单项\n   * 现在菜单项通过导航API提供，不需要额外项目\n   *\n   * @returns 额外的菜单项节点\n   */ const generateSolutionExtraItems = ()=>{\n        return null; // 解决方案菜单项现在通过导航API提供，避免重复\n    };\n    // 添加移动端菜单函数\n    // 修改移动菜单中的产品分类\n    const renderMobileProductCategories = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mobile-submenu \".concat(activeSubmenus[\"products\"] ? \"active\" : \"\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                children: productCategories.map((category)=>{\n                    var _category_translations_locale, _category_translations;\n                    const href = category.url ? \"/\".concat(locale).concat(category.url) : \"/\".concat(locale, \"/products/\").concat(category.slug);\n                    // 获取翻译后的名称，优先使用translations，回退到name\n                    const displayName = ((_category_translations = category.translations) === null || _category_translations === void 0 ? void 0 : (_category_translations_locale = _category_translations[locale]) === null || _category_translations_locale === void 0 ? void 0 : _category_translations_locale.name) || category.name;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"mobile-submenu-item\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: href,\n                            className: \"mobile-submenu-link\",\n                            onClick: ()=>setMobileMenuActive(false),\n                            children: displayName\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 17\n                        }, this)\n                    }, category._id, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                lineNumber: 385,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n            lineNumber: 384,\n            columnNumber: 7\n        }, this);\n    };\n    // 修改移动菜单中的服务分类\n    const renderMobileServiceCategories = ()=>{\n        if (serviceCategories.length === 0) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mobile-submenu \".concat(activeSubmenus[\"services\"] ? \"active\" : \"\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                children: serviceCategories.map((category)=>{\n                    var _category_translations_locale, _category_translations;\n                    const href = category.url ? \"/\".concat(locale).concat(category.url) : \"/\".concat(locale, \"/service/\").concat(category.slug);\n                    // 获取翻译后的名称，优先使用translations，回退到name\n                    const displayName = ((_category_translations = category.translations) === null || _category_translations === void 0 ? void 0 : (_category_translations_locale = _category_translations[locale]) === null || _category_translations_locale === void 0 ? void 0 : _category_translations_locale.name) || category.name;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"mobile-submenu-item\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: href,\n                            className: \"mobile-submenu-link\",\n                            onClick: ()=>setMobileMenuActive(false),\n                            children: displayName\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 17\n                        }, this)\n                    }, category._id, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                lineNumber: 413,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n            lineNumber: 412,\n            columnNumber: 7\n        }, this);\n    };\n    // 修改移动菜单中的解决方案分类\n    const renderMobileSolutionCategories = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mobile-submenu \".concat(activeSubmenus[\"solutions\"] ? \"active\" : \"\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                children: solutionCategories.map((category)=>{\n                    var _category_translations_locale, _category_translations;\n                    const href = category.url ? \"/\".concat(locale).concat(category.url) : \"/\".concat(locale, \"/products?category=\").concat(category.slug);\n                    // 获取翻译后的名称，优先使用translations，回退到name\n                    const displayName = ((_category_translations = category.translations) === null || _category_translations === void 0 ? void 0 : (_category_translations_locale = _category_translations[locale]) === null || _category_translations_locale === void 0 ? void 0 : _category_translations_locale.name) || category.name;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"mobile-submenu-item\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: href,\n                            className: \"mobile-submenu-link\",\n                            onClick: ()=>setMobileMenuActive(false),\n                            children: displayName\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 17\n                        }, this)\n                    }, category._id, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                lineNumber: 439,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n            lineNumber: 438,\n            columnNumber: 7\n        }, this);\n    };\n    // 如果未挂载，返回一个静态占位符以避免水合错误\n    if (!mounted || !isHydrated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n            className: \"header\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"header-main\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"logo\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-logo\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"logo-text\",\n                                        children: \"钓盛\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"logo-tag\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"nav-container\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mobile-menu-toggle\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                lineNumber: 465,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n            lineNumber: 464,\n            columnNumber: 7\n        }, this);\n    }\n    // 修改PC端主导航的渲染，使用通用的DropdownMenu组件\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"header\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"header-main \".concat(isSticky ? \"sticky\" : \"transparent\"),\n                style: {\n                    transform: isSticky ? headerVisible ? \"translateY(0)\" : \"translateY(-100%)\" : \"none\",\n                    transition: \"transform 0.3s ease, background-color 0.3s ease\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"logo\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\".concat(locale),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-logo\",\n                                    children: locale === \"zh\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"logo-text\",\n                                                children: \"钓盛\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"logo-tag\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"logo-text\",\n                                                children: \"JUNSHENG\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"logo-tag\",\n                                                children: \"TECH\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"nav-container\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"main-nav\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"nav-list\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"nav-item\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/\".concat(locale),\n                                                    className: \"nav-link\",\n                                                    children: t(\"common.home\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"nav-item has-dropdown\",\n                                                ref: productsRef,\n                                                onMouseEnter: ()=>handleMouseEnter(\"products\"),\n                                                onMouseLeave: handleMouseLeave,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/\".concat(locale, \"/products\"),\n                                                        className: \"nav-link\",\n                                                        children: [\n                                                            t(\"common.products\"),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-chevron-down \".concat(activeDropdown === \"products\" ? \"rotate-180\" : \"\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DropdownMenu__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        items: productCategories,\n                                                        navItem: productsRef.current,\n                                                        isActive: activeDropdown === \"products\",\n                                                        dropdownName: \"products\",\n                                                        onMouseEnter: handleMouseEnter,\n                                                        onMouseLeave: handleMouseLeave,\n                                                        locale: locale,\n                                                        basePath: \"products\",\n                                                        extraItems: generateProductExtraItems(),\n                                                        closeMenu: ()=>setMobileMenuActive(false)\n                                                    }, \"products-\".concat(locale), false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 17\n                                            }, this),\n                                            serviceCategories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"nav-item has-dropdown\",\n                                                ref: servicesRef,\n                                                onMouseEnter: ()=>handleMouseEnter(\"services\"),\n                                                onMouseLeave: handleMouseLeave,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/\".concat(locale, \"/pages/service\"),\n                                                        className: \"nav-link\",\n                                                        children: [\n                                                            t(\"common.services\"),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-chevron-down \".concat(activeDropdown === \"services\" ? \"rotate-180\" : \"\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DropdownMenu__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        items: serviceCategories,\n                                                        navItem: servicesRef.current,\n                                                        isActive: activeDropdown === \"services\",\n                                                        dropdownName: \"services\",\n                                                        onMouseEnter: handleMouseEnter,\n                                                        onMouseLeave: handleMouseLeave,\n                                                        locale: locale,\n                                                        basePath: \"service\",\n                                                        extraItems: generateServiceExtraItems(),\n                                                        closeMenu: ()=>setMobileMenuActive(false)\n                                                    }, \"services-\".concat(locale), false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"nav-item has-dropdown\",\n                                                ref: solutionsRef,\n                                                onMouseEnter: ()=>handleMouseEnter(\"solutions\"),\n                                                onMouseLeave: handleMouseLeave,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: \"/\".concat(locale, \"/pages/custom-solutions\"),\n                                                        className: \"nav-link\",\n                                                        children: [\n                                                            t(\"common.solutions\"),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-chevron-down \".concat(activeDropdown === \"solutions\" ? \"rotate-180\" : \"\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 583,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 581,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DropdownMenu__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        items: solutionCategories,\n                                                        navItem: solutionsRef.current,\n                                                        isActive: activeDropdown === \"solutions\",\n                                                        dropdownName: \"solutions\",\n                                                        onMouseEnter: handleMouseEnter,\n                                                        onMouseLeave: handleMouseLeave,\n                                                        locale: locale,\n                                                        basePath: \"products\",\n                                                        extraItems: generateSolutionExtraItems(),\n                                                        closeMenu: ()=>setMobileMenuActive(false)\n                                                    }, \"solutions-\".concat(locale), false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"nav-item\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/\".concat(locale, \"/pages/about-us\"),\n                                                    className: \"nav-link\",\n                                                    children: t(\"common.about\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"nav-item\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/\".concat(locale, \"/pages/contact-us\"),\n                                                    className: \"nav-link\",\n                                                    children: t(\"common.contact\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"header-actions\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"language-selector\",\n                                            ref: languageSelectorRef,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"current-language\",\n                                                    onClick: ()=>setLanguageDropdownOpen(!languageDropdownOpen),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-globe\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: locale.toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"fas fa-chevron-down \".concat(languageDropdownOpen ? \"rotate-180\" : \"\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 17\n                                                }, this),\n                                                languageDropdownOpen && mounted && /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_5__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"language-dropdown-portal\",\n                                                    style: {\n                                                        position: \"fixed\",\n                                                        top: \"\".concat(getLanguageDropdownPosition().top, \"px\"),\n                                                        left: \"\".concat(getLanguageDropdownPosition().left, \"px\"),\n                                                        zIndex: 100000,\n                                                        backgroundColor: \"white\",\n                                                        boxShadow: \"0 4px 20px rgba(0, 0, 0, 0.15)\",\n                                                        borderRadius: \"4px\",\n                                                        padding: \"5px 0\",\n                                                        minWidth: \"120px\",\n                                                        maxHeight: \"80vh\",\n                                                        overflowY: \"auto\",\n                                                        display: \"block\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"language-list\",\n                                                        style: {\n                                                            listStyle: \"none\",\n                                                            margin: 0,\n                                                            padding: 0\n                                                        },\n                                                        children: _utils_i18n__WEBPACK_IMPORTED_MODULE_4__.i18n.locales.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    padding: \"5px 0\",\n                                                                    backgroundColor: locale === lang ? \"#f0f4f8\" : \"transparent\"\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        changeLanguage(lang);\n                                                                        setLanguageDropdownOpen(false);\n                                                                    },\n                                                                    style: {\n                                                                        display: \"block\",\n                                                                        width: \"100%\",\n                                                                        padding: \"8px 15px\",\n                                                                        fontSize: \"14px\",\n                                                                        textAlign: \"left\",\n                                                                        border: \"none\",\n                                                                        background: \"none\",\n                                                                        cursor: \"pointer\",\n                                                                        fontWeight: locale === lang ? \"bold\" : \"normal\",\n                                                                        color: locale === lang ? \"#1a65e3\" : \"#333\"\n                                                                    },\n                                                                    children: lang === \"en\" ? \"English\" : lang === \"zh\" ? \"中文\" : lang\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, lang, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 27\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 21\n                                                }, this), document.body)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"quote-button\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\".concat(locale, \"/pages/contact-us\"),\n                                                className: \"btn-quote\",\n                                                children: t(\"home.ctaButton\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 684,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mobile-menu-toggle\",\n                            onClick: toggleMobileMenu,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: mobileMenuActive ? \"active\" : \"\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 692,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: mobileMenuActive ? \"active\" : \"\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: mobileMenuActive ? \"active\" : \"\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 694,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 691,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                    lineNumber: 495,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                lineNumber: 488,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mobile-menu \".concat(mobileMenuActive ? \"active\" : \"\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"mobile-nav\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"mobile-nav-list\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"mobile-nav-item\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\".concat(locale),\n                                        className: \"mobile-nav-link\",\n                                        children: t(\"common.home\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"mobile-nav-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mobile-nav-link\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/\".concat(locale, \"/products\"),\n                                                    className: \"mobile-link-text\",\n                                                    children: t(\"common.products\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mobile-dropdown-toggle \".concat(activeSubmenus[\"products\"] ? \"active\" : \"\"),\n                                                    onClick: ()=>toggleSubmenu(\"products\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-chevron-down\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 718,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 714,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 17\n                                        }, this),\n                                        renderMobileProductCategories()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 15\n                                }, this),\n                                serviceCategories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"mobile-nav-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mobile-nav-link\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/\".concat(locale, \"/pages/service\"),\n                                                    className: \"mobile-link-text\",\n                                                    children: t(\"common.services\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mobile-dropdown-toggle \".concat(activeSubmenus[\"services\"] ? \"active\" : \"\"),\n                                                    onClick: ()=>toggleSubmenu(\"services\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-chevron-down\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 729,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 725,\n                                            columnNumber: 19\n                                        }, this),\n                                        renderMobileServiceCategories()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 724,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"mobile-nav-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mobile-nav-link\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/\".concat(locale, \"/pages/custom-solutions\"),\n                                                    className: \"mobile-link-text\",\n                                                    children: t(\"common.solutions\")\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mobile-dropdown-toggle \".concat(activeSubmenus[\"solutions\"] ? \"active\" : \"\"),\n                                                    onClick: ()=>toggleSubmenu(\"solutions\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-chevron-down\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 748,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 744,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 17\n                                        }, this),\n                                        renderMobileSolutionCategories()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"mobile-nav-item\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\".concat(locale, \"/pages/about-us\"),\n                                        className: \"mobile-nav-link\",\n                                        children: t(\"common.about\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"mobile-nav-item\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\".concat(locale, \"/pages/contact-us\"),\n                                        className: \"mobile-nav-link\",\n                                        children: t(\"common.contact\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                        lineNumber: 759,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                                    lineNumber: 758,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                            lineNumber: 703,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                        lineNumber: 702,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                    lineNumber: 701,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n                lineNumber: 700,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Header.tsx\",\n        lineNumber: 487,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"qooObac4FEqu77yLCtCtsEPQQuU=\", false, function() {\n    return [\n        _LanguageProvider__WEBPACK_IMPORTED_MODULE_3__.useLanguage\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/Header.tsx\n"));

/***/ })

}]);