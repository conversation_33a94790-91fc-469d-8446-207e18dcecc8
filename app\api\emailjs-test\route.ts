import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 开始EmailJS邮件测试...');
    
    // 检查EmailJS配置
    const emailjsConfig = {
      publicKey: process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY,
      privateKey: process.env.NEXT_PUBLIC_EMAILJS_PRIVATE_KEY,
      serviceId: process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID,
      templateId: process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID
    };

    console.log('EmailJS配置检查:', {
      hasPublicKey: !!emailjsConfig.publicKey,
      hasPrivateKey: !!emailjsConfig.privateKey,
      hasServiceId: !!emailjsConfig.serviceId,
      hasTemplateId: !!emailjsConfig.templateId
    });

    if (!emailjsConfig.publicKey) {
      return NextResponse.json({
        success: false,
        message: 'EmailJS配置不完整',
        missing: 'NEXT_PUBLIC_EMAILJS_PUBLIC_KEY',
        guide: '请在.env.local中设置EmailJS配置'
      }, { status: 400 });
    }

    // 测试数据
    const testData = {
      id: 8888,
      name: "陈总",
      email: "<EMAIL>",
      phone: "+86 138-8888-8888",
      country: "中国",
      playground_size: "5000+ sqm",
      product: "全套AR互动娱乐解决方案",
      message: "您好，我们是一家大型商业地产开发商，目前正在建设一个10万平方米的大型商业综合体，其中包含3000平方米的儿童娱乐区域。我们对贵公司的AR互动娱乐解决方案非常感兴趣，希望能够了解：1. 完整的产品组合和技术方案 2. 大型项目的定制化服务 3. 投资预算和回报分析 4. 项目实施时间表和售后服务。我们的预算在1000万元以上，希望能够打造国内领先的沉浸式娱乐体验。",
      created_at: new Date().toISOString()
    };

    const targetEmail = '<EMAIL>';

    // 使用EmailJS发送邮件
    try {
      // 使用fetch调用EmailJS API
      const emailjsResponse = await fetch('https://api.emailjs.com/api/v1.0/email/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          service_id: emailjsConfig.serviceId,
          template_id: emailjsConfig.templateId,
          user_id: emailjsConfig.publicKey,
          accessToken: emailjsConfig.privateKey,
          template_params: {
            to_email: targetEmail,
            from_name: '跨境电商网站',
            subject: `🔔 重要客户咨询 - ${testData.name}`,
            customer_name: testData.name,
            customer_email: testData.email,
            customer_phone: testData.phone,
            customer_country: testData.country,
            playground_size: testData.playground_size,
            product_interest: testData.product,
            customer_message: testData.message,
            submission_time: new Date(testData.created_at).toLocaleString('zh-CN'),
            submission_id: testData.id,
            // 邮件内容
            email_content: `
新的表单提交通知

客户信息:
- 姓名: ${testData.name}
- 邮箱: ${testData.email}
- 电话: ${testData.phone}
- 国家: ${testData.country}
- 场地大小: ${testData.playground_size}
- 感兴趣的产品: ${testData.product}

客户留言:
${testData.message}

提交时间: ${new Date(testData.created_at).toLocaleString('zh-CN')}
提交ID: #${testData.id}

此邮件由跨境电商网站系统通过 EmailJS 服务自动发送
            `
          }
        })
      });

      console.log('EmailJS API响应状态:', emailjsResponse.status);

      if (emailjsResponse.ok) {
        const result = await emailjsResponse.text();
        console.log('✅ EmailJS发送成功:', result);

        return NextResponse.json({
          success: true,
          message: 'EmailJS邮件发送成功！',
          service: 'EmailJS',
          sentTo: targetEmail,
          messageId: result,
          customerInfo: {
            name: testData.name,
            phone: testData.phone,
            email: testData.email,
            product: testData.product
          },
          timestamp: new Date().toISOString()
        });
      } else {
        const errorText = await emailjsResponse.text();
        console.error('❌ EmailJS发送失败:', errorText);
        throw new Error(`EmailJS API错误: ${emailjsResponse.status} - ${errorText}`);
      }

    } catch (emailjsError) {
      console.error('❌ EmailJS发送异常:', emailjsError);
      
      // 备用方案：记录到控制台
      console.log('\n' + '='.repeat(80));
      console.log('📧 EmailJS发送失败，记录到控制台');
      console.log('='.repeat(80));
      console.log(`🎯 目标收件人: ${targetEmail}`);
      console.log(`📱 客户电话: ${testData.phone}`);
      console.log(`📧 客户邮箱: ${testData.email}`);
      console.log('');
      console.log('🚨 重要客户咨询 - 需要立即处理');
      console.log('');
      console.log('👤 客户详情:');
      console.log(`   姓名: ${testData.name}`);
      console.log(`   邮箱: ${testData.email}`);
      console.log(`   电话: ${testData.phone}`);
      console.log(`   国家: ${testData.country}`);
      console.log(`   场地规模: ${testData.playground_size}`);
      console.log(`   产品需求: ${testData.product}`);
      console.log('');
      console.log('💬 客户留言:');
      console.log(`   ${testData.message}`);
      console.log('');
      console.log('🎯 立即行动建议:');
      console.log(`   1. 电话联系客户: ${testData.phone}`);
      console.log(`   2. 邮件回复客户: ${testData.email}`);
      console.log(`   3. 发送邮件到: ${targetEmail}`);
      console.log('');
      console.log(`📅 提交时间: ${new Date(testData.created_at).toLocaleString('zh-CN')}`);
      console.log(`🆔 咨询ID: #${testData.id}`);
      console.log('='.repeat(80));

      return NextResponse.json({
        success: false,
        message: 'EmailJS发送失败，但已记录到控制台',
        error: emailjsError instanceof Error ? emailjsError.message : 'Unknown error',
        fallback: 'console_log',
        customerInfo: {
          name: testData.name,
          phone: testData.phone,
          email: testData.email,
          product: testData.product
        },
        targetEmail: targetEmail,
        suggestion: '请检查EmailJS配置或直接联系客户'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ EmailJS测试API错误:', error);
    return NextResponse.json({
      success: false,
      message: 'EmailJS测试API发生错误',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const emailjsConfig = {
    hasPublicKey: !!process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY,
    hasPrivateKey: !!process.env.NEXT_PUBLIC_EMAILJS_PRIVATE_KEY,
    hasServiceId: !!process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID,
    hasTemplateId: !!process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID
  };

  return NextResponse.json({
    message: 'EmailJS邮件测试API',
    endpoint: '/api/emailjs-test',
    method: 'POST',
    service: 'EmailJS',
    freeLimit: '200封/月',
    targetEmail: '<EMAIL>',
    configuration: emailjsConfig,
    advantages: [
      '可发送到任何邮箱地址',
      '无需后端SMTP配置',
      '前端直接调用',
      '免费额度充足'
    ],
    setup: {
      step1: '访问 https://www.emailjs.com 注册账号',
      step2: '创建邮件服务 (Gmail/Outlook)',
      step3: '创建邮件模板',
      step4: '获取API密钥',
      step5: '配置环境变量'
    }
  });
}
