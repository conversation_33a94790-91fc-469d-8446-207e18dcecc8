'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { useLanguage } from '../../../components/LanguageProvider';
import PageHeader from '../../../components/PageHeader';
import SeriesGallery from '../../../components/SeriesGallery';
import { getDictionary } from '../../../utils/i18n';

// 板块数据接口
interface SectionData {
  slug: string;
  title: string;
  description: string;
  category: string;
  images: string[];
  products: Array<{
    id: string;
    name: string;
    image: string;
    slug: string;
    description: string;
  }>;
}

// 板块数据配置
const sectionsData: Record<string, SectionData> = {
  'interactive-projection': {
    slug: 'interactive-projection',
    title: '互动投影系列',
    description: '创新的互动投影技术，打造沉浸式体验空间，让用户与数字内容进行自然交互',
    category: 'interactive',
    images: [
      '/images/product-sections/0007.jpg',
      '/images/products/interactive-ball/interactive-ball-1.jpg',
      '/images/products/interactive-ball/interactive-ball-2.jpg',
      '/images/products/interactive-ball/interactive-ball-3.jpg',
      '/images/products/interactive-ball/interactive-ball-5.jpg',
      '/images/products/interactive-ball/interactive-ball-6.jpg',
      '/images/products/interactive-football-1.jpg'
    ],
    products: [
      {
        id: '1',
        name: '互动砸球系统',
        image: '/images/products/interactive-ball/interactive-ball-1.jpg',
        slug: 'interactive-ball-system',
        description: '创新的互动砸球游戏系统，结合投影技术和体感识别'
      },
      {
        id: '2',
        name: '互动足球游戏',
        image: '/images/products/interactive-football-1.jpg',
        slug: 'interactive-football',
        description: '沉浸式足球互动体验，让运动更加有趣'
      },
      {
        id: '3',
        name: '蹦床互动系统',
        image: '/images/products/trampoline-1.jpg',
        slug: 'trampoline-interactive',
        description: '结合蹦床运动的互动投影娱乐系统'
      }
    ]
  },
  'interactive-projection-series': {
    slug: 'interactive-projection-series',
    title: '互动投影系列',
    description: '创新的互动投影技术，打造沉浸式体验空间，让用户与数字内容进行自然交互',
    category: 'interactive',
    images: [
      '/images/product-sections/0007.jpg',
      '/images/products/interactive-ball/interactive-ball-1.jpg',
      '/images/products/interactive-ball/interactive-ball-2.jpg',
      '/images/products/interactive-ball/interactive-ball-3.jpg',
      '/images/products/interactive-ball/interactive-ball-5.jpg',
      '/images/products/interactive-ball/interactive-ball-6.jpg',
      '/images/products/interactive-football-1.jpg'
    ],
    products: [
      {
        id: '1',
        name: '互动砸球系统',
        image: '/images/products/interactive-ball/interactive-ball-1.jpg',
        slug: 'interactive-ball-system',
        description: '创新的互动砸球游戏系统，结合投影技术和体感识别'
      },
      {
        id: '2',
        name: '互动足球游戏',
        image: '/images/products/interactive-football-1.jpg',
        slug: 'interactive-football',
        description: '沉浸式足球互动体验，让运动更加有趣'
      },
      {
        id: '3',
        name: '蹦床互动系统',
        image: '/images/products/trampoline-1.jpg',
        slug: 'trampoline-interactive',
        description: '结合蹦床运动的互动投影娱乐系统'
      }
    ]
  },
  'holographic-display': {
    slug: 'holographic-display',
    title: '全息展示系列',
    description: '先进的全息投影技术，呈现震撼视觉效果，创造前所未有的视觉体验',
    category: 'holographic',
    images: [
      '/images/product-sections/0012.jpg',
      '/images/products/hologram-dining-1.jpg',
      '/images/products/hologram-dining-2.jpg',
      '/images/products/hologram-stage-1.jpg',
      '/images/products/hologram-stage-2.jpg',
      '/images/products/产品介绍模板_01.jpg',
      '/images/products/产品介绍模板_02.jpg'
    ],
    products: [
      {
        id: '2',
        name: '全息沙盘',
        image: '/images/products/hologram-dining-1.jpg',
        slug: 'holographic-sand-table',
        description: '全息投影沙盘展示系统，实现立体化信息展示'
      },
      {
        id: '3',
        name: '全息舞台',
        image: '/images/products/hologram-stage-1.jpg',
        slug: 'holographic-stage',
        description: '震撼的全息舞台表演系统，打造视觉盛宴'
      },
      {
        id: '4',
        name: '全息餐厅',
        image: '/images/products/hologram-dining-2.jpg',
        slug: 'holographic-dining',
        description: '沉浸式全息餐厅体验，重新定义用餐环境'
      }
    ]
  },
  'digital-sandbox': {
    slug: 'digital-sandbox',
    title: '数字沙盘系列',
    description: '智能数字沙盘，实现精准展示与互动控制，为规划展示提供创新解决方案',
    category: 'digital',
    images: [
      '/images/product-sections/0021.jpg',
      '/images/products/3d-1.jpg',
      '/images/products/3d-2.jpg',
      '/images/products/3d-3.jpg',
      '/images/products/3d-4.jpg',
      '/images/products/3d-5.jpg',
      '/images/products/3d-6.jpg',
      '/images/products/3d-sandbox-1.jpg'
    ],
    products: [
      {
        id: '5',
        name: '智能规划沙盘',
        image: '/images/products/3d-2.jpg',
        slug: '3d-planning',
        description: '智能化城市规划展示沙盘，支持实时数据更新'
      },
      {
        id: '6',
        name: '互动数字沙盘',
        image: '/images/products/3d-sandbox-1.jpg',
        slug: 'interactive-sandbox',
        description: '支持多点触控的互动数字沙盘系统'
      }
    ]
  },
  'ar-reality': {
    slug: 'ar-reality',
    title: 'AR增强现实系列',
    description: 'AR增强现实技术，融合虚拟与现实世界，创造全新的交互体验',
    category: 'ar',
    images: [
      '/images/product-sections/0035.jpg',
      '/images/products/ar-1.jpg',
      '/images/products/ar-2.jpg',
      '/images/products/ar-3.jpg',
      '/images/products/ar-4.jpg',
      '/images/products/ar-education-1.jpg',
      '/images/products/ar-education-2.jpg',
      '/images/products/ar-education-3.jpg'
    ],
    products: [
      {
        id: '4',
        name: 'AR教育系统',
        image: '/images/products/ar-1.jpg',
        slug: 'ar',
        description: 'AR增强现实教育系统，提供沉浸式学习体验'
      },
      {
        id: '7',
        name: 'AR展示系统',
        image: '/images/products/ar-education-1.jpg',
        slug: 'ar-display',
        description: '专业AR展示解决方案，适用于展览展示'
      },
      {
        id: '8',
        name: 'AR互动体验',
        image: '/images/products/ar-education-2.jpg',
        slug: 'ar-interactive',
        description: '创新AR互动体验系统，打造未来科技感'
      }
    ]
  },
  'smart-integrated': {
    slug: 'smart-integrated',
    title: '智能一体机系列',
    description: '集成化智能设备，提供完整解决方案，满足多样化应用需求',
    category: 'smart',
    images: [
      '/images/product-sections/0046.jpg',
      '/images/products/ktv-1.jpg',
      '/images/products/ktv-2.jpg',
      '/images/products/ktv-3.jpg',
      '/images/products/ktv-4.jpg',
      '/images/products/ktv-5.jpg',
      '/images/products/ktv-6.jpg',
      '/images/products/-1.jpg'
    ],
    products: [
      {
        id: '5',
        name: 'KTV智能系统',
        image: '/images/products/ktv-1.jpg',
        slug: 'ktv',
        description: 'KTV智能娱乐系统，提供全方位娱乐体验'
      },
      {
        id: '9',
        name: '智能会议系统',
        image: '/images/products/ktv-2.jpg',
        slug: 'smart-meeting',
        description: '高效智能会议解决方案，提升办公效率'
      },
      {
        id: '10',
        name: '多媒体一体机',
        image: '/images/products/-1.jpg',
        slug: 'multimedia-system',
        description: '集成多媒体功能的智能一体机设备'
      }
    ]
  }
};

export default function SectionPage() {
  const params = useParams();
  const router = useRouter();
  const { locale } = useLanguage();
  const [sectionData, setSectionData] = useState<SectionData | null>(null);
  const [dict, setDict] = useState<any>({});

  // 加载字典
  useEffect(() => {
    const loadDictionary = async () => {
      try {
        const dictionary = await getDictionary(locale);
        setDict(dictionary);
      } catch (error) {
        console.error('Failed to load dictionary:', error);
        // 设置默认字典
        setDict({
          common: {
            home: locale === 'zh' ? '首页' : 'Home',
            products: locale === 'zh' ? '产品中心' : 'Products',
            related_products: locale === 'zh' ? '相关产品' : 'Related Products',
            product_details: locale === 'zh' ? '产品详情' : 'Product Details',
            view_more_products: locale === 'zh' ? '查看更多产品' : 'View More Products',
            explore_complete_series: locale === 'zh' ? '探索我们的完整产品系列' : 'Explore our complete product series',
            browse_all: locale === 'zh' ? '浏览全部' : 'Browse All',
            back_to_products: locale === 'zh' ? '返回产品中心' : 'Back to Products'
          }
        });
      }
    };

    loadDictionary();
  }, [locale]);

  useEffect(() => {
    if (!params) {
      router.push(`/${locale}/products`);
      return;
    }

    const slug = params.slug as string;
    const data = sectionsData[slug];

    if (data) {
      setSectionData(data);
    } else {
      // 如果找不到对应的板块，重定向到产品页面
      router.push(`/${locale}/products`);
    }
  }, [params?.slug, router, locale]);

  if (!sectionData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="section-page">
      {/* 页面头部带面包屑导航 */}
      <div className="relative">
        <PageHeader
          title=""
          subtitle=""
          bgImage={sectionData.images[0]}
          height="md:h-[400px] h-[300px]"
          overlayOpacity={0.7}
          useImageComponent={true}
          animationEffect="up"
          className="mb-16"
        />

        {/* 面包屑导航在标题下方 */}
        <div className="absolute inset-0 flex items-center justify-center z-20">
          <div className="container mx-auto px-4 text-center">
            {/* 标题区域 */}
            <div className="mb-6">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 animate-fade-in-up">
                {sectionData.title}
              </h1>
              {sectionData.description && (
                <p className="text-lg md:text-xl text-gray-200 max-w-3xl mx-auto mb-6 animate-fade-in-up-delay">
                  {sectionData.description}
                </p>
              )}
            </div>

            {/* 面包屑导航 */}
            <nav className="breadcrumbs-overlay flex items-center justify-center space-x-2 text-sm animate-slide-in-left">
              <Link
                href={`/${locale}`}
                className="text-white hover:text-white transition-all duration-200 px-3 py-2 rounded-lg bg-black/30 backdrop-blur-sm border border-white/20 hover:bg-black/50 hover:border-white/30 hover:-translate-y-0.5 font-medium text-shadow-lg"
              >
                {dict.common?.home || (locale === 'zh' ? '首页' : 'Home')}
              </Link>
              <span className="text-white/80 mx-2 text-base font-bold text-shadow-lg">/</span>
              <Link
                href={`/${locale}/products`}
                className="text-white hover:text-white transition-all duration-200 px-3 py-2 rounded-lg bg-black/30 backdrop-blur-sm border border-white/20 hover:bg-black/50 hover:border-white/30 hover:-translate-y-0.5 font-medium text-shadow-lg"
              >
                {dict.common?.products || (locale === 'zh' ? '产品中心' : 'Products')}
              </Link>
              <span className="text-white/80 mx-2 text-base font-bold text-shadow-lg">/</span>
              <span className="text-white font-semibold px-3 py-2 rounded-lg bg-white/15 backdrop-blur-sm border border-white/30 text-shadow-lg">
                {sectionData.title}
              </span>
            </nav>
          </div>
        </div>
      </div>

      {/* 系列展示区 */}
      <div className="container mx-auto px-4 mb-16">
        <SeriesGallery sectionSlug={sectionData.slug} />
      </div>

      {/* 相关产品展示 */}
      {sectionData.products.length > 0 && (
        <div className="container mx-auto px-4 mb-16">
          <div className="related-products">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              {dict.common?.related_products || (locale === 'zh' ? '相关产品' : 'Related Products')}
            </h2>

            <div className="products-horizontal-scroll">
              <div className="flex gap-6 overflow-x-auto pb-4 scrollbar-hide">
                {sectionData.products.map((product) => (
                  <Link
                    key={product.id}
                    href={`/${locale}/products`}
                    className="product-mini-card group flex-shrink-0"
                  >
                    <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 group-hover:shadow-lg group-hover:-translate-y-1 w-72">
                      <div className="relative w-full h-40">
                        <Image
                          src={product.image}
                          alt={product.name}
                          fill
                          style={{ objectFit: 'cover' }}
                          className="transition-transform duration-300 group-hover:scale-105"
                        />
                        <div className="absolute top-3 left-3">
                          <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                            {sectionData.category}
                          </span>
                        </div>
                      </div>
                      <div className="p-4">
                        <h3 className="text-base font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors line-clamp-1">
                          {product.name}
                        </h3>
                        <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                          {product.description}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                            {dict.common?.product_details || (locale === 'zh' ? '产品详情' : 'Product Details')}
                          </span>
                          <i className="fas fa-arrow-right text-blue-500 text-sm transition-transform group-hover:translate-x-1"></i>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}

                {/* 查看更多卡片 */}
                <Link
                  href={`/${locale}/products`}
                  className="view-more-card group flex-shrink-0"
                >
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border-2 border-dashed border-blue-200 overflow-hidden transition-all duration-300 group-hover:border-blue-400 group-hover:shadow-lg w-72 h-full flex items-center justify-center min-h-[240px]">
                    <div className="text-center p-6">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-200 transition-colors">
                        <i className="fas fa-plus text-blue-600 text-lg"></i>
                      </div>
                      <h3 className="text-base font-semibold text-gray-700 mb-2">
                        {dict.common?.view_more_products || (locale === 'zh' ? '查看更多产品' : 'View More Products')}
                      </h3>
                      <p className="text-sm text-gray-500 mb-3">
                        {dict.common?.explore_complete_series || (locale === 'zh' ? '探索我们的完整产品系列' : 'Explore our complete product series')}
                      </p>
                      <div className="flex items-center justify-center text-blue-600 text-sm font-medium">
                        <span>{dict.common?.browse_all || (locale === 'zh' ? '浏览全部' : 'Browse All')}</span>
                        <i className="fas fa-arrow-right ml-2 transition-transform group-hover:translate-x-1"></i>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 返回按钮 */}
      <div className="container mx-auto px-4 mb-16 text-center">
        <Link
          href={`/${locale}/products`}
          className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <i className="fas fa-arrow-left mr-2"></i>
          {dict.common?.back_to_products || (locale === 'zh' ? '返回产品中心' : 'Back to Products')}
        </Link>
      </div>

      <style jsx>{`
        .section-gallery {
          max-width: 1000px;
          margin: 0 auto;
        }

        .main-image-container {
          max-width: 800px;
          margin: 0 auto;
        }

        .thumbnails-grid {
          max-width: 600px;
          margin: 0 auto;
        }

        .line-clamp-1 {
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }

        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }

        .products-horizontal-scroll {
          position: relative;
        }

        .products-horizontal-scroll::before,
        .products-horizontal-scroll::after {
          content: '';
          position: absolute;
          top: 0;
          bottom: 0;
          width: 20px;
          z-index: 10;
          pointer-events: none;
        }

        .products-horizontal-scroll::before {
          left: 0;
          background: linear-gradient(to right, rgba(255,255,255,1), rgba(255,255,255,0));
        }

        .products-horizontal-scroll::after {
          right: 0;
          background: linear-gradient(to left, rgba(255,255,255,1), rgba(255,255,255,0));
        }

        .product-mini-card:hover .bg-blue-100 {
          background-color: rgb(219 234 254);
        }

        /* 面包屑导航动画和样式 */
        .animate-slide-in-left {
          animation: slideInLeft 1s ease-out 0.5s both;
        }

        .animate-fade-in-up {
          animation: fadeInUp 0.8s ease-out;
        }

        .animate-fade-in-up-delay {
          animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        @keyframes slideInLeft {
          from {
            opacity: 0;
            transform: translateX(-30px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }

        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .text-shadow-lg {
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8), 0 0 8px rgba(0, 0, 0, 0.5);
        }

        .breadcrumbs-overlay a {
          backdrop-filter: blur(8px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .breadcrumbs-overlay a:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
          transform: translateY(-2px);
        }

        .breadcrumbs-overlay span:last-child {
          backdrop-filter: blur(8px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
          .products-horizontal-scroll .flex {
            padding-left: 1rem;
            padding-right: 1rem;
          }

          .product-mini-card .w-72 {
            width: 16rem;
          }

          .breadcrumbs-overlay {
            font-size: 0.75rem;
          }

          .breadcrumbs-overlay a,
          .breadcrumbs-overlay span {
            padding: 0.375rem 0.5rem;
          }
        }
      `}</style>
    </div>
  );
}
