/* 统一CTA区域样式 */

/* 基础CTA区域样式 - 统一所有CTA区域 */
.cta-section,
.contact-cta,
.premium-cta {
  background-color: var(--primary-color, #0a59f7) !important;
  color: white !important;
  padding: 5rem 0 !important;
  text-align: center !important;
  position: relative !important;
  overflow: hidden !important;
}

/* CTA粒子背景效果 - 统一应用到所有CTA区域 */
.cta-section::before,
.contact-cta::before,
.premium-cta::before,
.cta-particles {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.12) 0%, transparent 25%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.12) 0%, transparent 25%) !important;
  z-index: 0 !important;
  pointer-events: none !important;
}

/* 确保内容在粒子效果之上 */
.cta-section .container,
.contact-cta .container,
.premium-cta .container {
  position: relative !important;
  z-index: 1 !important;
}

/* CTA内容区域 */
.cta-section .cta-content,
.contact-cta .cta-content,
.premium-cta .cta-content {
  max-width: 700px !important;
  margin: 0 auto !important;
  text-align: center !important;
  position: relative !important;
  z-index: 1 !important;
}

/* CTA标题样式 */
.cta-section h2,
.contact-cta h2,
.premium-cta h2,
.cta-section .cta-content h2,
.contact-cta .cta-content h2,
.premium-cta .cta-content h2 {
  font-size: 2.5rem !important;
  font-weight: 600 !important;
  color: white !important;
  margin-bottom: 1.2rem !important;
  position: relative !important;
  z-index: 1 !important;
  text-shadow: none !important;
}

/* CTA描述文字样式 */
.cta-section p,
.contact-cta p,
.premium-cta p,
.cta-section .cta-content p,
.contact-cta .cta-content p,
.premium-cta .cta-content p {
  font-size: 1.2rem !important;
  line-height: 1.6 !important;
  color: rgba(255, 255, 255, 0.9) !important;
  margin-bottom: 2.5rem !important;
  max-width: 700px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  position: relative !important;
  z-index: 1 !important;
}

/* CTA按钮样式 - 动画按钮效果 - 最高优先级 */
.btn-primary[data-text],
.cta-section .btn-primary,
.contact-cta .btn-primary,
.premium-cta .btn-primary,
.cta-section .btn-glow,
.contact-cta .btn-glow,
.premium-cta .btn-glow,
a.btn-primary[data-text] {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 50px !important;
  position: relative !important;
  padding: 0 20px !important;
  font-size: 18px !important;
  text-transform: uppercase !important;
  border: 0 !important;
  box-shadow: hsl(210deg 87% 36%) 0px 7px 0px 0px !important;
  background-color: hsl(210deg 100% 44%) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  transition: 31ms cubic-bezier(.5, .7, .4, 1) !important;
  text-decoration: none !important;
  color: white !important;
  font-weight: bold !important;
  letter-spacing: 4px !important;
  z-index: 1 !important;
  cursor: pointer !important;
  margin: 0 auto !important;
  width: auto !important;
  min-width: 200px !important;
}

/* 按钮默认文字 - 最高优先级 */
.btn-primary[data-text]:before,
.cta-section .btn-primary:before,
.contact-cta .btn-primary:before,
.premium-cta .btn-primary:before,
.cta-section .btn-glow:before,
.contact-cta .btn-glow:before,
.premium-cta .btn-glow:before,
a.btn-primary[data-text]:before {
  content: attr(data-text) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: absolute !important;
  inset: 0 !important;
  font-size: 15px !important;
  font-weight: bold !important;
  color: white !important;
  letter-spacing: 2px !important;
  opacity: 1 !important;
  transition: all 0.3s ease !important;
  transform: translateY(0) !important; /* 默认位置 */
  z-index: 2 !important; /* 确保在字母之上 */
}

/* 按钮点击效果 - 最高优先级 */
.btn-primary[data-text]:active,
.cta-section .btn-primary:active,
.contact-cta .btn-primary:active,
.premium-cta .btn-primary:active,
.cta-section .btn-glow:active,
.contact-cta .btn-glow:active,
.premium-cta .btn-glow:active,
a.btn-primary[data-text]:active {
  box-shadow: none !important;
  transform: translateY(7px) !important;
  transition: 35ms cubic-bezier(.5, .7, .4, 1) !important;
}

/* 悬停时隐藏默认文字 - 最高优先级 */
.btn-primary[data-text]:hover:before,
.cta-section .btn-primary:hover:before,
.contact-cta .btn-primary:hover:before,
.premium-cta .btn-primary:hover:before,
.cta-section .btn-glow:hover:before,
.contact-cta .btn-glow:hover:before,
.premium-cta .btn-glow:hover:before,
a.btn-primary[data-text]:hover:before {
  transition: all 0.2s ease !important;
  transform: translateY(-100%) !important; /* 向上滑出 */
  opacity: 0 !important;
}

/* 动画字母样式 - 默认隐藏 - 最高优先级 */
.btn-primary[data-text] i,
.cta-section .btn-primary i,
.contact-cta .btn-primary i,
.premium-cta .btn-primary i,
.cta-section .btn-glow i,
.contact-cta .btn-glow i,
.premium-cta .btn-glow i,
a.btn-primary[data-text] i {
  color: white !important;
  font-size: 15px !important;
  font-weight: bold !important;
  letter-spacing: 0px !important; /* 默认无字母间距 */
  font-style: normal !important;
  transition: all 0.3s ease !important;
  transform: translateY(20px) !important; /* 从下方出现 */
  opacity: 0 !important; /* 默认完全隐藏 */
  display: inline-block !important;
  position: relative !important;
  z-index: 3 !important; /* 确保在默认文字之上 */
}

/* 悬停时显示动画字母 - 最高优先级 */
.btn-primary[data-text]:hover i,
.cta-section .btn-primary:hover i,
.contact-cta .btn-primary:hover i,
.premium-cta .btn-primary:hover i,
.cta-section .btn-glow:hover i,
.contact-cta .btn-glow:hover i,
.premium-cta .btn-glow:hover i,
a.btn-primary[data-text]:hover i {
  transition: all 0.3s ease !important;
  transform: translateY(0px) !important;
  opacity: 1 !important;
  letter-spacing: 2px !important; /* 悬停时增加字母间距 */
}

/* 字母动画延迟效果 - 最高优先级 */
.btn-primary[data-text]:hover i:nth-child(1),
.cta-section .btn-primary:hover i:nth-child(1),
.contact-cta .btn-primary:hover i:nth-child(1),
.premium-cta .btn-primary:hover i:nth-child(1),
.cta-section .btn-glow:hover i:nth-child(1),
.contact-cta .btn-glow:hover i:nth-child(1),
.premium-cta .btn-glow:hover i:nth-child(1),
a.btn-primary[data-text]:hover i:nth-child(1) {
  transition-delay: 0.045s !important;
}

.btn-primary[data-text]:hover i:nth-child(2),
.cta-section .btn-primary:hover i:nth-child(2),
.contact-cta .btn-primary:hover i:nth-child(2),
.premium-cta .btn-primary:hover i:nth-child(2),
.cta-section .btn-glow:hover i:nth-child(2),
.contact-cta .btn-glow:hover i:nth-child(2),
.premium-cta .btn-glow:hover i:nth-child(2),
a.btn-primary[data-text]:hover i:nth-child(2) {
  transition-delay: calc(0.045s * 2) !important;
}

.btn-primary[data-text]:hover i:nth-child(3),
.cta-section .btn-primary:hover i:nth-child(3),
.contact-cta .btn-primary:hover i:nth-child(3),
.premium-cta .btn-primary:hover i:nth-child(3),
.cta-section .btn-glow:hover i:nth-child(3),
.contact-cta .btn-glow:hover i:nth-child(3),
.premium-cta .btn-glow:hover i:nth-child(3),
a.btn-primary[data-text]:hover i:nth-child(3) {
  transition-delay: calc(0.045s * 3) !important;
}

.btn-primary[data-text]:hover i:nth-child(4),
.cta-section .btn-primary:hover i:nth-child(4),
.contact-cta .btn-primary:hover i:nth-child(4),
.premium-cta .btn-primary:hover i:nth-child(4),
.cta-section .btn-glow:hover i:nth-child(4),
.contact-cta .btn-glow:hover i:nth-child(4),
.premium-cta .btn-glow:hover i:nth-child(4),
a.btn-primary[data-text]:hover i:nth-child(4) {
  transition-delay: calc(0.045s * 4) !important;
}

.btn-primary[data-text]:hover i:nth-child(5),
.cta-section .btn-primary:hover i:nth-child(5),
.contact-cta .btn-primary:hover i:nth-child(5),
.premium-cta .btn-primary:hover i:nth-child(5),
.cta-section .btn-glow:hover i:nth-child(5),
.contact-cta .btn-glow:hover i:nth-child(5),
.premium-cta .btn-glow:hover i:nth-child(5),
a.btn-primary[data-text]:hover i:nth-child(5) {
  transition-delay: calc(0.045s * 5) !important;
}

.btn-primary[data-text]:hover i:nth-child(6),
.cta-section .btn-primary:hover i:nth-child(6),
.contact-cta .btn-primary:hover i:nth-child(6),
.premium-cta .btn-primary:hover i:nth-child(6),
.cta-section .btn-glow:hover i:nth-child(6),
.contact-cta .btn-glow:hover i:nth-child(6),
.premium-cta .btn-glow:hover i:nth-child(6),
a.btn-primary[data-text]:hover i:nth-child(6) {
  transition-delay: calc(0.045s * 6) !important;
}

.cta-section .btn-primary:hover i:nth-child(7),
.contact-cta .btn-primary:hover i:nth-child(7),
.premium-cta .btn-primary:hover i:nth-child(7),
.cta-section .btn-glow:hover i:nth-child(7),
.contact-cta .btn-glow:hover i:nth-child(7),
.premium-cta .btn-glow:hover i:nth-child(7) {
  transition-delay: calc(0.045s * 7) !important;
}

.cta-section .btn-primary:hover i:nth-child(8),
.contact-cta .btn-primary:hover i:nth-child(8),
.premium-cta .btn-primary:hover i:nth-child(8),
.cta-section .btn-glow:hover i:nth-child(8),
.contact-cta .btn-glow:hover i:nth-child(8),
.premium-cta .btn-glow:hover i:nth-child(8) {
  transition-delay: calc(0.045s * 8) !important;
}

.cta-section .btn-primary:hover i:nth-child(9),
.contact-cta .btn-primary:hover i:nth-child(9),
.premium-cta .btn-primary:hover i:nth-child(9),
.cta-section .btn-glow:hover i:nth-child(9),
.contact-cta .btn-glow:hover i:nth-child(9),
.premium-cta .btn-glow:hover i:nth-child(9) {
  transition-delay: calc(0.045s * 9) !important;
}

.cta-section .btn-primary:hover i:nth-child(10),
.contact-cta .btn-primary:hover i:nth-child(10),
.premium-cta .btn-primary:hover i:nth-child(10),
.cta-section .btn-glow:hover i:nth-child(10),
.contact-cta .btn-glow:hover i:nth-child(10),
.premium-cta .btn-glow:hover i:nth-child(10) {
  transition-delay: calc(0.045s * 10) !important;
}

.cta-section .btn-primary:hover i:nth-child(11),
.contact-cta .btn-primary:hover i:nth-child(11),
.premium-cta .btn-primary:hover i:nth-child(11),
.cta-section .btn-glow:hover i:nth-child(11),
.contact-cta .btn-glow:hover i:nth-child(11),
.premium-cta .btn-glow:hover i:nth-child(11) {
  transition-delay: calc(0.045s * 11) !important;
}

.cta-section .btn-primary:hover i:nth-child(12),
.contact-cta .btn-primary:hover i:nth-child(12),
.premium-cta .btn-primary:hover i:nth-child(12),
.cta-section .btn-glow:hover i:nth-child(12),
.contact-cta .btn-glow:hover i:nth-child(12),
.premium-cta .btn-glow:hover i:nth-child(12) {
  transition-delay: calc(0.045s * 12) !important;
}

.cta-section .btn-primary:hover i:nth-child(13),
.contact-cta .btn-primary:hover i:nth-child(13),
.premium-cta .btn-primary:hover i:nth-child(13),
.cta-section .btn-glow:hover i:nth-child(13),
.contact-cta .btn-glow:hover i:nth-child(13),
.premium-cta .btn-glow:hover i:nth-child(13) {
  transition-delay: calc(0.045s * 13) !important;
}

.cta-section .btn-primary:hover i:nth-child(14),
.contact-cta .btn-primary:hover i:nth-child(14),
.premium-cta .btn-primary:hover i:nth-child(14),
.cta-section .btn-glow:hover i:nth-child(14),
.contact-cta .btn-glow:hover i:nth-child(14),
.premium-cta .btn-glow:hover i:nth-child(14) {
  transition-delay: calc(0.045s * 14) !important;
}

.cta-section .btn-primary:hover i:nth-child(15),
.contact-cta .btn-primary:hover i:nth-child(15),
.premium-cta .btn-primary:hover i:nth-child(15),
.cta-section .btn-glow:hover i:nth-child(15),
.contact-cta .btn-glow:hover i:nth-child(15),
.premium-cta .btn-glow:hover i:nth-child(15) {
  transition-delay: calc(0.045s * 15) !important;
}

/* 移除所有可能冲突的背景样式 */
.cta-section {
  background: var(--primary-color, #0a59f7) !important;
  background-color: var(--primary-color, #0a59f7) !important;
  background-image: none !important;
  background-gradient: none !important;
}

.contact-cta {
  background: var(--primary-color, #0a59f7) !important;
  background-color: var(--primary-color, #0a59f7) !important;
  background-image: none !important;
  background-gradient: none !important;
}

.premium-cta {
  background: var(--primary-color, #0a59f7) !important;
  background-color: var(--primary-color, #0a59f7) !important;
  background-image: none !important;
  background-gradient: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cta-section,
  .contact-cta,
  .premium-cta {
    padding: 3rem 0 !important;
  }

  .cta-section h2,
  .contact-cta h2,
  .premium-cta h2,
  .cta-section .cta-content h2,
  .contact-cta .cta-content h2,
  .premium-cta .cta-content h2 {
    font-size: 2rem !important;
  }

  .cta-section p,
  .contact-cta p,
  .premium-cta p,
  .cta-section .cta-content p,
  .contact-cta .cta-content p,
  .premium-cta .cta-content p {
    font-size: 1.1rem !important;
    margin-bottom: 2rem !important;
  }

  .cta-section .btn-primary,
  .contact-cta .btn-primary,
  .premium-cta .btn-primary,
  .cta-section .btn-glow,
  .contact-cta .btn-glow,
  .premium-cta .btn-glow {
    padding: 12px 24px !important;
    font-size: 0.9rem !important;
  }
}

/* 确保所有CTA区域都有粒子效果 */
.cta-section:not(.cta-particles):not([class*="particles"])::before,
.contact-cta:not(.cta-particles):not([class*="particles"])::before,
.premium-cta:not(.cta-particles):not([class*="particles"])::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.12) 0%, transparent 25%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.12) 0%, transparent 25%) !important;
  z-index: 0 !important;
  pointer-events: none !important;
}

/* 强制覆盖 - 确保动画按钮样式绝对优先 */
a[data-text].btn-primary,
a.btn-primary[data-text] {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 50px !important;
  position: relative !important;
  padding: 0 20px !important;
  font-size: 18px !important;
  text-transform: uppercase !important;
  border: 0 !important;
  box-shadow: hsl(210deg 87% 36%) 0px 7px 0px 0px !important;
  background-color: hsl(210deg 100% 44%) !important;
  border-radius: 12px !important;
  overflow: visible !important; /* 改为visible以防止内容被裁剪 */
  transition: 31ms cubic-bezier(.5, .7, .4, 1) !important;
  text-decoration: none !important;
  color: white !important;
  font-weight: bold !important;
  letter-spacing: 4px !important;
  z-index: 1 !important;
  cursor: pointer !important;
  min-width: fit-content !important; /* 确保按钮宽度适应内容 */
  white-space: nowrap !important; /* 防止文字换行 */
  flex-wrap: nowrap !important; /* 防止flex项目换行 */
}

/* 覆盖Next.js生成的内联样式 - 针对关于我们页面 */
.contact-cta a[data-text].btn-primary,
.contact-cta a.btn-primary[data-text],
.about-us-page .contact-cta .btn-primary {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 50px !important;
  position: relative !important;
  padding: 0 20px !important;
  font-size: 18px !important;
  text-transform: uppercase !important;
  border: 0 !important;
  box-shadow: hsl(210deg 87% 36%) 0px 7px 0px 0px !important;
  background-color: hsl(210deg 100% 44%) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  transition: 31ms cubic-bezier(.5, .7, .4, 1) !important;
  text-decoration: none !important;
  color: white !important;
  font-weight: bold !important;
  letter-spacing: 4px !important;
  z-index: 1 !important;
  cursor: pointer !important;
  gap: 0 !important; /* 移除gap */
  margin: 0 auto !important; /* 居中显示 */
  width: auto !important;
  min-width: 200px !important;
}

/* 针对Next.js生成的JSX类名的特殊覆盖 */
.contact-cta .btn-primary i[class*="jsx-"],
.contact-cta .btn-primary i[class^="jsx-"],
.about-us-page .contact-cta .btn-primary i[class*="jsx-"],
.about-us-page .contact-cta .btn-primary i[class^="jsx-"] {
  display: inline !important;
  font-style: normal !important;
  font-weight: inherit !important;
  color: inherit !important;
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
}

/* 确保按钮内的字符正确显示 */
.contact-cta .btn-primary i,
.about-us-page .contact-cta .btn-primary i {
  display: inline !important;
  font-style: normal !important;
  font-weight: inherit !important;
  color: inherit !important;
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
}

/* 强制覆盖 - 默认文字显示 */
a[data-text].btn-primary:before,
a.btn-primary[data-text]:before {
  content: attr(data-text) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: absolute !important;
  inset: 0 !important;
  font-size: 15px !important;
  font-weight: bold !important;
  color: white !important;
  letter-spacing: 2px !important;
  opacity: 1 !important;
  transition: all 0.3s ease !important;
  transform: translateY(0) !important;
  z-index: 2 !important;
}

/* 强制覆盖 - 字母默认隐藏 */
a[data-text].btn-primary i,
a.btn-primary[data-text] i {
  color: white !important;
  font-size: 15px !important;
  font-weight: bold !important;
  letter-spacing: 0px !important;
  font-style: normal !important;
  transition: all 0.3s ease !important;
  transform: translateY(20px) !important;
  opacity: 0 !important;
  display: inline-block !important;
  position: relative !important;
  z-index: 3 !important;
}

/* 特定放大：首页CustomSolutions组件中的"获取定制服务方案"按钮 */
/* 使用data-text属性来精确定位这个特定按钮 */
a[data-text*="获取"][data-text*="定制"][data-text*="方案"].btn-primary,
a.btn-primary[data-text*="获取"][data-text*="定制"][data-text*="方案"],
a[data-text*="获取全息投影定制方案"].btn-primary,
a.btn-primary[data-text*="获取全息投影定制方案"] {
  transform: scale(1.4) !important; /* 进一步增加放大比例 */
  min-width: 360px !important; /* 增加中文按钮最小宽度 */
  width: auto !important; /* 允许按钮自动调整宽度 */
  height: 80px !important; /* 进一步增加高度 */
  font-size: 20px !important; /* 进一步增大字体 */
  padding: 0 40px !important; /* 进一步增加内边距 */
  margin: 25px auto !important; /* 增加外边距 */
  white-space: nowrap !important; /* 防止文字换行 */
  flex-wrap: nowrap !important; /* 防止flex项目换行 */
  max-width: 95% !important; /* 稍微增加最大宽度 */
}

/* 英文按钮样式 - 需要更大的宽度 */
a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary,
a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"],
a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary,
a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"],
a[data-text="Get Your Custom Service Plan"].btn-primary,
a.btn-primary[data-text="Get Your Custom Service Plan"] {
  transform: scale(1.2) !important; /* 适中的放大比例 */
  min-width: 420px !important; /* 适中的最小宽度 */
  width: auto !important; /* 允许按钮自动调整宽度 */
  height: 65px !important; /* 适中的高度 */
  font-size: 18px !important; /* 适中的字体大小 */
  padding: 0 35px !important; /* 适中的内边距 */
  margin: 20px auto !important; /* 适中的外边距 */
  white-space: nowrap !important; /* 防止文字换行 */
  flex-wrap: nowrap !important; /* 防止flex项目换行 */
  max-width: 95% !important; /* 适中的最大宽度 */
  overflow: visible !important; /* 确保内容不被裁剪 */
}

/* 特定放大：首页CustomSolutions组件中按钮的默认文字 */
a[data-text*="获取"][data-text*="定制"][data-text*="方案"].btn-primary:before,
a.btn-primary[data-text*="获取"][data-text*="定制"][data-text*="方案"]:before,
a[data-text*="获取全息投影定制方案"].btn-primary:before,
a.btn-primary[data-text*="获取全息投影定制方案"]:before,
/* 英文按钮默认文字样式 */
a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary:before,
a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"]:before,
a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary:before,
a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"]:before,
a[data-text="Get Your Custom Service Plan"].btn-primary:before,
a.btn-primary[data-text="Get Your Custom Service Plan"]:before {
  font-size: 18px !important; /* 适中的默认文字字体 */
}

/* 特定放大：首页CustomSolutions组件中按钮的动画字母 */
a[data-text*="获取"][data-text*="定制"][data-text*="方案"].btn-primary i,
a.btn-primary[data-text*="获取"][data-text*="定制"][data-text*="方案"] i,
a[data-text*="获取全息投影定制方案"].btn-primary i,
a.btn-primary[data-text*="获取全息投影定制方案"] i {
  font-size: 20px !important; /* 进一步增大动画字母字体 */
  white-space: nowrap !important; /* 防止字母换行 */
  flex-shrink: 0 !important; /* 防止字母收缩 */
  display: inline-block !important; /* 确保字母正确显示 */
}

/* 英文按钮动画字母样式 - 特殊处理 */
a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary i,
a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"] i,
a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary i,
a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] i,
a[data-text="Get Your Custom Service Plan"].btn-primary i,
a.btn-primary[data-text="Get Your Custom Service Plan"] i {
  font-size: 18px !important; /* 适中的动画字母字体 */
  white-space: nowrap !important; /* 防止字母换行 */
  flex-shrink: 0 !important; /* 防止字母收缩 */
  display: inline-block !important; /* 确保字母正确显示 */
  min-width: auto !important; /* 允许字母自适应宽度 */
  letter-spacing: 0 !important; /* 默认无额外字母间距 */
}

/* 特定放大：首页CustomSolutions组件中按钮悬停时的动画字母 */
a[data-text*="获取"][data-text*="定制"][data-text*="方案"].btn-primary:hover i,
a.btn-primary[data-text*="获取"][data-text*="定制"][data-text*="方案"]:hover i,
a[data-text*="获取全息投影定制方案"].btn-primary:hover i,
a.btn-primary[data-text*="获取全息投影定制方案"]:hover i {
  font-size: 20px !important; /* 进一步增大悬停时的字体大小 */
  letter-spacing: 2px !important; /* 悬停时增加字母间距 */
}

/* 英文按钮悬停时动画字母样式 - 减少字母间距以防止溢出 */
a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary:hover i,
a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"]:hover i,
a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary:hover i,
a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"]:hover i {
  font-size: 20px !important; /* 进一步增大悬停时的字体大小 */
  letter-spacing: 1px !important; /* 英文按钮悬停时使用较小的字母间距 */
}

/* 响应式设计 - 平板设备 */
@media (max-width: 768px) {
  /* 英文按钮在平板设备上的样式调整 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"],
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] {
    min-width: 400px !important; /* 在平板上减少最小宽度 */
    font-size: 16px !important; /* 减小字体 */
    padding: 0 25px !important; /* 减少内边距 */
    transform: scale(1.15) !important; /* 减少缩放比例 */
  }

  /* 平板设备上的字母样式 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary i,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"] i,
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary i,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] i {
    font-size: 16px !important;
  }
}

/* 响应式设计 - 手机设备 */
@media (max-width: 576px) {
  /* 英文按钮在手机设备上的样式调整 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"],
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] {
    min-width: 300px !important; /* 在手机上进一步减少最小宽度 */
    font-size: 14px !important; /* 进一步减小字体 */
    padding: 0 20px !important; /* 进一步减少内边距 */
    transform: scale(1.05) !important; /* 进一步减少缩放比例 */
    height: 60px !important; /* 减少高度 */
  }

  /* 手机设备上的字母样式 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary i,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"] i,
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary i,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] i {
    font-size: 14px !important;
  }

  /* 手机设备上悬停时减少字母间距 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary:hover i,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"]:hover i,
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary:hover i,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"]:hover i {
    letter-spacing: 0.5px !important; /* 在手机上使用更小的字母间距 */
  }
}
