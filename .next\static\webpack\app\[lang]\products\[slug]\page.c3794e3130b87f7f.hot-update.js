"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lang]/products/[slug]/page",{

/***/ "(app-pages-browser)/./app/[lang]/products/[slug]/page.tsx":
/*!*********************************************!*\
  !*** ./app/[lang]/products/[slug]/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _utils_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/i18n */ \"(app-pages-browser)/./app/utils/i18n.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// 移除复杂的导入\n// 产品骨架屏组件\nfunction ProductSkeleton() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-16 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-4 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-20 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-4 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-24 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 md:px-8 lg:px-12 py-8 md:py-12 lg:py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-200 rounded-2xl product-image-1920x1080\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-3\",\n                                            children: [\n                                                ...Array(4)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-200 rounded-xl aspect-video\"\n                                                }, i, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8 lg:pl-8 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-12 w-4/5 bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 44,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-8 w-24 bg-gray-200 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 w-full bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 w-full bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 w-3/4 bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 w-20 bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3\",\n                                                    children: [\n                                                        ...Array(4)\n                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-12 bg-gray-200 rounded-xl\"\n                                                        }, i, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 60,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4 pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-14 w-48 bg-gray-200 rounded-2xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 w-20 bg-gray-200 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 69,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 w-16 bg-gray-200 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 70,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_c = ProductSkeleton;\n// 产品详情页面组件\nfunction ProductPage(param) {\n    let { params } = param;\n    var _dict_common, _dict_common1, _product_images, _dict_common2, _dict_common3, _dict_common4, _dict_common5, _dict_common6, _dict_common7, _dict_common8, _dict_common9;\n    _s();\n    const { slug, lang } = params;\n    // 状态\n    const [dict, setDict] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedImageIndex, setSelectedImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    // 获取数据 - 使用国际化API\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // 定义一个异步函数来获取数据\n        const fetchData = async ()=>{\n            console.log(\"[产品详情] 开始加载产品数据: \".concat(slug, \", 语言: \").concat(lang));\n            try {\n                setLoading(true);\n                setError(null);\n                // 获取字典\n                const dictionary = await (0,_utils_i18n__WEBPACK_IMPORTED_MODULE_1__.getDictionary)(lang);\n                setDict(dictionary);\n                // 使用国际化API获取产品详情\n                const response = await fetch(\"/api/products/by-slug/\".concat(slug, \"?lang=\").concat(lang));\n                if (response.ok) {\n                    const productData = await response.json();\n                    if (productData.product) {\n                        console.log(\"[产品详情] 成功获取产品数据:\", productData.product.name);\n                        setProduct(productData.product);\n                        setLoading(false);\n                        return;\n                    }\n                }\n                // 如果API失败，回退到直接读取JSON文件\n                console.log(\"[产品详情] API失败，回退到JSON文件\");\n                const jsonResponse = await fetch(\"/mock-products.json\");\n                if (jsonResponse.ok) {\n                    const products = await jsonResponse.json();\n                    const foundProduct = products.find((p)=>p.slug === slug);\n                    if (foundProduct) {\n                        // 根据语言选择相应的字段\n                        const localizedProduct = {\n                            ...foundProduct,\n                            name: lang === \"en\" ? foundProduct.name_en || foundProduct.name : foundProduct.name,\n                            description: lang === \"en\" ? foundProduct.description_en || foundProduct.description : foundProduct.description,\n                            category: lang === \"en\" ? foundProduct.category_en || foundProduct.category : foundProduct.category,\n                            features: lang === \"en\" ? foundProduct.features_en || foundProduct.features : foundProduct.features\n                        };\n                        console.log(\"[产品详情] 从JSON文件获取产品数据:\", localizedProduct.name);\n                        setProduct(localizedProduct);\n                        setLoading(false);\n                        return;\n                    }\n                }\n                // 如果没有找到产品，抛出错误\n                throw new Error(lang === \"zh\" ? \"产品不存在\" : \"Product not found\");\n            } catch (error) {\n                console.error(\"[产品详情] 加载失败:\", error);\n                setError(error instanceof Error ? error.message : lang === \"zh\" ? \"加载失败\" : \"Loading failed\");\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, [\n        lang,\n        slug\n    ]);\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    style: {\n                        marginTop: \"0\",\n                        display: \"block\",\n                        lineHeight: 0\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-200 w-full\",\n                        style: {\n                            height: \"300px\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"product-detail-container product-detail-fix\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSkeleton, {}, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // 渲染错误状态\n    if (error || !product) {\n        var _dict_common10, _dict_common11;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    style: {\n                        marginTop: \"0\",\n                        display: \"block\",\n                        lineHeight: 0\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 w-full\",\n                        style: {\n                            height: \"100px\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"product-detail-container product-detail-fix\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 py-12 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 text-red-500\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-16 w-16 mx-auto\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold mb-4 text-gray-800\",\n                                children: ((_dict_common10 = dict.common) === null || _dict_common10 === void 0 ? void 0 : _dict_common10.product_not_found) || (lang === \"zh\" ? \"产品不存在\" : \"Product Not Found\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-8\",\n                                children: error || (lang === \"zh\" ? \"无法找到请求的产品\" : \"The requested product could not be found\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\".concat(lang, \"/products\"),\n                                className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: ((_dict_common11 = dict.common) === null || _dict_common11 === void 0 ? void 0 : _dict_common11.products) || (lang === \"zh\" ? \"浏览产品\" : \"Browse Products\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // 获取产品图片 - 使用实际图片\n    const getProductImages = (product)=>{\n        if (product.images && product.images.length > 0) {\n            return product.images;\n        }\n        // 如果没有图片，返回默认占位符\n        return [\n            \"/images/products/placeholder.jpg\"\n        ];\n    };\n    // 获取产品规格信息\n    const getProductSpecifications = (product, lang)=>{\n        var _product_category, _product_name, _product_category1, _product_name1, _product_category2, _product_name2;\n        // 根据产品类型返回不同的规格\n        if (product.slug === \"ktv\" || ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.includes(\"KTV\")) || ((_product_name = product.name) === null || _product_name === void 0 ? void 0 : _product_name.includes(\"KTV\"))) {\n            return [\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    title: lang === \"zh\" ? \"投影技术\" : \"Projection Technology\",\n                    desc: lang === \"zh\" ? \"4K全息投影系统\" : \"4K Holographic Projection System\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    title: lang === \"zh\" ? \"容纳人数\" : \"Capacity\",\n                    desc: lang === \"zh\" ? \"最多20人同时体验\" : \"Up to 20 people simultaneously\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    title: lang === \"zh\" ? \"音响配置\" : \"Audio System\",\n                    desc: lang === \"zh\" ? \"7.1环绕立体声\" : \"7.1 Surround Sound\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    title: lang === \"zh\" ? \"处理器\" : \"Processor\",\n                    desc: lang === \"zh\" ? \"实时渲染引擎\" : \"Real-time Rendering Engine\"\n                }\n            ];\n        } else if (((_product_category1 = product.category) === null || _product_category1 === void 0 ? void 0 : _product_category1.includes(\"蹦床\")) || ((_product_name1 = product.name) === null || _product_name1 === void 0 ? void 0 : _product_name1.includes(\"蹦床\"))) {\n            return [\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    title: lang === \"zh\" ? \"AR技术\" : \"AR Technology\",\n                    desc: lang === \"zh\" ? \"增强现实互动系统\" : \"Augmented Reality Interactive System\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    title: lang === \"zh\" ? \"适用年龄\" : \"Age Range\",\n                    desc: lang === \"zh\" ? \"3-15岁儿童\" : \"Children aged 3-15\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    title: lang === \"zh\" ? \"安全等级\" : \"Safety Level\",\n                    desc: lang === \"zh\" ? \"欧盟CE认证\" : \"EU CE Certified\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    title: lang === \"zh\" ? \"传感器\" : \"Sensors\",\n                    desc: lang === \"zh\" ? \"高精度动作捕捉\" : \"High-precision Motion Capture\"\n                }\n            ];\n        } else if (((_product_category2 = product.category) === null || _product_category2 === void 0 ? void 0 : _product_category2.includes(\"沙盘\")) || ((_product_name2 = product.name) === null || _product_name2 === void 0 ? void 0 : _product_name2.includes(\"沙盘\"))) {\n            return [\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    title: lang === \"zh\" ? \"投影技术\" : \"Projection Technology\",\n                    desc: lang === \"zh\" ? \"3D立体投影\" : \"3D Stereoscopic Projection\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    title: lang === \"zh\" ? \"互动方式\" : \"Interaction Method\",\n                    desc: lang === \"zh\" ? \"手势识别控制\" : \"Gesture Recognition Control\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    title: lang === \"zh\" ? \"教育内容\" : \"Educational Content\",\n                    desc: lang === \"zh\" ? \"多学科课程包\" : \"Multi-disciplinary Course Package\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    title: lang === \"zh\" ? \"系统配置\" : \"System Configuration\",\n                    desc: lang === \"zh\" ? \"智能学习算法\" : \"Intelligent Learning Algorithm\"\n                }\n            ];\n        } else {\n            return [\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    title: lang === \"zh\" ? \"显示技术\" : \"Display Technology\",\n                    desc: lang === \"zh\" ? \"高清数字显示\" : \"High-definition Digital Display\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    title: lang === \"zh\" ? \"用户体验\" : \"User Experience\",\n                    desc: lang === \"zh\" ? \"多人互动支持\" : \"Multi-user Interactive Support\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    title: lang === \"zh\" ? \"音效系统\" : \"Audio System\",\n                    desc: lang === \"zh\" ? \"立体声音响\" : \"Stereo Sound System\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    title: lang === \"zh\" ? \"控制系统\" : \"Control System\",\n                    desc: lang === \"zh\" ? \"智能化管理\" : \"Intelligent Management\"\n                }\n            ];\n        }\n    };\n    const specifications = getProductSpecifications(product);\n    const mappedImages = getProductImages(product);\n    const thumbnails = mappedImages;\n    // 正常渲染产品详情 - 现代化设计\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative product-detail-page\",\n        style: {\n            marginTop: 0,\n            paddingTop: 0\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full absolute top-0 left-0 right-0\",\n                style: {\n                    height: \"450px\",\n                    marginTop: \"0\",\n                    zIndex: 0\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/images/products/product-banner.png\",\n                        alt: \"Product Banner\",\n                        className: \"w-full h-full object-cover\",\n                        loading: \"eager\",\n                        fetchPriority: \"high\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        style: {\n                            zIndex: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"product-detail-navigation-overlay\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"breadcrumbs-overlay animate-slide-in-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\".concat(lang),\n                                                children: ((_dict_common = dict.common) === null || _dict_common === void 0 ? void 0 : _dict_common.home) || (lang === \"zh\" ? \"首页\" : \"Home\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"separator\",\n                                                children: \"/\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\".concat(lang, \"/products\"),\n                                                children: ((_dict_common1 = dict.common) === null || _dict_common1 === void 0 ? void 0 : _dict_common1.products) || (lang === \"zh\" ? \"产品\" : \"Products\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"separator\",\n                                                children: \"/\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"current\",\n                                                children: product.title || product.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                lineNumber: 332,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"product-detail-container bg-white relative transform scale-110 origin-top\",\n                style: {\n                    marginTop: \"450px\",\n                    zIndex: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-8 py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-2 gap-8 items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative aspect-[16/9] bg-gray-100 rounded-lg overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: thumbnails[selectedImageIndex] || ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : _product_images[0]) || \"/images/products/placeholder.jpg\",\n                                                        alt: product.title || product.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    product.video_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-900 shadow-lg px-6 py-3 rounded-lg transition-all group\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-5 h-5 group-hover:scale-110 transition-transform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ((_dict_common2 = dict.common) === null || _dict_common2 === void 0 ? void 0 : _dict_common2.watch_demo) || (lang === \"zh\" ? \"观看演示\" : \"Watch Demo\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, this),\n                                            thumbnails && thumbnails.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2 overflow-x-auto pb-2 -mx-2 px-2\",\n                                                children: thumbnails.map((thumb, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedImageIndex(index),\n                                                        className: \"flex-shrink-0 w-24 h-20 rounded border-2 overflow-hidden transition-colors \".concat(selectedImageIndex === index ? \"border-gray-900\" : \"border-gray-200 hover:border-gray-400\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: thumb || \"/images/products/placeholder.jpg\",\n                                                            alt: \"视图 \".concat(index + 1),\n                                                            className: \"object-cover w-full h-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-gray-900 text-white px-3 py-1 rounded text-sm\",\n                                                            children: ((_dict_common3 = dict.common) === null || _dict_common3 === void 0 ? void 0 : _dict_common3.professional) || (lang === \"zh\" ? \"专业级\" : \"Professional\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 415,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                [\n                                                                    ...Array(5)\n                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4 fill-gray-900 text-gray-900\"\n                                                                    }, i, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 25\n                                                                    }, this)),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 ml-2\",\n                                                                    children: [\n                                                                        \"4.9 (128 \",\n                                                                        ((_dict_common4 = dict.common) === null || _dict_common4 === void 0 ? void 0 : _dict_common4.reviews) || (lang === \"zh\" ? \"评价\" : \"reviews\"),\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 422,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-4xl lg:text-5xl font-light text-gray-900 mb-4 leading-tight\",\n                                                            children: product.title || product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl text-gray-600 leading-relaxed\",\n                                                            children: product.description || (lang === \"zh\" ? \"专业级互动设备，采用先进技术为用户提供沉浸式体验解决方案。\" : \"Professional interactive equipment using advanced technology to provide immersive experience solutions.\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6\",\n                                                    children: specifications.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(spec.icon, {\n                                                                            className: \"w-5 h-5 text-gray-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 440,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: spec.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 441,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 text-sm\",\n                                                                    children: spec.desc\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/\".concat(lang, \"/pages/contact-us\"),\n                                                                className: \"flex-1 bg-gray-900 hover:bg-gray-800 text-white h-12 px-6 rounded-lg flex items-center justify-center transition-colors\",\n                                                                children: ((_dict_common5 = dict.common) === null || _dict_common5 === void 0 ? void 0 : _dict_common5.get_quote) || (lang === \"zh\" ? \"获取报价\" : \"Get Quote\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 461,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        ((_dict_common6 = dict.common) === null || _dict_common6 === void 0 ? void 0 : _dict_common6.in_stock) || (lang === \"zh\" ? \"现货供应\" : \"In Stock\")\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 460,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: ((_dict_common7 = dict.common) === null || _dict_common7 === void 0 ? void 0 : _dict_common7.professional_installation) || (lang === \"zh\" ? \"专业安装\" : \"Professional Installation\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: ((_dict_common8 = dict.common) === null || _dict_common8 === void 0 ? void 0 : _dict_common8.three_year_warranty) || (lang === \"zh\" ? \"质保3年\" : \"3-Year Warranty\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl lg:text-5xl font-light text-gray-900 mb-6\",\n                                        children: ((_dict_common9 = dict.common) === null || _dict_common9 === void 0 ? void 0 : _dict_common9.product_gallery) || (lang === \"zh\" ? \"产品展示\" : \"Product Gallery\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed\",\n                                        children: lang === \"zh\" ? \"专业级\".concat(product.category || \"互动设备\", \"在不同应用场景中的实际效果展示\") : \"Professional \".concat(product.category || \"interactive equipment\", \" showcased in various application scenarios\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-0\",\n                                children: mappedImages.filter(Boolean).map((image, index)=>{\n                                    var _dict_common, _dict_common1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full h-screen bg-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: image || \"/images/products/placeholder.jpg\",\n                                                alt: \"\".concat(product.title || product.name, \" - \").concat(((_dict_common = dict.common) === null || _dict_common === void 0 ? void 0 : _dict_common.application_scenario) || (lang === \"zh\" ? \"应用场景\" : \"Application scenario\"), \" \").concat(index + 1),\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"container mx-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white text-lg font-medium\",\n                                                        children: [\n                                                            product.title || product.name,\n                                                            \" - \",\n                                                            ((_dict_common1 = dict.common) === null || _dict_common1 === void 0 ? void 0 : _dict_common1.application_scenario) || (lang === \"zh\" ? \"应用场景\" : \"Application Scenario\"),\n                                                            \" \",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n        lineNumber: 330,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductPage, \"Uzsa2ydllt3CX7Io1AHebWWioLk=\");\n_c1 = ProductPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ProductSkeleton\");\n$RefreshReg$(_c1, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[lang]/products/[slug]/page.tsx\n"));

/***/ })

});