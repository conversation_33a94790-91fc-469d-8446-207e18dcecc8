'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function RedirectFixPage() {
  const router = useRouter();

  useEffect(() => {
    // 立即重定向到正确的管理员后台页面
    console.log('检测到错误的路径，正在重定向到正确的管理员后台...');
    router.replace('/zh/admin');
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p className="text-gray-600">正在重定向到管理员后台...</p>
      </div>
    </div>
  );
}
