#!/bin/bash

# 跨境电商项目部署脚本
set -e

echo "🚀 开始部署跨境电商项目..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p ssl
mkdir -p uploads
mkdir -p logs

# 检查环境变量文件
if [ ! -f .env.production ]; then
    echo "❌ .env.production文件不存在，请先配置环境变量"
    exit 1
fi

# 构建镜像
echo "🔨 构建Docker镜像..."
docker-compose build --no-cache

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down

# 清理未使用的镜像
echo "🧹 清理未使用的镜像..."
docker image prune -f

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 检查应用健康状态
echo "🏥 检查应用健康状态..."
if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
    echo "✅ 应用启动成功！"
else
    echo "❌ 应用启动失败，请检查日志"
    docker-compose logs web
    exit 1
fi

# 显示访问信息
echo ""
echo "🎉 部署完成！"
echo "📱 应用访问地址: http://localhost:3000"
echo "🗄️  数据库访问: localhost:5432"
echo "🔄 Redis访问: localhost:6379"
echo ""
echo "📋 常用命令:"
echo "  查看日志: docker-compose logs -f"
echo "  重启服务: docker-compose restart"
echo "  停止服务: docker-compose down"
echo "  更新应用: ./deploy.sh"
echo ""

# 设置定时备份
echo "💾 设置数据库备份..."
(crontab -l 2>/dev/null; echo "0 2 * * * docker exec \$(docker-compose ps -q postgres) pg_dump -U postgres ecommerce > /backup/db_\$(date +\%Y\%m\%d).sql") | crontab -

echo "✅ 部署脚本执行完成！"
