'use client';

import { useEffect, useRef } from 'react';
import { useLanguage } from './LanguageProvider';

export default function AdvantageShowcase() {
  const { t, locale } = useLanguage();
  const sectionRef = useRef<HTMLDivElement>(null);

  // 加载图标字体
  useEffect(() => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css';
    document.head.appendChild(link);

    // 添加简单的滚动动画
    const handleScroll = () => {
      const section = sectionRef.current;
      if (!section) return;

      const sectionTop = section.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;

      if (sectionTop < windowHeight * 0.75) {
        section.classList.add('visible');
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // 初始检查

    return () => {
      document.head.removeChild(link);
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // 优势卡片数据
  const advantages = [
    {
      icon: 'fa-wand-magic-sparkles',
      title: t('advantages.customization.title', { fallback: '定制全息解决方案' }),
      description: t('advantages.customization.description', {
        fallback: '根据您的空间和需求定制专属全息投影环境',
      }),
      number: '01',
    },
    {
      icon: 'fa-headset',
      title: t('advantages.support.title', { fallback: '专业技术团队' }),
      description: t('advantages.support.description', {
        fallback: '拥有10年+全息投影技术经验的专家团队',
      }),
      number: '02',
    },
    {
      icon: 'fa-clock',
      title: t('advantages.service.title', { fallback: '24小时技术支持' }),
      description: t('advantages.service.description', {
        fallback: '全天候专业客户服务和技术支持',
      }),
      number: '03',
    },
    {
      icon: 'fa-ranking-star',
      title: t('advantages.quality.title', { fallback: '高端设备品质' }),
      description: t('advantages.quality.description', {
        fallback: '采用国际一流投影设备和光学材料',
      }),
      number: '04',
    },
    {
      icon: 'fa-earth-asia',
      title: t('advantages.invoice.title', { fallback: '全球项目经验' }),
      description: t('advantages.invoice.description', {
        fallback: '已在80多个国家完成数百个成功案例',
      }),
      number: '05',
    },
    {
      icon: 'fa-handshake',
      title: t('advantages.value.title', { fallback: '一站式服务' }),
      description: t('advantages.value.description', {
        fallback: '从设计、内容制作到安装调试的全流程服务',
      }),
      number: '06',
    },
  ];

  return (
    <div className="advantages-container" ref={sectionRef}>
      <div className="section-header">
        <div className="title-flex">
          <div className="section-badge">
            <span>{t('advantages.badge', { fallback: '为什么选择我们' })}</span>
          </div>
          <h2 className="section-title">
            <span className="thin-text">
              {t('advantages.title_prefix', { fallback: '我们的' })}
            </span>
            <span className="strong-text">
              {t('advantages.title_main', { fallback: '核心优势' })}
            </span>
          </h2>
        </div>

        <div className="title-underline"></div>

        <div className="section-subtitle">
          {t('advantages.subtitle', {
            fallback: '广州钧盛科技有限公司专注于提供高品质的全息投影解决方案，为客户创造价值',
          })}
        </div>

        <div className="divider-dot-container">
          <span className="divider-dot"></span>
        </div>
      </div>

      <div className="advantages-grid">
        {advantages.map((advantage, index) => (
          <div key={index} className="advantage-card">
            <div className="advantage-number">{advantage.number}</div>
            <div className="advantage-content">
              <div className="advantage-icon-wrapper">
                <div className="advantage-icon" style={{ background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)', color: 'white' }}>
                  <i className={`fas ${advantage.icon}`} style={{ color: 'white' }}></i>
                </div>
              </div>
              <h3 className="advantage-title">{advantage.title}</h3>
              <p className="advantage-description">{advantage.description}</p>
            </div>
          </div>
        ))}
      </div>

      <style jsx>{`
        .advantages-container {
          padding: 100px 30px;
          max-width: 100%;
          margin: 0 auto;
          opacity: 0;
          transform: translateY(30px);
          transition: all 1s cubic-bezier(0.19, 1, 0.22, 1);
          position: relative;
        }

        .advantages-container::before {
          content: '';
          position: absolute;
          width: 300px;
          height: 300px;
          border-radius: 50%;
          background: radial-gradient(circle, rgba(59, 130, 246, 0.03) 0%, rgba(59, 130, 246, 0) 70%);
          top: 10%;
          left: -5%;
          z-index: -1;
        }

        .advantages-container::after {
          content: '';
          position: absolute;
          width: 200px;
          height: 200px;
          border-radius: 50%;
          background: radial-gradient(circle, rgba(29, 78, 216, 0.02) 0%, rgba(29, 78, 216, 0) 70%);
          bottom: 10%;
          right: -5%;
          z-index: -1;
        }

        .advantages-container.visible {
          opacity: 1;
          transform: translateY(0);
        }

        .section-header {
          margin-bottom: 60px;
          text-align: center;
          max-width: 1200px;
          margin-left: auto;
          margin-right: auto;
        }

        .title-flex {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-bottom: 10px;
        }

        .section-badge {
          display: inline-block;
          padding: 7px 15px;
          border-radius: 100px;
          background-color: rgba(26, 26, 46, 0.03);
          color: #1a1a2e;
          font-size: 0.85rem;
          font-weight: 400;
          margin-bottom: 10px;
        }

        .section-title {
          margin-bottom: 16px;
          font-size: 42px;
          line-height: 1.2;
          color: #1a1a2e;
          text-align: center;
        }

        .thin-text {
          font-weight: 300;
          color: #666;
          margin-right: 10px;
        }

        .strong-text {
          font-weight: 600;
          color: #1a1a2e;
        }

        .title-underline {
          width: 50px;
          height: 2px;
          background-color: rgba(26, 26, 46, 0.3);
          margin: 15px auto 20px;
        }

        .section-subtitle {
          max-width: 700px;
          margin: 0 auto 32px;
          font-size: 18px;
          line-height: 1.6;
          color: #555;
          text-align: center;
        }

        .divider-dot-container {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 5px;
        }

        .divider-dot {
          width: 4px;
          height: 4px;
          background-color: rgba(26, 26, 46, 0.4);
          border-radius: 50%;
        }

        .advantages-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 30px;
          max-width: 1400px;
          margin: 0 auto;
        }

        .advantage-card {
          background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
          padding: 0;
          border-radius: 12px;
          box-shadow: 0 15px 35px rgba(59, 130, 246, 0.08);
          transition: all 0.4s ease;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          text-align: left;
          position: relative;
          overflow: hidden;
          border: 1px solid rgba(59, 130, 246, 0.1);
          height: 100%;
        }

        .advantage-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 50%, #2563eb 100%);
          z-index: 1;
        }

        .advantage-card:hover {
          transform: translateY(-10px);
          box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
          border-color: rgba(59, 130, 246, 0.2);
          background: linear-gradient(135deg, #ffffff 0%, #eff6ff 100%);
        }

        .advantage-number {
          position: absolute;
          top: 20px;
          right: 20px;
          font-size: 4.5rem;
          font-weight: 800;
          color: rgba(59, 130, 246, 0.08);
          z-index: 1;
          line-height: 0.8;
          transition: color 0.3s ease;
        }

        .advantage-card:hover .advantage-number {
          color: rgba(59, 130, 246, 0.12);
        }

        .advantage-content {
          padding: 45px 35px 35px;
          width: 100%;
          position: relative;
          z-index: 2;
        }

        .advantage-icon-wrapper {
          margin-bottom: 25px;
        }

        .advantage-icon {
          width: 70px;
          height: 70px;
          border-radius: 14px;
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
          color: white !important;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.8rem;
          box-shadow: 0 8px 20px rgba(59, 130, 246, 0.25);
          transition: all 0.3s ease;
        }

        .advantage-icon i {
          color: white !important;
        }

        .advantage-card:hover .advantage-icon {
          transform: scale(1.05);
          box-shadow: 0 12px 25px rgba(59, 130, 246, 0.35);
        }

        .advantage-card:hover .advantage-icon i {
          color: white !important;
        }

        .advantage-title {
          font-size: 1.4rem;
          font-weight: 600;
          margin-bottom: 15px;
          color: #1e40af;
          transition: color 0.3s ease;
        }

        .advantage-card:hover .advantage-title {
          color: #1d4ed8;
        }

        .advantage-description {
          font-size: 1rem;
          line-height: 1.6;
          color: #555;
          font-weight: 300;
          margin: 15px 0 0 0;
        }

        @media (max-width: 1600px) {
          .advantages-grid {
            gap: 25px;
          }

          .advantage-content {
            padding: 40px 30px 30px;
          }

          .advantage-icon {
            width: 65px;
            height: 65px;
            font-size: 1.7rem;
          }

          .advantage-number {
            font-size: 4rem;
          }
        }

        @media (max-width: 1200px) {
          .advantages-container {
            padding: 80px 20px;
          }

          .section-title {
            font-size: 2.4rem;
          }

          .section-subtitle {
            font-size: 0.95rem;
          }
        }

        @media (max-width: 992px) {
          .advantages-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
          }

          .section-title {
            font-size: 2.2rem;
          }
        }

        @media (max-width: 768px) {
          .advantages-container {
            padding: 60px 20px;
          }

          .advantages-grid {
            grid-template-columns: 1fr;
            max-width: 500px;
            margin: 0 auto;
          }

          .section-title {
            font-size: 2rem;
          }

          .section-subtitle {
            font-size: 0.95rem;
          }

          .title-underline {
            margin: 12px auto 16px;
          }

          .advantage-content {
            padding: 35px 30px 30px;
          }
        }

        @media (max-width: 576px) {
          .section-title {
            font-size: 1.8rem;
          }

          .section-subtitle {
            font-size: 0.9rem;
          }

          .title-underline {
            width: 40px;
            margin: 10px auto 16px;
          }

          .advantage-number {
            font-size: 3.5rem;
            top: 15px;
            right: 15px;
          }

          .advantage-content {
            padding: 30px 25px 25px;
          }

          .advantage-icon {
            width: 60px;
            height: 60px;
            font-size: 1.6rem;
            border-radius: 12px;
          }

          .advantage-title {
            font-size: 1.3rem;
          }
        }
      `}</style>
    </div>
  );
}
