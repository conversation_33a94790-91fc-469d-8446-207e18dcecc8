#!/bin/bash

echo "💰 超便宜香港服务器部署脚本"
echo "总成本: ¥24/月 + 免费CDN"
echo ""

echo "🔧 更新系统..."
apt update && apt upgrade -y

echo "🐳 安装Docker..."
curl -fsSL https://get.docker.com | sh
systemctl start docker
systemctl enable docker

echo "📦 安装Docker Compose..."
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

echo "🔥 安装Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

echo "🛡️ 配置防火墙..."
ufw allow 22
ufw allow 80  
ufw allow 443
ufw --force enable

echo "📁 创建项目目录..."
mkdir -p /var/www/ecommerce
cd /var/www/ecommerce

echo "✅ 服务器配置完成！"
echo ""
echo "📋 下一步操作:"
echo "1. 上传您的项目代码"
echo "2. 运行: docker-compose up -d"
echo "3. 配置Cloudflare CDN"
echo ""
echo "🌐 Cloudflare设置:"
echo "1. 访问 https://cloudflare.com"
echo "2. 添加您的域名"
echo "3. 修改DNS到Cloudflare"
echo "4. 开启CDN加速"
echo ""
echo "💰 总成本: ¥24/月 (节省95%费用)"
