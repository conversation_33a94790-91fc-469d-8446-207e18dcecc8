import { getDictionary } from '../../../utils/i18n';
import Link from 'next/link';
import { solutionPlaceholders } from '../../../utils/imagePlaceholder';
import ModernProductGrid from '../../../components/ModernProductGrid';

export default async function MediumSizePage({ params }: { params: { lang: string } }) {
  const { lang } = params;
  const dict = await getDictionary(lang);

  // 500-1000 sqm 产品数据
  const products = [
    {
      url: `/${lang}/products/730-sqm-customizable-pastel-style-indoor-playground-solution`,
      image: solutionPlaceholders[0],
      title:
        dict.solutions?.product1 || '730 SQM Customizable Pastel Style Indoor Playground Solution',
    },
    {
      url: `/${lang}/products/800-sqm-customizable-colorful-style-indoor-playground-solution`,
      image: solutionPlaceholders[5],
      title:
        dict.solutions?.product6 ||
        '800 SQM Customizable Colorful Style Indoor Playground Solution',
    },
    {
      url: `/${lang}/products/600-sqm-customizable-morandi-style-indoor-playground-solution`,
      image: solutionPlaceholders[6],
      title:
        dict.solutions?.product7 || '600 SQM Customizable Morandi Style Indoor Playground Solution',
    },
  ];

  return (
    <>
      <section className="page-banner">
        <div className="container">
          <h1 className="page-title">
            {dict.products?.categories?.medium || '500-1000 SQM Playgrounds'}
          </h1>
          <div className="breadcrumb">
            <Link href={`/${lang}`}>{dict.common?.home || 'Home'}</Link> /
            <Link href={`/${lang}/products`}>{dict.common?.products || 'Products'}</Link> /
            <span>{dict.products?.filter?.medium || '500-1000 sqm'}</span>
          </div>
        </div>
      </section>

      <section className="products-page">
        <div className="container">
          <div className="page-header">
            <p className="page-description">
              {dict.products?.medium?.description ||
                'Our 500-1000 sqm playground solutions offer the perfect balance of space and features, ideal for dedicated family entertainment centers and larger commercial venues. These mid-sized playgrounds provide diverse play experiences while maintaining operational efficiency.'}
            </p>
          </div>

          <div className="product-filters">
            <Link href={`/${lang}/products`} className="filter-item">
              {dict.products?.filter?.all || 'All Products'}
            </Link>
            <Link href={`/${lang}/products/indoor-playground`} className="filter-item">
              {dict.products?.filter?.indoor || 'Indoor Playgrounds'}
            </Link>
            <Link href={`/${lang}/products/trampoline-park`} className="filter-item">
              {dict.products?.filter?.trampoline || 'Trampoline Parks'}
            </Link>
          </div>

          <ModernProductGrid products={products} />

          <div className="size-benefits">
            <h2>
              {dict.products?.medium?.benefits?.title || 'Benefits of 500-1000 SQM Playgrounds'}
            </h2>
            <div className="benefits-grid">
              <div className="benefit-item">
                <div className="benefit-icon">
                  <i className="fas fa-users"></i>
                </div>
                <h3>{dict.products?.medium?.benefits?.capacity?.title || 'Optimal Capacity'}</h3>
                <p>
                  {dict.products?.medium?.benefits?.capacity?.description ||
                    'Accommodates more visitors simultaneously while maintaining a comfortable play environment.'}
                </p>
              </div>

              <div className="benefit-item">
                <div className="benefit-icon">
                  <i className="fas fa-puzzle-piece"></i>
                </div>
                <h3>{dict.products?.medium?.benefits?.variety?.title || 'Greater Play Variety'}</h3>
                <p>
                  {dict.products?.medium?.benefits?.variety?.description ||
                    'Space for more diverse play elements, themed zones, and specialized activities.'}
                </p>
              </div>

              <div className="benefit-item">
                <div className="benefit-icon">
                  <i className="fas fa-birthday-cake"></i>
                </div>
                <h3>
                  {dict.products?.medium?.benefits?.party?.title || 'Enhanced Party Facilities'}
                </h3>
                <p>
                  {dict.products?.medium?.benefits?.party?.description ||
                    'Room for multiple party areas, increasing revenue potential from celebrations and events.'}
                </p>
              </div>

              <div className="benefit-item">
                <div className="benefit-icon">
                  <i className="fas fa-balance-scale"></i>
                </div>
                <h3>{dict.products?.medium?.benefits?.balance?.title || 'Balanced Investment'}</h3>
                <p>
                  {dict.products?.medium?.benefits?.balance?.description ||
                    'Strikes the perfect balance between investment size and revenue potential for sustainable business.'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="cta-section cta-particles">
        <div className="container">
          <div className="cta-content">
            <h2>准备好讨论您的全息定制解决方案？</h2>
            <p>今天就联系我们的团队，探索我们如何为您的需求创造完美的全息解决方案。</p>
            <Link
              href={`/${lang}/pages/contact-us`}
              className="btn-primary"
              data-text="立即联系我们"
            >
              {'立即联系我们'.split('').map((char, index) => (
                <i key={index}>{char === ' ' ? '\u00A0' : char}</i>
              ))}
            </Link>
          </div>
        </div>
      </section>
    </>
  );
}
