<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        .api-result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 产品显示测试页面</h1>
        
        <div class="test-section">
            <h2>1. API 测试</h2>
            <button onclick="testAPI()">测试产品 API</button>
            <button onclick="testPublishedAPI()">测试已发布产品 API</button>
            <div id="api-status" class="status loading">点击按钮开始测试...</div>
            <div id="api-result" class="api-result"></div>
        </div>

        <div class="test-section">
            <h2>2. 产品显示测试</h2>
            <button onclick="loadProducts()">加载产品</button>
            <div id="product-status" class="status loading">点击按钮加载产品...</div>
            <div id="product-grid" class="product-grid"></div>
        </div>

        <div class="test-section">
            <h2>3. 页面链接测试</h2>
            <button onclick="window.open('/zh/products', '_blank')">打开产品页面</button>
            <button onclick="window.open('/products', '_blank')">测试重定向</button>
            <button onclick="window.open('/api/products?published=true', '_blank')">查看 API 响应</button>
        </div>
    </div>

    <script>
        async function testAPI() {
            const statusEl = document.getElementById('api-status');
            const resultEl = document.getElementById('api-result');
            
            statusEl.className = 'status loading';
            statusEl.textContent = '正在测试 API...';
            
            try {
                const response = await fetch('/api/products');
                const data = await response.json();
                
                statusEl.className = 'status success';
                statusEl.textContent = `✅ API 测试成功！返回 ${data.products?.length || 0} 个产品`;
                resultEl.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ API 测试失败: ${error.message}`;
                resultEl.textContent = error.stack;
            }
        }

        async function testPublishedAPI() {
            const statusEl = document.getElementById('api-status');
            const resultEl = document.getElementById('api-result');
            
            statusEl.className = 'status loading';
            statusEl.textContent = '正在测试已发布产品 API...';
            
            try {
                const response = await fetch('/api/products?published=true');
                const data = await response.json();
                
                statusEl.className = 'status success';
                statusEl.textContent = `✅ 已发布产品 API 测试成功！返回 ${data.products?.length || 0} 个产品`;
                resultEl.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ 已发布产品 API 测试失败: ${error.message}`;
                resultEl.textContent = error.stack;
            }
        }

        async function loadProducts() {
            const statusEl = document.getElementById('product-status');
            const gridEl = document.getElementById('product-grid');
            
            statusEl.className = 'status loading';
            statusEl.textContent = '正在加载产品...';
            gridEl.innerHTML = '';
            
            try {
                const response = await fetch('/api/products?published=true');
                const data = await response.json();
                
                if (data.success && data.products && data.products.length > 0) {
                    statusEl.className = 'status success';
                    statusEl.textContent = `✅ 成功加载 ${data.products.length} 个产品`;
                    
                    data.products.forEach(product => {
                        const card = document.createElement('div');
                        card.className = 'product-card';
                        card.innerHTML = `
                            <h3>${product.name}</h3>
                            <p><strong>ID:</strong> ${product.id}</p>
                            <p><strong>Slug:</strong> ${product.slug}</p>
                            <p><strong>描述:</strong> ${product.description}</p>
                            <p><strong>分类:</strong> ${product.category}</p>
                            <p><strong>已发布:</strong> ${product.is_published ? '是' : '否'}</p>
                            <p><strong>图片:</strong> ${product.image_url || '无'}</p>
                        `;
                        gridEl.appendChild(card);
                    });
                } else {
                    statusEl.className = 'status error';
                    statusEl.textContent = '❌ 没有找到产品';
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ 加载产品失败: ${error.message}`;
            }
        }

        // 页面加载时自动测试
        window.onload = function() {
            testPublishedAPI();
        };
    </script>
</body>
</html>
