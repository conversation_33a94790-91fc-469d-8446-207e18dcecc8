/* 首页轮播图样式优化 */
.hero-slider-wrapper {
  margin-top: -70px !important; /* 使用负边距，让轮播图向上移动 */
  position: relative !important;
  overflow: hidden !important;
  width: 100vw !important; /* 占据整个视口宽度 */
  max-width: 100vw !important;
  margin-left: calc(50% - 50vw) !important; /* 向左延伸到屏幕边缘 */
  margin-right: calc(50% - 50vw) !important; /* 向右延伸到屏幕边缘 */
  left: 0 !important;
  right: 0 !important;
  padding-top: 70px !important; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

.hero-slider {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* 轮播图内容样式优化 */
.hero-slider .slide-content {
  padding-top: 50px !important; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
  z-index: 5 !important; /* 确保内容在最上层 */
}

/* 轮播图导航按钮样式优化 */
.hero-slider .arrow {
  z-index: 10 !important; /* 确保导航按钮在最上层 */
}

/* 轮播图指示器样式优化 */
.hero-slider .slider-dots {
  bottom: 20px !important; /* 调整指示器位置 */
  z-index: 10 !important; /* 确保指示器在最上层 */
}
