<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .search-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 10px;
        }
        .search-input:focus {
            border-color: #3b82f6;
            outline: none;
        }
        .tag-button {
            display: inline-block;
            padding: 6px 12px;
            margin: 4px;
            background: #f0f0f0;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
        }
        .tag-button:hover {
            background: #e0e0e0;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .test-section {
            margin-bottom: 30px;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #3b82f6;
            padding-bottom: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>产品搜索功能测试</h1>
    
    <div class="test-container">
        <div class="test-section">
            <h2>1. 热门搜索标签测试</h2>
            <p>测试热门搜索标签是否可以正常点击而不会消失：</p>
            
            <div class="search-container">
                <input type="text" class="search-input" id="searchInput" placeholder="搜索产品名称、型号或关键词...">
                <div id="suggestions" style="display: none; margin-top: 10px;">
                    <div style="font-size: 14px; color: #666; margin-bottom: 8px;">热门搜索</div>
                    <div>
                        <button class="tag-button" onclick="selectTag('互动投影')">互动投影</button>
                        <button class="tag-button" onclick="selectTag('数字沙盘')">数字沙盘</button>
                        <button class="tag-button" onclick="selectTag('全息投影')">全息投影</button>
                        <button class="tag-button" onclick="selectTag('AR体验')">AR体验</button>
                        <button class="tag-button" onclick="selectTag('VR设备')">VR设备</button>
                    </div>
                </div>
            </div>
            
            <div id="searchResults" class="results" style="display: none;">
                <strong>搜索结果：</strong>
                <div id="resultText"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>2. API搜索测试</h2>
            <p>测试搜索API是否正常工作：</p>
            
            <button onclick="testSearchAPI('互动投影')" class="tag-button">测试搜索"互动投影"</button>
            <button onclick="testSearchAPI('数字沙盘')" class="tag-button">测试搜索"数字沙盘"</button>
            <button onclick="testSearchAPI('不存在的产品')" class="tag-button">测试搜索"不存在的产品"</button>
            
            <div id="apiResults" class="results" style="display: none;">
                <strong>API测试结果：</strong>
                <div id="apiResultText"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>3. 测试状态</h2>
            <div id="testStatus">
                <div class="status">准备开始测试...</div>
            </div>
        </div>
    </div>

    <script>
        const searchInput = document.getElementById('searchInput');
        const suggestions = document.getElementById('suggestions');
        const searchResults = document.getElementById('searchResults');
        const resultText = document.getElementById('resultText');
        const apiResults = document.getElementById('apiResults');
        const apiResultText = document.getElementById('apiResultText');
        const testStatus = document.getElementById('testStatus');

        // 显示/隐藏建议
        searchInput.addEventListener('focus', () => {
            if (!searchInput.value) {
                suggestions.style.display = 'block';
            }
        });

        searchInput.addEventListener('blur', (e) => {
            // 延迟隐藏，让点击事件有时间触发
            setTimeout(() => {
                suggestions.style.display = 'none';
            }, 200);
        });

        searchInput.addEventListener('input', (e) => {
            if (e.target.value) {
                suggestions.style.display = 'none';
                performSearch(e.target.value);
            } else {
                suggestions.style.display = 'block';
                searchResults.style.display = 'none';
            }
        });

        // 选择标签
        function selectTag(tag) {
            searchInput.value = tag;
            suggestions.style.display = 'none';
            performSearch(tag);
            updateTestStatus('success', `成功选择热门搜索标签: ${tag}`);
        }

        // 执行搜索
        function performSearch(query) {
            searchResults.style.display = 'block';
            resultText.innerHTML = `搜索关键词: "${query}"<br>模拟搜索结果...`;
            updateTestStatus('success', `搜索功能正常工作，查询: ${query}`);
        }

        // 测试搜索API
        async function testSearchAPI(query) {
            try {
                updateTestStatus('info', `正在测试API搜索: ${query}`);
                
                const response = await fetch(`/api/products?search=${encodeURIComponent(query)}&published=true&limit=5`);
                const data = await response.json();
                
                apiResults.style.display = 'block';
                
                if (response.ok) {
                    apiResultText.innerHTML = `
                        <strong>搜索成功!</strong><br>
                        查询: "${query}"<br>
                        找到产品数量: ${data.products ? data.products.length : 0}<br>
                        总数: ${data.total || 0}<br>
                        状态: ${response.status}
                    `;
                    updateTestStatus('success', `API搜索测试成功: ${query}`);
                } else {
                    apiResultText.innerHTML = `
                        <strong>搜索失败</strong><br>
                        状态码: ${response.status}<br>
                        错误: ${data.error || '未知错误'}
                    `;
                    updateTestStatus('error', `API搜索测试失败: ${response.status}`);
                }
            } catch (error) {
                apiResults.style.display = 'block';
                apiResultText.innerHTML = `
                    <strong>网络错误</strong><br>
                    错误信息: ${error.message}
                `;
                updateTestStatus('error', `API测试网络错误: ${error.message}`);
            }
        }

        // 更新测试状态
        function updateTestStatus(type, message) {
            const statusClass = type === 'success' ? 'success' : type === 'error' ? 'error' : 'status';
            const timestamp = new Date().toLocaleTimeString();
            
            testStatus.innerHTML += `
                <div class="${statusClass}">
                    [${timestamp}] ${message}
                </div>
            `;
            
            // 滚动到底部
            testStatus.scrollTop = testStatus.scrollHeight;
        }

        // 初始化测试状态
        updateTestStatus('info', '测试页面已加载，可以开始测试搜索功能');
    </script>
</body>
</html>
