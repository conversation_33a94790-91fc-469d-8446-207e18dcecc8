import { i18n } from '../utils/i18n';
import { redirect } from 'next/navigation';
import { HtmlLangSetter } from '../components/HtmlLangSetter';
import '../styles/globals.css';
import '../styles/global-quote-form.css';

export default function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { lang: string };
}) {
  // Validate that the locale is supported
  if (!i18n.locales.includes(params.lang)) {
    redirect(`/${i18n.defaultLocale}`);
  }

  return (
    <>
      {/* Client component to set HTML lang attribute */}
      <HtmlLangSetter lang={params.lang} />
      {children}
    </>
  );
}

// Generate static params for all supported locales
export function generateStaticParams() {
  return i18n.locales.map(locale => ({ lang: locale }));
}
