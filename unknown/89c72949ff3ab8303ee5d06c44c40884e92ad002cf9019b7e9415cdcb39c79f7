import { Html, Head, Main, NextScript } from 'next/document';

/**
 * Custom Document component
 * This is needed for Next.js to properly handle the document structure
 * It's referenced by the error: Cannot find module '.next/server/pages/_document.js'
 */
export default function Document() {
  return (
    <Html lang="en" translate="no" className="dark" data-theme="dark">
      <Head>
        {/* 增强Microsoft Translator阻止功能 */}
        <meta name="translate" content="no" />
        <meta name="microsoft-translate-skip" content="true" />
        <meta name="format-detection" content="telephone=no,date=no,address=no,email=no,url=no" />
        <meta name="ms-translate-skip" content="true" />
        <meta name="no-translate" content="true" />
        <meta name="google" content="notranslate" />

        {/* 高对比度模式相关 - 使用现代标准但不强制启用 */}
        <meta name="color-scheme" content="dark" />
        
        {/* 添加高对比度修复样式表 */}
        <link rel="stylesheet" href="/styles/high-contrast-fix.css" />
        <link rel="stylesheet" href="/styles/modern-contrast-fix.css" />

        {/* 添加深色主题样式 */}
        <style
          dangerouslySetInnerHTML={{
            __html: `
            /* 使用深色主题 */
            :root {
              color-scheme: dark !important;
            }
            
            /* 阻止所有Microsoft Translator添加的属性 */
            [_msttexthash],
            [_msthidden],
            [_msthash],
            [_mstvisible],
            [_mstlang],
            [_mstaltindent],
            [_mstalt],
            [_mstwidth],
            [_msthiddenattr],
            [_mstplaceholder] {
              _msttexthash: initial !important;
              _msthidden: initial !important;
              _msthash: initial !important;
              _mstvisible: initial !important;
              _mstlang: initial !important;
              _mstaltindent: initial !important;
              _mstalt: initial !important;
              _mstwidth: initial !important;
              _msthiddenattr: initial !important;
              _mstplaceholder: initial !important;
            }
            
            /* 只在高对比度模式下应用特殊样式，不影响正常深色模式 */
            @media (forced-colors: active) {
              a, button, input, select, textarea, [role="button"], .btn {
                forced-color-adjust: auto;
              }
            }
          `,
          }}
        />

        {/* 预加载修复脚本 */}
        <link rel="preload" href="/js/high-contrast-fix.js" as="script" />
        <link rel="preload" href="/js/ultimate-high-contrast-fixer.js" as="script" />

        {/* 内联脚本，确保修复器尽早启动 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
            // 立即加载修复脚本
            (function() {
              // 加载高对比度修复脚本
              var highContrastFixScript = document.createElement('script');
              highContrastFixScript.src = '/js/high-contrast-fix.js';
              highContrastFixScript.async = false; // 同步加载确保尽早执行
              document.head.appendChild(highContrastFixScript);
              
              // 创建并添加终极修复脚本
              var fixerScript = document.createElement('script');
              fixerScript.src = '/js/ultimate-high-contrast-fixer.js';
              fixerScript.async = false; // 同步加载确保尽早执行
              document.head.appendChild(fixerScript);
              
              // 端口检测和重定向脚本
              if (window.location.port === '3000' && window.location.pathname.includes('/admin/login')) {
                console.log('检测到端口3000的登录请求，尝试重定向到端口3001...');
                var newUrl = window.location.href.replace(':3000', ':3001');
                window.location.href = newUrl;
              }
              
              // 启用深色模式
              document.documentElement.classList.add('dark');
              document.documentElement.classList.remove('light');
              document.body && document.body.classList.add('dark', 'dark-mode');
              document.body && document.body.classList.remove('light', 'light-mode');
              document.documentElement.dataset.theme = 'dark';
              document.documentElement.dataset.mode = 'dark';
            })();
          `,
          }}
        />
      </Head>
      <body translate="no" className="dark" data-theme="dark">
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
