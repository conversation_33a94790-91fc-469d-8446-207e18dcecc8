/**
 * Custom App component
 * This is needed for Next.js to properly handle the application structure
 * It works together with _document.js to provide the base structure
 */
import { SessionProvider } from 'next-auth/react';
import PortRedirector from '../components/PortRedirector';
import ApiPortFixer from '../components/ApiPortFixer';
import DbConnectionMonitor from '../components/DbConnectionMonitor';
import FloatingCacheCleaner from '../components/FloatingCacheCleaner';
import HighContrastFixer from '../components/HighContrastFixer';

export default function MyApp({ Component, pageProps }) {
  return (
    <SessionProvider session={pageProps.session}>
      <HighContrastFixer />
      <PortRedirector />
      <ApiPortFixer />
      <DbConnectionMonitor />
      <FloatingCacheCleaner onlyInDev={true} />
      <Component {...pageProps} />
    </SessionProvider>
  );
}
