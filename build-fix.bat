@echo off
echo 🔧 修复构建错误并重新构建
echo.

echo 📋 已修复的问题:
echo ✅ test-products页面 - 改用fetch加载JSON
echo ✅ check-database API - 禁用数据库依赖
echo.

echo 🔨 开始构建...
call npm run build

if errorlevel 1 (
    echo.
    echo ❌ 构建仍然失败
    echo 💡 可能的解决方案:
    echo 1. 删除有问题的文件
    echo 2. 使用Vercel部署 (推荐)
    echo 3. 创建最小化版本
    echo.
    
    set /p choice=选择解决方案 (1-3): 
    
    if "%choice%"=="1" (
        echo 🗑️ 删除有问题的文件...
        if exist "app\test-products" rmdir /s /q "app\test-products"
        if exist "app\api\admin\check-database" rmdir /s /q "app\api\admin\check-database"
        echo ✅ 已删除问题文件，请重新运行构建
    )
    
    if "%choice%"=="2" (
        echo 🚀 推荐使用Vercel部署
        echo 1. 访问 https://vercel.com
        echo 2. 拖拽项目文件夹
        echo 3. 自动部署
        start https://vercel.com
    )
    
    if "%choice%"=="3" (
        echo 📦 创建最小化版本...
        call create-minimal-version.bat
    )
    
) else (
    echo.
    echo ✅ 构建成功！
    echo 📁 检查out目录...
    if exist "out" (
        dir out
        echo.
        echo 🚀 现在可以部署到Netlify:
        echo 1. 拖拽 'out' 文件夹到Netlify
        echo 2. 等待部署完成
        echo 3. 测试网站功能
    ) else (
        echo ❌ out目录不存在，构建可能有问题
    )
)

echo.
pause
