import type { Metadata } from 'next';
import { Poppins } from 'next/font/google';
import './styles/globals.css';
import './styles/header-dark.css';
// Temporarily commenting out CSS files that require Tailwind until we fix the dependency issue
// import './styles/services.css'
import './styles/products.css';
import './styles/quality-control.css';
// import './styles/feature-cards.css'
// import './styles/design-process.css'
// import './styles/purchase-guide.css'
// import './styles/marketing-support.css'
import './styles/banner-fix.css';
import './styles/ms-fix.css'; // 添加修复-ms-high-contrast警告的CSS
import './styles/high-contrast-override.css'; // 强力覆盖所有第三方库的-ms-high-contrast媒体查询
import './styles/ms-high-contrast-blocker.css'; // 完全阻止所有-ms-high-contrast媒体查询
import './styles/ms-high-contrast-killer.css'; // 终极解决方案，彻底消除-ms-high-contrast警告
// import './styles/modern-contrast-fix.css'; // 使用现代forced-colors替代-ms-high-contrast
import './styles/ms-translator-blocker.css'; // 添加微软翻译器阻止器CSS
import './styles/product-detail-fix.css'; // 添加产品详情页样式修复（最新版本）
import './styles/hero.css'; // 添加hero样式文件
import './styles/home-page.css'; // 添加首页样式文件
import './styles/home-page-fix.css'; // 添加首页轮播图修复样式
import './styles/custom-overrides.css'; // 添加自定义样式覆盖，隐藏不需要的图标
import './styles/global-quote-form.css'; // 添加全局引用表单样式，解决样式闪烁问题
import './styles/loading-fix.css'; // 添加页面加载过程中的样式修复
import './styles/top-space-fix.css'; // 添加修复页面顶部空白的样式
import './styles/custom-solutions.css'; // 最后导入以确保覆盖home-page.css中的.bg-light样式
import './styles/unified-cta.css'; // 导入统一CTA样式，包含动画按钮效果
import './styles/holographic-guide.css'; // 全息投影购买指南页面样式
import './styles/holographic-guide-override.css'; // 全息投影购买指南页面专用样式覆盖 - 最高优先级
import './styles/custom-playground-design.css'; // 项目定制指南页面样式
import './styles/animations.css'; // 导入动画样式文件
import { generateFavicon } from './favicon';
// import dynamic from 'next/dynamic'
import { LanguageProvider } from './components/LanguageProvider';
import HydrationErrorBoundary from './components/HydrationErrorBoundary';
// 导入条件布局组件
import ConditionalLayout from './components/ConditionalLayout';
// 导入HighContrastFixer组件，用于消除-ms-high-contrast警告
import HighContrastFixer from './components/HighContrastFixer';
// 导入LoadingFix组件，用于解决页面加载过程中的样式重叠问题
import LoadingFix from './components/LoadingFix';

// 移除客户端专用的useEffect导入
// import { useEffect } from 'react'
import { initPreload } from './preload';

// Initialize Poppins font
const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  display: 'swap',
  variable: '--font-poppins',
});

// 移除原来的动态导入
// const Header = dynamic(() => import('./components/Header'), { ssr: false })

export const metadata: Metadata = {
  title: 'Guangzhou Junsheng - 3D Holographic & Immersive Projection Solutions',
  description:
    'Professional provider of 3D holographic projection, naked-eye 5D interactive projection, immersive restaurants, and tech exhibition hall solutions.',
  icons: {
    icon: '/favicon.ico',
  },
  // 添加元数据以阻止Microsoft Translator
  other: {
    translate: 'no',
    'microsoft-translate-skip': 'true',
    'format-detection': 'telephone=no,date=no,address=no,email=no,url=no',
    'ms-translate-skip': 'true',
    'no-translate': 'true',
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
};

// 创建一个客户端组件包装预加载逻辑
import ClientPreload from './components/ClientPreload';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  const faviconUrl = generateFavicon();

  return (
    <html
      lang="en"
      suppressHydrationWarning
      className={`${poppins.variable} font-sans`}
      translate="no"
    >
      <head>
        {/* 预加载关键字体 */}
        <link
          rel="preload"
          href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
          as="style"
        />
        <link
          rel="stylesheet"
          href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
        />
        {/* 预加载关键图片 - 使用实际存在的图片 */}
        <link rel="preload" href="/images/junsheng-logo-1.png" as="image" type="image/png" />

        {/* 添加阻止Microsoft Translator的meta标签 */}
        <meta name="translate" content="no" />
        <meta name="microsoft-translate-skip" content="true" />
        <meta name="format-detection" content="telephone=no,date=no,address=no,email=no,url=no" />
        <meta name="ms-translate-skip" content="true" />
        <meta name="no-translate" content="true" />
        <meta name="google" content="notranslate" />

        {/* 添加现代颜色模式meta标签 */}
        <meta name="color-scheme" content="light dark" />

        {/* 先加载脚本以确保尽早运行 */}
        <script src="/js/ms-high-contrast-blocker.js" async={false}></script>
        <script src="/js/ms-high-contrast-remover.js" async defer></script>

        <link rel="icon" href={faviconUrl} />
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
      </head>
      <body className={poppins.className} translate="no">
        {/* Add a hidden element to help with CSS specificity for -ms-high-contrast blocking */}
        <div id="ms-high-contrast-blocker" aria-hidden="true" style={{ display: 'none' }}></div>
        {/* 添加HighContrastFixer组件，用于消除-ms-high-contrast警告 */}
        <HighContrastFixer />
        <LoadingFix />
        <LanguageProvider>
          <HydrationErrorBoundary>
            <ClientPreload />
            {/* 使用条件布局组件，根据路径决定是否显示主网站组件 */}
            <ConditionalLayout>
              {children}
            </ConditionalLayout>
          </HydrationErrorBoundary>
        </LanguageProvider>
      </body>
    </html>
  );
}
