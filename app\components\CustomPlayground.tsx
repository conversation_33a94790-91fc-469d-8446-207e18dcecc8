'use client';

import { useRef, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { generatePlaceholderSVG } from '../utils/imagePlaceholder';
import { useLanguage } from './LanguageProvider';

export default function CustomPlayground() {
  const { locale, t } = useLanguage();
  const sectionRef = useRef<HTMLDivElement>(null);

  // 背景图片占位符
  const bgImage = generatePlaceholderSVG(1920, 1080, 'Holographic Background', '#0e1224');

  // 加载图标字体
  useEffect(() => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css';
    document.head.appendChild(link);

    return () => {
      document.head.removeChild(link);
    };
  }, []);

  // 添加滚动动画
  useEffect(() => {
    const handleScroll = () => {
      const section = sectionRef.current;
      if (!section) return;

      const sectionTop = section.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;

      if (sectionTop < windowHeight * 0.75) {
        section.classList.add('visible');
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // 初始检查

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // 定义服务项目
  const services = [
    {
      icon: 'fa-wand-magic-sparkles',
      title: t('custom_playground.services.creative.title', { fallback: '创意策划' }),
      description: t('custom_playground.services.creative.description', {
        fallback: '专业创意团队根据品牌特性定制全息内容与视觉效果',
      }),
    },
    {
      icon: 'fa-drafting-compass',
      title: t('custom_playground.services.spatial.title', { fallback: '空间设计' }),
      description: t('custom_playground.services.spatial.description', {
        fallback: '根据场地环境和用途优化投影布局与互动体验设计',
      }),
    },
    {
      icon: 'fa-cube',
      title: t('custom_playground.services.content.title', { fallback: '3D内容制作' }),
      description: t('custom_playground.services.content.description', {
        fallback: '专业3D建模团队打造高品质全息投影内容与视觉效果',
      }),
    },
    {
      icon: 'fa-tools',
      title: t('custom_playground.services.installation.title', { fallback: '专业安装' }),
      description: t('custom_playground.services.installation.description', {
        fallback: '经验丰富的工程团队确保设备精准安装和系统调试',
      }),
    },
  ];

  return (
    <div className="custom-solution-wrapper" ref={sectionRef}>
      <div className="floating-particle particle-1"></div>
      <div className="floating-particle particle-2"></div>
      <div className="floating-particle particle-3"></div>

      <div className="custom-solution-container">
        <div className="content-wrapper">
          <div className="text-content">
            <div className="section-badge">
              <span>{t('custom_playground.badge', { fallback: '专属定制' })}</span>
            </div>

            <h2 className="section-title">
              <span className="thin-text">
                {t('custom_playground.title_prefix', { fallback: '专属' })}{' '}
              </span>
              <strong>{t('custom_playground.title_main', { fallback: '全息解决方案' })}</strong>
            </h2>

            <div className="section-divider"></div>

            <p className="section-description">
              {t('custom_playground.description', {
                fallback:
                  '为您的品牌、场所或活动量身定制创新的全息投影解决方案，从创意策划到实施交付，提供一站式服务。我们专注于打造独特的视觉体验，为您的项目增添科技感与未来感。',
              })}
            </p>

            <div className="benefits-grid">
              {services.map((service, index) => (
                <div className="benefit-item" key={index}>
                  <div className="benefit-icon">
                    <i className={`fas ${service.icon}`}></i>
                  </div>
                  <div className="benefit-text">
                    <h3>{service.title}</h3>
                    <p>{service.description}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="solution-cta">
              <Link
                href={`/${locale}/pages/custom-solutions`}
                className="btn-primary"
                data-text={t('custom_playground.cta_button', { fallback: '获取定制方案' })}
                suppressHydrationWarning
              >
                <span suppressHydrationWarning>
                  {t('custom_playground.cta_button', { fallback: '获取定制方案' }).split('').map((char, index) => (
                    <i key={index}>{char === ' ' ? '\u00A0' : char}</i>
                  ))}
                </span>
              </Link>

              <Link
                href={`/${locale}/pages/contact-us`}
                className="btn-primary"
                data-text={t('custom_playground.contact_button', { fallback: '联系我们' })}
                style={{ marginLeft: '20px' }}
                suppressHydrationWarning
              >
                <span suppressHydrationWarning>
                  {t('custom_playground.contact_button', { fallback: '联系我们' }).split('').map((char, index) => (
                    <i key={index}>{char === ' ' ? '\u00A0' : char}</i>
                  ))}
                </span>
              </Link>
            </div>
          </div>

          <div className="visual-element">
            <div
              className="solution-image"
              style={{ position: 'relative', width: '100%', aspectRatio: '16/9' }}
            >
              <Image
                src="/images/solutions/custom-solution.jpg"
                alt={t('custom_playground.image_alt', { fallback: '定制全息解决方案' })}
                fill
                style={{ objectFit: 'cover' }}
                onError={e => {
                  const target = e.target as HTMLImageElement;
                  if (target.src !== bgImage) {
                    target.src = bgImage;
                  }
                }}
                loading="lazy"
                sizes="100vw"
              />
              <div className="image-overlay"></div>
              <div className="image-decoration"></div>
            </div>

            <div className="overlay-text">
              <div className="solution-badge">
                <span>{t('custom_playground.overlay.badge', { fallback: '高端定制' })}</span>
              </div>
              <h3>{t('custom_playground.overlay.title', { fallback: '专业全息解决方案' })}</h3>
              <div className="overlay-divider"></div>
              <p>
                {t('custom_playground.overlay.description', {
                  fallback: '高端定制 · 视觉震撼 · 互动体验',
                })}
              </p>
            </div>

            <div className="experience-tag">
              <span className="tag-number">
                10<span className="plus">+</span>
              </span>
              <span className="tag-text">
                {t('custom_playground.experience_years', { fallback: '年行业经验' })}
              </span>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .custom-solution-wrapper {
          background: linear-gradient(135deg, #0e1224, #080b17);
          padding: 0;
          color: white;
          opacity: 0;
          transform: translateY(30px);
          transition: all 1s cubic-bezier(0.19, 1, 0.22, 1);
          position: relative;
          overflow: hidden;
        }

        .custom-solution-wrapper.visible {
          opacity: 1;
          transform: translateY(0);
        }

        .floating-particle {
          position: absolute;
          border-radius: 50%;
          background: radial-gradient(circle, rgba(26, 26, 46, 0.15) 0%, rgba(26, 26, 46, 0) 70%);
          animation: float 20s infinite linear;
          z-index: 1;
        }

        .particle-1 {
          width: 400px;
          height: 400px;
          top: -200px;
          right: 10%;
          animation-duration: 30s;
        }

        .particle-2 {
          width: 300px;
          height: 300px;
          bottom: -100px;
          left: 5%;
          animation-duration: 25s;
          animation-delay: 2s;
        }

        .particle-3 {
          width: 200px;
          height: 200px;
          top: 30%;
          right: -100px;
          animation-duration: 20s;
          animation-delay: 5s;
        }

        @keyframes float {
          0% {
            transform: translate(0, 0) rotate(0deg);
          }
          25% {
            transform: translate(-20px, 20px) rotate(90deg);
          }
          50% {
            transform: translate(20px, 30px) rotate(180deg);
          }
          75% {
            transform: translate(10px, -20px) rotate(270deg);
          }
          100% {
            transform: translate(0, 0) rotate(360deg);
          }
        }

        .custom-solution-container {
          max-width: 1240px;
          margin: 0 auto;
          padding: 120px 20px;
          position: relative;
          z-index: 2;
        }

        .content-wrapper {
          display: grid;
          grid-template-columns: 1.1fr 0.9fr;
          gap: 70px;
          align-items: center;
        }

        .text-content {
          padding-right: 20px;
        }

        .section-badge {
          display: inline-block;
          padding: 8px 16px;
          border-radius: 100px;
          background-color: rgba(26, 26, 46, 0.1);
          color: #9797aa;
          font-size: 0.85rem;
          font-weight: 500;
          margin-bottom: 24px;
          letter-spacing: 0.5px;
          backdrop-filter: blur(5px);
        }

        .section-title {
          font-size: 42px;
          margin-bottom: 24px;
          line-height: 1.2;
          color: #fff;
        }

        .thin-text {
          font-weight: 200;
          opacity: 0.85;
        }

        .section-title strong {
          font-weight: 600;
        }

        .section-divider {
          width: 60px;
          height: 3px;
          background: linear-gradient(90deg, #1a1a2e, rgba(26, 26, 46, 0.3));
          margin: 30px 0;
        }

        .section-description {
          font-size: 1.05rem;
          line-height: 1.8;
          margin-bottom: 40px;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 300;
          max-width: 90%;
        }

        .benefits-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 30px;
          margin-bottom: 50px;
        }

        .benefit-item {
          display: flex;
          gap: 20px;
          transition: transform 0.4s cubic-bezier(0.19, 1, 0.22, 1);
        }

        .benefit-item:hover {
          transform: translateY(-5px);
        }

        .benefit-icon {
          width: 50px;
          height: 50px;
          background: rgba(26, 26, 46, 0.15);
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.2rem;
          color: #9797aa;
          flex-shrink: 0;
          position: relative;
          overflow: hidden;
          transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
        }

        .benefit-item:hover .benefit-icon {
          background: #1a1a2e;
          color: white;
          box-shadow: 0 10px 20px rgba(26, 26, 46, 0.3);
        }

        .benefit-icon::before {
          content: '';
          position: absolute;
          width: 100%;
          height: 100%;
          background: radial-gradient(
            circle at center,
            rgba(255, 255, 255, 0.8) 0%,
            transparent 60%
          );
          opacity: 0;
          transform: scale(0);
          transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
        }

        .benefit-item:hover .benefit-icon::before {
          opacity: 0.2;
          transform: scale(2);
        }

        .benefit-text {
          flex: 1;
        }

        .benefit-text h3 {
          font-size: 1.1rem;
          font-weight: 500;
          margin-bottom: 8px;
          color: white;
        }

        .benefit-text p {
          font-size: 0.9rem;
          line-height: 1.6;
          color: rgba(255, 255, 255, 0.7);
          font-weight: 300;
        }

        .solution-cta {
          display: flex;
          gap: 20px;
          margin-top: 20px;
        }

        .custom-btn {
          position: relative;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          min-width: 220px;
          padding: 14px 28px;
          font-size: 0.95rem;
          font-weight: 600;
          text-decoration: none;
          text-transform: none;
          color: #fff;
          background: linear-gradient(135deg, #264eca 0%, #2239b4 100%);
          border: none;
          border-radius: 8px;
          box-shadow: 0 10px 20px rgba(38, 78, 202, 0.3);
          overflow: hidden;
          cursor: pointer;
          transition: all 0.4s ease;
          z-index: 1;
        }

        .custom-btn:hover {
          transform: translateY(-5px);
          box-shadow: 0 15px 30px rgba(38, 78, 202, 0.4);
        }

        .custom-btn:active {
          transform: translateY(-2px);
        }

        .btn-glow {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0) 10%,
            rgba(255, 255, 255, 0.2) 20%,
            rgba(255, 255, 255, 0) 30%
          );
          z-index: -1;
          transform: translateX(-100%);
          animation: btn-glow-animation 3s infinite;
        }

        @keyframes btn-glow-animation {
          0% {
            transform: translateX(-100%);
          }
          100% {
            transform: translateX(100%);
          }
        }

        .btn-text-wrapper {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          transition: all 0.3s ease;
        }

        .btn-icon-left {
          margin-right: 14px;
          font-size: 1.1rem;
          transition: transform 0.3s ease;
        }

        .btn-icon-right {
          margin-left: 14px;
          font-size: 1.1rem;
          transition: transform 0.3s ease;
        }

        .custom-btn:hover .btn-icon-left {
          transform: translateX(-3px) rotate(-10deg);
        }

        .custom-btn:hover .btn-icon-right {
          transform: translateX(3px);
        }

        .custom-btn.outline {
          background: transparent;
          border: 2px solid rgba(255, 255, 255, 0.3);
          box-shadow: none;
        }

        .custom-btn.outline:hover {
          border-color: rgba(255, 255, 255, 0.6);
          background: rgba(255, 255, 255, 0.08);
        }

        .visual-element {
          position: relative;
        }

        .solution-image {
          position: relative;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
          transform: perspective(1000px) rotateY(-5deg);
          transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
        }

        .custom-solution-wrapper.visible .solution-image {
          transform: perspective(1000px) rotateY(0deg);
        }

        .solution-image img {
          width: 100%;
          display: block;
          height: auto;
          object-fit: cover;
          transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
        }

        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(45deg, rgba(26, 26, 46, 0.3), rgba(14, 18, 36, 0.3));
          mix-blend-mode: multiply;
          pointer-events: none;
        }

        .image-decoration {
          position: absolute;
          width: 150px;
          height: 150px;
          border: 2px solid rgba(26, 26, 46, 0.3);
          border-radius: 12px;
          top: -15px;
          right: -15px;
          z-index: -1;
        }

        .overlay-text {
          position: absolute;
          bottom: 30px;
          left: 30px;
          z-index: 10;
          max-width: 80%;
        }

        .solution-badge {
          display: inline-block;
          padding: 6px 12px;
          background: rgba(26, 26, 46, 0.2);
          backdrop-filter: blur(5px);
          border-radius: 100px;
          font-size: 0.8rem;
          margin-bottom: 15px;
          color: white;
          font-weight: 500;
        }

        .overlay-text h3 {
          font-size: 1.5rem;
          font-weight: 600;
          margin-bottom: 10px;
          color: white;
          text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .overlay-divider {
          width: 40px;
          height: 2px;
          background: white;
          margin: 15px 0;
        }

        .overlay-text p {
          font-size: 0.9rem;
          color: rgba(255, 255, 255, 0.9);
          font-weight: 300;
        }

        .experience-tag {
          position: absolute;
          top: 30px;
          right: -15px;
          background: #1a1a2e;
          color: white;
          padding: 20px;
          border-radius: 12px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          box-shadow: 0 15px 30px rgba(26, 26, 46, 0.3);
          transform: translateX(100px);
          opacity: 0;
          transition: all 0.8s cubic-bezier(0.19, 1, 0.22, 1);
          transition-delay: 0.5s;
        }

        .custom-solution-wrapper.visible .experience-tag {
          transform: translateX(0);
          opacity: 1;
        }

        .tag-number {
          font-size: 2.5rem;
          font-weight: 700;
          line-height: 1;
        }

        .plus {
          font-size: 1.2rem;
          font-weight: 500;
          vertical-align: top;
        }

        .tag-text {
          font-size: 0.8rem;
          font-weight: 500;
          margin-top: 5px;
        }

        @media (max-width: 1200px) {
          .custom-solution-container {
            padding: 100px 20px;
          }

          .content-wrapper {
            gap: 50px;
          }

          .section-title {
            font-size: 2.5rem;
          }
        }

        @media (max-width: 992px) {
          .content-wrapper {
            grid-template-columns: 1fr;
            gap: 60px;
          }

          .text-content {
            padding-right: 0;
          }

          .section-title {
            font-size: 2.3rem;
          }

          .section-description {
            max-width: 100%;
          }

          .visual-element {
            max-width: 600px;
            margin: 0 auto;
          }
        }

        @media (max-width: 768px) {
          .custom-solution-container {
            padding: 80px 20px;
          }

          .benefits-grid {
            grid-template-columns: 1fr;
            gap: 25px;
          }

          .section-title {
            font-size: 2.1rem;
          }

          .solution-cta {
            flex-direction: column;
            gap: 15px;
          }

          .custom-btn {
            width: 100%;
          }
        }

        @media (max-width: 576px) {
          .custom-solution-container {
            padding: 60px 15px;
          }

          .section-title {
            font-size: 1.8rem;
          }

          .section-description {
            font-size: 1rem;
          }

          .overlay-text {
            bottom: 20px;
            left: 20px;
          }

          .overlay-text h3 {
            font-size: 1.3rem;
          }

          .experience-tag {
            padding: 15px;
            right: 0;
            top: 20px;
          }

          .tag-number {
            font-size: 2rem;
          }
        }
      `}</style>
    </div>
  );
}
