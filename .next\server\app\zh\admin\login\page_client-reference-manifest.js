globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/zh/admin/login/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/not-found.tsx":{"*":{"id":"(ssr)/./app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/ClientPreload.tsx":{"*":{"id":"(ssr)/./app/components/ClientPreload.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/ConditionalLayout.tsx":{"*":{"id":"(ssr)/./app/components/ConditionalLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/HighContrastFixer.tsx":{"*":{"id":"(ssr)/./app/components/HighContrastFixer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/HydrationErrorBoundary.tsx":{"*":{"id":"(ssr)/./app/components/HydrationErrorBoundary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/LanguageProvider.tsx":{"*":{"id":"(ssr)/./app/components/LanguageProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/components/LoadingFix.tsx":{"*":{"id":"(ssr)/./app/components/LoadingFix.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/zh/admin/providers.tsx":{"*":{"id":"(ssr)/./app/zh/admin/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/zh/admin/page.tsx":{"*":{"id":"(ssr)/./app/zh/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/zh/admin/login/page.tsx":{"*":{"id":"(ssr)/./app/zh/admin/login/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\not-found.tsx":{"id":"(app-pages-browser)/./app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\components\\ClientPreload.tsx":{"id":"(app-pages-browser)/./app/components/ClientPreload.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\components\\ConditionalLayout.tsx":{"id":"(app-pages-browser)/./app/components/ConditionalLayout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\components\\HighContrastFixer.tsx":{"id":"(app-pages-browser)/./app/components/HighContrastFixer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\components\\HydrationErrorBoundary.tsx":{"id":"(app-pages-browser)/./app/components/HydrationErrorBoundary.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\components\\LanguageProvider.tsx":{"id":"(app-pages-browser)/./app/components/LanguageProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\components\\LoadingFix.tsx":{"id":"(app-pages-browser)/./app/components/LoadingFix.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"display\":\"swap\",\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"display\":\"swap\",\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\header-dark.css":{"id":"(app-pages-browser)/./app/styles/header-dark.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\products.css":{"id":"(app-pages-browser)/./app/styles/products.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\quality-control.css":{"id":"(app-pages-browser)/./app/styles/quality-control.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\banner-fix.css":{"id":"(app-pages-browser)/./app/styles/banner-fix.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\ms-fix.css":{"id":"(app-pages-browser)/./app/styles/ms-fix.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\high-contrast-override.css":{"id":"(app-pages-browser)/./app/styles/high-contrast-override.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\ms-high-contrast-blocker.css":{"id":"(app-pages-browser)/./app/styles/ms-high-contrast-blocker.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\ms-high-contrast-killer.css":{"id":"(app-pages-browser)/./app/styles/ms-high-contrast-killer.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\ms-translator-blocker.css":{"id":"(app-pages-browser)/./app/styles/ms-translator-blocker.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\product-detail-fix.css":{"id":"(app-pages-browser)/./app/styles/product-detail-fix.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\hero.css":{"id":"(app-pages-browser)/./app/styles/hero.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\home-page.css":{"id":"(app-pages-browser)/./app/styles/home-page.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\home-page-fix.css":{"id":"(app-pages-browser)/./app/styles/home-page-fix.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\custom-overrides.css":{"id":"(app-pages-browser)/./app/styles/custom-overrides.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\global-quote-form.css":{"id":"(app-pages-browser)/./app/styles/global-quote-form.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\loading-fix.css":{"id":"(app-pages-browser)/./app/styles/loading-fix.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\top-space-fix.css":{"id":"(app-pages-browser)/./app/styles/top-space-fix.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\custom-solutions.css":{"id":"(app-pages-browser)/./app/styles/custom-solutions.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\unified-cta.css":{"id":"(app-pages-browser)/./app/styles/unified-cta.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\holographic-guide.css":{"id":"(app-pages-browser)/./app/styles/holographic-guide.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\holographic-guide-override.css":{"id":"(app-pages-browser)/./app/styles/holographic-guide-override.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\custom-playground-design.css":{"id":"(app-pages-browser)/./app/styles/custom-playground-design.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\animations.css":{"id":"(app-pages-browser)/./app/styles/animations.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\zh\\admin\\providers.tsx":{"id":"(app-pages-browser)/./app/zh/admin/providers.tsx","name":"*","chunks":["app/zh/admin/login/layout","static/chunks/app/zh/admin/login/layout.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\zh\\admin\\page.tsx":{"id":"(app-pages-browser)/./app/zh/admin/page.tsx","name":"*","chunks":["app/zh/admin/page","static/chunks/app/zh/admin/page.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\zh\\admin\\login\\page.tsx":{"id":"(app-pages-browser)/./app/zh/admin/login/page.tsx","name":"*","chunks":["app/zh/admin/login/page","static/chunks/app/zh/admin/login/page.js"],"async":false},"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\styles\\globals.css":{"id":"(app-pages-browser)/./app/styles/globals.css","name":"*","chunks":["app/zh/admin/login/layout","static/chunks/app/zh/admin/login/layout.js"],"async":false}},"entryCSSFiles":{"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\not-found":[],"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\layout":["static/css/app/layout.css"],"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\zh\\admin\\layout":[],"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\zh\\admin\\page":[],"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\zh\\admin\\login\\page":[],"D:\\AIGC-dm\\Cross-border E-commerce Website Project\\nextjs\\app\\zh\\admin\\login\\layout":["static/css/app/zh/admin/login/layout.css"]}}