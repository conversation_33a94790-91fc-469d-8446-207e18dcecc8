{"c": ["app/layout", "app/[lang]/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Ccomponents%5CClientPreload.tsx&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Ccomponents%5CConditionalLayout.tsx&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Ccomponents%5CHighContrastFixer.tsx&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Ccomponents%5CHydrationErrorBoundary.tsx&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Ccomponents%5CLanguageProvider.tsx&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Ccomponents%5CLoadingFix.tsx&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%2C%22display%22%3A%22swap%22%2C%22variable%22%3A%22--font-poppins%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cheader-dark.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cproducts.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cquality-control.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cbanner-fix.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cms-fix.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Chigh-contrast-override.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cms-high-contrast-blocker.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cms-high-contrast-killer.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cms-translator-blocker.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cproduct-detail-fix.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Chero.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Chome-page.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Chome-page-fix.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Ccustom-overrides.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cloading-fix.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Ctop-space-fix.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Ccustom-solutions.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cunified-cta.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cholographic-guide.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cholographic-guide-override.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Ccustom-playground-design.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Canimations.css&server=false!", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Ccomponents%5CHtmlLangSetter.tsx&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cglobals.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cglobal-quote-form.css&server=false!"]}