'use client';

import Image from 'next/image';
import Link from 'next/link';

export default function CustomSolutionsContent() {
  return (
    <main className="main-content">
      <section className="page-header">
        <div className="container">
          <h1 className="page-title">全息解决方案</h1>
          <div className="breadcrumbs">
            <Link href="/">首页</Link> &gt; <span>全息解决方案</span>
          </div>
        </div>
      </section>

      <section className="solutions-intro">
        <div className="container">
          <div className="content-grid">
            <div className="content-image">
              <Image
                src="/images/solutions/custom-solution-planning.jpg"
                alt="定制全息投影解决方案"
                width={600}
                height={400}
                style={{ objectFit: 'cover' }}
              />
            </div>
            <div className="content-text">
              <h2>为您的独特需求定制全息投影解决方案</h2>
              <p>
                广州钧声深刻理解每个全息投影项目都是独一无二的。因此，我们提供完全定制化的解决方案，满足您的特定需求、空间限制和预算。
              </p>
              <p>
                无论您是需要全息定制方案，还是寻求建立第一个全息展厅的指导，我们的专家团队都将全程协助您。
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="solutions-list">
        <div className="container">
          <h2 className="section-title">我们的解决方案</h2>

          <div className="services-grid">
            <div className="service-card">
              <div className="service-image">
                <Image
                  src="/images/solutions/custom-design-guide.jpg"
                  alt="全息定制方案"
                  width={400}
                  height={300}
                  style={{ objectFit: 'cover' }}
                />
              </div>
              <div className="service-content">
                <h3>全息定制方案</h3>
                <p>
                  将您的创意变为现实，我们的定制全息解决方案服务可帮助您打造独特的全息展示空间。我们才华横溢的设计师将与您紧密合作，创造完全符合您需求的全息体验。
                </p>
                <ul className="service-features">
                  <li>根据您的需求量身定制</li>
                  <li>3D可视化全息展示</li>
                  <li>主题式全息方案</li>
                  <li>空间优化解决方案</li>
                </ul>
                <Link href="/pages/custom-playground-design" className="btn-secondary">
                  了解更多
                </Link>
              </div>
            </div>

            <div className="service-card">
              <div className="service-image">
                <Image
                  src="/images/solutions/purchase-guide.jpg"
                  alt="全息项目指导"
                  width={400}
                  height={300}
                  style={{ objectFit: 'cover' }}
                />
              </div>
              <div className="service-content">
                <h3>全息项目指导</h3>
                <p>
                  刚接触全息投影展示业务？我们的综合指南将引导您了解购买第一个全息展示系统的每个方面，确保您项目的成功。
                </p>
                <ul className="service-features">
                  <li>逐步采购流程</li>
                  <li>预算与财务规划</li>
                  <li>场地与空间规划建议</li>
                  <li>设备选择指导</li>
                </ul>
                <Link
                  href="/pages/how-to-purchase-your-first-holographic-system"
                  className="btn-secondary"
                >
                  了解更多
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="design-process">
        <div className="container">
          <h2 className="section-title">我们的定制设计流程</h2>

          <div className="process-timeline">
            <div className="timeline-item">
              <div className="timeline-number">1</div>
              <div className="timeline-content">
                <h3>初步咨询</h3>
                <p>我们首先了解您的创意、需求、空间限制和预算。</p>
              </div>
            </div>

            <div className="timeline-item">
              <div className="timeline-number">2</div>
              <div className="timeline-content">
                <h3>概念开发</h3>
                <p>我们的设计团队根据您的需求创建初步概念方案。</p>
              </div>
            </div>

            <div className="timeline-item">
              <div className="timeline-number">3</div>
              <div className="timeline-content">
                <h3>设计优化</h3>
                <p>根据您的反馈和偏好对选定概念进行精细调整。</p>
              </div>
            </div>

            <div className="timeline-item">
              <div className="timeline-number">4</div>
              <div className="timeline-content">
                <h3>最终设计与实施</h3>
                <p>方案获得批准后，我们确定最终设计并开始生产过程。</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="cta-section cta-particles">
        <div className="container">
          <div className="cta-content">
            <h2>准备好讨论您的全息定制解决方案？</h2>
            <p>今天就联系我们的团队，探索我们如何为您的需求创造完美的全息解决方案。</p>
            <Link
              href="/contact-us"
              className="btn-primary"
              data-text="立即联系我们"
            >
              {'立即联系我们'.split('').map((char, index) => (
                <i key={index}>{char === ' ' ? '\u00A0' : char}</i>
              ))}
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
