"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lang]/products/page",{

/***/ "(app-pages-browser)/./app/components/ProductGrid.tsx":
/*!****************************************!*\
  !*** ./app/components/ProductGrid.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductGrid; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _LanguageProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./LanguageProvider */ \"(app-pages-browser)/./app/components/LanguageProvider.tsx\");\n/* harmony import */ var _ModernProductCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ModernProductCard */ \"(app-pages-browser)/./app/components/ModernProductCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ProductGrid(param) {\n    let { searchQuery = \"\" } = param;\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [visibleCards, setVisibleCards] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Set());\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [totalProducts, setTotalProducts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const lang = (pathname === null || pathname === void 0 ? void 0 : pathname.split(\"/\")[1]) || \"zh\";\n    const cardRefs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);\n    const productsPerPage = 12; // 每页显示12个产品\n    const { t } = (0,_LanguageProvider__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    // 获取产品列表 - 使用API并传递语言参数\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchProducts = async ()=>{\n            try {\n                var _allProducts_;\n                setLoading(true);\n                console.log(\"[ProductGrid] Fetching products, language: \".concat(lang, \", page: \").concat(currentPage));\n                // 使用API获取产品数据，传递语言参数\n                const response = await fetch(\"/api/products?lang=\".concat(lang, \"&page=\").concat(currentPage, \"&limit=\").concat(productsPerPage));\n                if (!response.ok) {\n                    throw new Error(\"\".concat(t(\"products.loading_error\", {\n                        fallback: \"Error loading products: \"\n                    })).concat(response.status));\n                }\n                const data = await response.json();\n                const allProducts = data.products || [];\n                console.log(\"[ProductGrid] Received products data, count: \".concat(allProducts.length, \", first product: \").concat((_allProducts_ = allProducts[0]) === null || _allProducts_ === void 0 ? void 0 : _allProducts_.name));\n                // 如果有搜索查询，在客户端进行过滤\n                let filteredProducts = allProducts;\n                if (searchQuery.trim()) {\n                    const query = searchQuery.trim().toLowerCase();\n                    filteredProducts = allProducts.filter((product)=>{\n                        var _product_description, _product_category, _product_features;\n                        return product.name.toLowerCase().includes(query) || ((_product_description = product.description) === null || _product_description === void 0 ? void 0 : _product_description.toLowerCase().includes(query)) || ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.toLowerCase().includes(query)) || ((_product_features = product.features) === null || _product_features === void 0 ? void 0 : _product_features.some((feature)=>feature.toLowerCase().includes(query)));\n                    });\n                }\n                // 设置产品数据（API已经处理了分页）\n                setProducts(filteredProducts);\n                setTotalProducts(data.total || filteredProducts.length);\n                setTotalPages(data.totalPages || Math.ceil((data.total || filteredProducts.length) / productsPerPage));\n            } catch (err) {\n                console.error(\"Error fetching products:\", err);\n                setError(err instanceof Error ? err.message : t(\"products.loading_error\", {\n                    fallback: \"Unknown error\"\n                }));\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchProducts();\n    }, [\n        currentPage,\n        productsPerPage,\n        searchQuery,\n        lang\n    ]); // 当页面、每页数量、搜索查询或语言变化时重新获取\n    // 当搜索查询变化时，重置到第一页\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (searchQuery !== undefined) {\n            setCurrentPage(1);\n        }\n    }, [\n        searchQuery\n    ]);\n    // 卡片动画观察器\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const observerOptions = {\n            threshold: 0.2,\n            rootMargin: \"0px 0px -50px 0px\"\n        };\n        const observer = new IntersectionObserver((entries)=>{\n            entries.forEach((entry)=>{\n                if (entry.isIntersecting) {\n                    const index = parseInt(entry.target.getAttribute(\"data-index\") || \"0\");\n                    setVisibleCards((prev)=>new Set([\n                            ...prev,\n                            index\n                        ]));\n                }\n            });\n        }, observerOptions);\n        cardRefs.current.forEach((ref)=>{\n            if (ref) observer.observe(ref);\n        });\n        return ()=>observer.disconnect();\n    }, [\n        products\n    ]);\n    // 加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n            children: [\n                ...Array(12)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-100 rounded-lg h-96 animate-pulse\",\n                    style: {\n                        animationDelay: \"\".concat(i * 0.1, \"s\"),\n                        animationDuration: \"1.5s\"\n                    }\n                }, i, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this);\n    }\n    // 错误状态\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-700\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>window.location.reload(),\n                    className: \"mt-4 bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg\",\n                    children: t(\"products.retry\", {\n                        fallback: \"Retry\"\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    // 无产品状态\n    if (products.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 border border-gray-200 rounded-lg p-8 text-center\",\n            children: searchQuery ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700 text-lg mb-2\",\n                        children: t(\"products.no_search_results\", {\n                            fallback: \"No matching products found\"\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: [\n                            t(\"products.search_keyword\", {\n                                fallback: \"Search keyword: \"\n                            }),\n                            '\"',\n                            searchQuery,\n                            '\"'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 mt-2\",\n                        children: t(\"products.try_other_keywords\", {\n                            fallback: \"Please try other keywords or browse all products\"\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                lineNumber: 155,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-700\",\n                children: t(\"products.no_products\", {\n                    fallback: \"No products available\"\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                lineNumber: 161,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    }\n    // 分页处理函数\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n        setVisibleCards(new Set()); // 重置可见卡片\n        // 滚动到顶部\n        window.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    // 渲染产品网格\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"1b1507c3aa451a09\",\n                children: \".product-card-container.jsx-1b1507c3aa451a09{opacity:0;-webkit-transform:translatey(30px)scale(.95);-moz-transform:translatey(30px)scale(.95);-ms-transform:translatey(30px)scale(.95);-o-transform:translatey(30px)scale(.95);transform:translatey(30px)scale(.95);-webkit-transition:all.8s cubic-bezier(.4,0,.2,1);-moz-transition:all.8s cubic-bezier(.4,0,.2,1);-o-transition:all.8s cubic-bezier(.4,0,.2,1);transition:all.8s cubic-bezier(.4,0,.2,1)}.product-card-container.visible.jsx-1b1507c3aa451a09{opacity:1;-webkit-transform:translatey(0)scale(1);-moz-transform:translatey(0)scale(1);-ms-transform:translatey(0)scale(1);-o-transform:translatey(0)scale(1);transform:translatey(0)scale(1)}.stagger-animation.jsx-1b1507c3aa451a09{-webkit-transition-delay:-webkit-calc(var(--index)*.1s);-moz-transition-delay:-moz-calc(var(--index)*.1s);-o-transition-delay:calc(var(--index)*.1s);transition-delay:-webkit-calc(var(--index)*.1s);transition-delay:-moz-calc(var(--index)*.1s);transition-delay:calc(var(--index)*.1s)}.hover-lift.jsx-1b1507c3aa451a09{-webkit-transition:-webkit-transform.3s ease,box-shadow.3s ease;-moz-transition:-moz-transform.3s ease,box-shadow.3s ease;-o-transition:-o-transform.3s ease,box-shadow.3s ease;transition:-webkit-transform.3s ease,box-shadow.3s ease;transition:-moz-transform.3s ease,box-shadow.3s ease;transition:-o-transform.3s ease,box-shadow.3s ease;transition:transform.3s ease,box-shadow.3s ease}.hover-lift.jsx-1b1507c3aa451a09:hover{-webkit-transform:translatey(-8px)scale(1.02);-moz-transform:translatey(-8px)scale(1.02);-ms-transform:translatey(-8px)scale(1.02);-o-transform:translatey(-8px)scale(1.02);transform:translatey(-8px)scale(1.02);-webkit-box-shadow:0 20px 40px rgba(0,0,0,.15);-moz-box-shadow:0 20px 40px rgba(0,0,0,.15);box-shadow:0 20px 40px rgba(0,0,0,.15)}.pagination-button.jsx-1b1507c3aa451a09{-webkit-transition:all.3s ease;-moz-transition:all.3s ease;-o-transition:all.3s ease;transition:all.3s ease}.pagination-button.jsx-1b1507c3aa451a09:hover{-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px);-webkit-box-shadow:0 4px 12px rgba(0,0,0,.15);-moz-box-shadow:0 4px 12px rgba(0,0,0,.15);box-shadow:0 4px 12px rgba(0,0,0,.15)}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-1b1507c3aa451a09\" + \" \" + \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-1b1507c3aa451a09\" + \" \" + \"text-center text-gray-600\",\n                        children: searchQuery ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-1b1507c3aa451a09\",\n                            children: t(\"products.search_results\", {\n                                fallback: 'Search \"{{query}}\" found {{count}} products, page {{current}} of {{total}}',\n                                query: searchQuery,\n                                count: totalProducts,\n                                current: currentPage,\n                                total: totalPages\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-1b1507c3aa451a09\",\n                            children: t(\"products.total_products\", {\n                                fallback: \"Total {{count}} products, page {{current}} of {{total}}\",\n                                count: totalProducts,\n                                current: currentPage,\n                                total: totalPages\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-1b1507c3aa451a09\" + \" \" + \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n                        children: products.map((product, index)=>{\n                            var _product_images;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: (el)=>cardRefs.current[index] = el,\n                                \"data-index\": index,\n                                style: {\n                                    \"--index\": index\n                                },\n                                className: \"jsx-1b1507c3aa451a09\" + \" \" + \"product-card-container stagger-animation hover-lift \".concat(visibleCards.has(index) ? \"visible\" : \"\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModernProductCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    id: product.id,\n                                    title: product.name,\n                                    description: product.description || t(\"products.no_description\", {\n                                        fallback: \"No description available\"\n                                    }),\n                                    category: product.category || \"Interactive\",\n                                    image: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : _product_images[0]) || product.image_url,\n                                    slug: product.slug\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this)\n                            }, product.id, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-1b1507c3aa451a09\" + \" \" + \"flex justify-center items-center space-x-2 mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(currentPage - 1),\n                                disabled: currentPage === 1,\n                                className: \"jsx-1b1507c3aa451a09\" + \" \" + \"pagination-button px-4 py-2 rounded-lg border \".concat(currentPage === 1 ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                children: t(\"products.previous_page\", {\n                                    fallback: \"Previous\"\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this),\n                            Array.from({\n                                length: totalPages\n                            }, (_, i)=>i + 1).map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handlePageChange(page),\n                                    className: \"jsx-1b1507c3aa451a09\" + \" \" + \"pagination-button px-4 py-2 rounded-lg border \".concat(page === currentPage ? \"bg-blue-600 text-white border-blue-600\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                    children: page\n                                }, page, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(currentPage + 1),\n                                disabled: currentPage === totalPages,\n                                className: \"jsx-1b1507c3aa451a09\" + \" \" + \"pagination-button px-4 py-2 rounded-lg border \".concat(currentPage === totalPages ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                children: t(\"products.next_page\", {\n                                    fallback: \"Next\"\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ProductGrid, \"FimfZSYWdYcT2ZvgslAsX+xPbEs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _LanguageProvider__WEBPACK_IMPORTED_MODULE_4__.useLanguage\n    ];\n});\n_c = ProductGrid;\nvar _c;\n$RefreshReg$(_c, \"ProductGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ProductGrid.tsx\n"));

/***/ })

});