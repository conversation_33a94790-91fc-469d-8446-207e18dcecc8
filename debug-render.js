const fetch = require('node-fetch');

async function debugRender() {
  try {
    console.log('🔍 调试页面渲染问题...\n');
    
    const response = await fetch('http://localhost:3000/zh/products/motion-sensing-climbing');
    const html = await response.text();
    
    // 查找页面的主要结构
    console.log('📋 页面结构分析:');
    
    // 查找body内容
    const bodyMatch = html.match(/<body[^>]*>(.*?)<\/body>/s);
    if (bodyMatch) {
      const bodyContent = bodyMatch[1];
      console.log(`   Body内容长度: ${bodyContent.length} 字符`);
      
      // 查找主要的div结构
      const divMatches = bodyContent.match(/<div[^>]*class="[^"]*"[^>]*>/g);
      if (divMatches) {
        console.log(`   找到 ${divMatches.length} 个div元素`);
        
        // 显示前几个div的class
        divMatches.slice(0, 10).forEach((div, index) => {
          const classMatch = div.match(/class="([^"]*)"/);
          if (classMatch) {
            console.log(`   ${index + 1}. ${classMatch[1]}`);
          }
        });
      }
    }
    
    // 查找错误信息
    console.log('\n🔍 错误信息检查:');
    const errorPatterns = [
      { name: 'JavaScript错误', pattern: /error|Error/g },
      { name: 'React错误', pattern: /React|react/g },
      { name: 'Hydration错误', pattern: /hydration|Hydration/gi },
      { name: 'Undefined值', pattern: /undefined/g },
      { name: 'Null值', pattern: /null/g }
    ];
    
    errorPatterns.forEach(({ name, pattern }) => {
      const matches = html.match(pattern);
      if (matches && matches.length > 0) {
        console.log(`   ${name}: ${matches.length} 次`);
      }
    });
    
    // 查找产品相关内容
    console.log('\n📦 产品内容检查:');
    const productPatterns = [
      { name: '产品标题', pattern: /体感攀岩系统/ },
      { name: '产品描述', pattern: /体感识别|motion sensing/ },
      { name: '产品图片', pattern: /motion-sensing-climbing.*\.jpg/ },
      { name: '产品特性', pattern: /features|特性/ }
    ];
    
    productPatterns.forEach(({ name, pattern }) => {
      const found = pattern.test(html);
      console.log(`   ${found ? '✅' : '❌'} ${name}: ${found ? '存在' : '缺失'}`);
    });
    
    // 查找页面是否完整
    console.log('\n📄 页面完整性:');
    const hasDoctype = html.includes('<!DOCTYPE');
    const hasHtml = html.includes('<html') && html.includes('</html>');
    const hasHead = html.includes('<head>') && html.includes('</head>');
    const hasBody = html.includes('<body') && html.includes('</body>');
    
    console.log(`   DOCTYPE声明: ${hasDoctype ? '✅' : '❌'}`);
    console.log(`   HTML标签: ${hasHtml ? '✅' : '❌'}`);
    console.log(`   HEAD标签: ${hasHead ? '✅' : '❌'}`);
    console.log(`   BODY标签: ${hasBody ? '✅' : '❌'}`);
    
    // 显示页面的开头和结尾
    console.log('\n📝 页面内容片段:');
    console.log('开头 (前200字符):');
    console.log(html.substring(0, 200));
    console.log('\n结尾 (后200字符):');
    console.log(html.substring(html.length - 200));
    
  } catch (error) {
    console.error('❌ 调试失败:', error.message);
  }
}

debugRender();
