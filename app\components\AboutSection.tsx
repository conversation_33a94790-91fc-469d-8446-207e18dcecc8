'use client';

import { useEffect, useState, useRef, useMemo } from 'react';
import Image from 'next/image';
import { useLanguage } from './LanguageProvider';

export default function AboutSection() {
  const { locale, t } = useLanguage();
  const sectionRef = useRef<HTMLDivElement>(null);

  // 公司统计数据
  const companyStats = useMemo(
    () => [
      {
        number: 15,
        text: t('about.stats.years'),
        icon: 'fa-calendar-check',
      },
      {
        number: 1300,
        text: t('about.stats.office_area'),
        icon: 'fa-building',
      },
      {
        number: 500,
        text: t('about.stats.showroom_area'),
        icon: 'fa-store',
      },
      { number: 100, text: t('about.stats.service_quality'), icon: 'fa-medal' },
    ],
    [t]
  );

  // 数字增长动画
  const [animatedStats, setAnimatedStats] = useState(companyStats.map(() => 0));
  const [startAnimation, setStartAnimation] = useState(false);

  // 表单状态
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    message: '',
  });
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      // 提交表单数据到API
      const response = await fetch('/api/form-submissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          email: null, // AboutSection表单没有email字段，使用null
          phone: formData.phone,
          country: '',
          playground_size: '',
          product: '',
          message: formData.message,
        }),
      });

      const result = await response.json();

      if (result.success) {
        console.log('表单提交成功:', result);
        setFormSubmitted(true);
        // 重置表单
        setFormData({
          name: '',
          phone: '',
          message: '',
        });
      } else {
        throw new Error(result.message || 'Submission failed');
      }
    } catch (error) {
      console.error('表单提交失败:', error);
      // 这里可以添加错误处理，比如显示错误消息
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    // 添加整体组件的滚动动画
    const handleScroll = () => {
      const section = sectionRef.current;
      if (!section) return;

      const sectionTop = section.getBoundingClientRect().top;
      const windowHeight = window.innerHeight;

      if (sectionTop < windowHeight * 0.75) {
        section.classList.add('visible');
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // 初始检查

    // 监听滚动事件，判断元素是否出现在视口中
    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting) {
          setStartAnimation(true);
          observer.disconnect();
        }
      },
      { threshold: 0.2 }
    );

    const stats = document.querySelector('.stats-grid');
    if (stats) observer.observe(stats);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    if (!startAnimation) return;

    // 逐步增加数字
    const intervals = companyStats.map((stat, index) => {
      return setInterval(() => {
        setAnimatedStats(prev => {
          const newStats = [...prev];
          const increment = Math.ceil(stat.number / 30);

          if (newStats[index] < stat.number) {
            newStats[index] = Math.min(newStats[index] + increment, stat.number);
          }
          return newStats;
        });
      }, 50);
    });

    return () => intervals.forEach(interval => clearInterval(interval));
  }, [startAnimation, companyStats]);

  // 加载图标字体
  useEffect(() => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css';
    document.head.appendChild(link);

    return () => {
      document.head.removeChild(link);
    };
  }, []);

  return (
    <div className="about-container" ref={sectionRef}>
      <div className="grid-layout">
        <div className="about-image premium-shadow">
          <div className="image-frame">
            <Image
              src="/images/company/company.jpg"
              alt={t('about.company_profile')}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 40vw"
              style={{ objectFit: 'cover' }}
              className="company-image"
              loading="lazy"
            />
            <div className="image-overlay"></div>
          </div>
          <div className="experience-badge">
            <div className="badge-inner">
              <span className="years">{animatedStats[0]}</span>
              <span className="plus-year">+</span>
              <span className="years-text">{t('about.years')}</span>
            </div>
          </div>
          <div className="quality-badge">
            <div className="badge-content">
              <i className="fas fa-medal"></i>
              <span>{t('about.quality')}</span>
            </div>
          </div>
        </div>

        <div className="about-content">
          <div className="section-subtitle">
            <i className="fas fa-caret-right subtitle-icon"></i>
            {t('about.about_us')}
          </div>
          <h2 className="about-title">
            <span className="thin-text">{t('about.global_leading')}</span>
            <strong>
              {t('about.holographic_solution_provider')}
            </strong>
          </h2>

          <div className="elegant-divider">
            <span className="divider-dot"></span>
          </div>

          <p className="about-description">
            {t('about.junsheng_description')}
          </p>

          <p className="about-description">
            {t('about.company_rd_description')}
          </p>

          <div className="stats-grid">
            {companyStats.map((stat, index) => (
              <div key={index} className="stat-item">
                <div className="stat-icon">
                  <i className={`fas ${stat.icon}`}></i>
                </div>
                <div className="stat-content">
                  <div className="stat-number">
                    <span className="counter">{animatedStats[index]}</span>
                    <span className="plus">+</span>
                  </div>
                  <div className="stat-text">{stat.text}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="quote-form-section">
        <div className="container">
          <h2 className="quote-form-title">{t('quote_form.title')}</h2>
          <div className="form-decoration left"></div>
          <div className="form-decoration right"></div>
          {formSubmitted ? (
            <div className="form-success">
              <h3>{t('quote_form.success_title')}</h3>
              <p>{t('quote_form.success_message')}</p>
              <button className="reset-btn" onClick={() => setFormSubmitted(false)}>
                {t('quote_form.submit_again')}
              </button>
            </div>
          ) : (
            <form className="quote-form" onSubmit={handleSubmit}>
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="name">
                    {t('quote_form.name')} <span className="required">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    className="form-control"
                    required
                    value={formData.name}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="phone">
                    {t('quote_form.phone')} <span className="required">*</span>
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    className="form-control"
                    required
                    value={formData.phone}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
              <div className="form-group">
                <label htmlFor="message">{t('quote_form.message')}</label>
                <textarea
                  id="message"
                  name="message"
                  className="form-control"
                  rows={4}
                  value={formData.message}
                  onChange={handleInputChange}
                ></textarea>
              </div>
              <div className="form-group">
                <button
                  type="submit"
                  className="submit-btn"
                  disabled={submitting}
                  data-text={submitting ? t('quote_form.submitting') : t('quote_form.submit')}
                >
                  {(submitting ? t('quote_form.submitting') : t('quote_form.submit')).split('').map((char, index) => (
                    <i key={index}>{char === ' ' ? '\u00A0' : char}</i>
                  ))}
                </button>
              </div>
              <p className="privacy-note">{t('quote_form.privacy_note')}</p>
            </form>
          )}
        </div>
      </div>

      <style jsx>{`
        .about-container {
          max-width: 1400px;
          margin: 0 auto;
          padding: 80px 20px;
          opacity: 0;
          transform: translateY(30px);
          transition: all 1s cubic-bezier(0.165, 0.84, 0.44, 1);
        }

        .about-container.visible {
          opacity: 1;
          transform: translateY(0);
        }

        .grid-layout {
          display: grid;
          grid-template-columns: 1fr 1.1fr;
          gap: 40px;
          align-items: center;
        }

        .about-image {
          position: relative;
          overflow: visible;
          border-radius: 12px;
          transform: perspective(1000px);
          width: 100%;
        }

        .image-frame {
          position: relative;
          overflow: hidden;
          border-radius: 12px;
          transform: translateZ(0);
          height: 650px;
          box-shadow: 0 25px 50px rgba(26, 26, 46, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.1);
          width: 100%;
        }

        .company-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transform: scale(1);
          transition: transform 0.7s cubic-bezier(0.165, 0.84, 0.44, 1);
          filter: contrast(1.05);
        }

        .image-overlay {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          background: linear-gradient(135deg, rgba(26, 26, 46, 0.2) 0%, rgba(26, 26, 46, 0) 50%);
          z-index: 1;
          transition: opacity 0.5s ease;
        }

        .about-image:hover .company-image {
          transform: scale(1.05);
        }

        .experience-badge {
          position: absolute;
          bottom: -40px;
          right: -40px;
          width: 150px;
          height: 150px;
          background: linear-gradient(135deg, #1a1a2e, #2a2a5e);
          color: white;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 2;
          box-shadow: 0 15px 30px rgba(26, 26, 46, 0.15);
          animation: pulse 3s infinite;
        }

        .badge-inner {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 140px;
          height: 140px;
          border-radius: 50%;
          border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .years {
          font-size: 3rem;
          font-weight: 700;
          line-height: 1;
          display: inline-block;
        }

        .plus-year {
          font-size: 1.5rem;
          font-weight: 300;
          line-height: 1;
          margin-left: 2px;
        }

        .years-text {
          font-size: 0.9rem;
          margin-top: 8px;
          letter-spacing: 0.5px;
          opacity: 0.9;
        }

        .quality-badge {
          position: absolute;
          top: 30px;
          left: -20px;
          background: white;
          padding: 12px 20px;
          border-radius: 30px;
          box-shadow: 0 10px 30px rgba(26, 26, 46, 0.1);
          color: #1a1a2e;
          display: flex;
          align-items: center;
          z-index: 3;
          border: 1px solid rgba(26, 26, 46, 0.05);
        }

        .badge-content {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .badge-content i {
          color: #f5c542;
          font-size: 1.2rem;
        }

        .badge-content span {
          font-weight: 500;
          font-size: 0.95rem;
        }

        .about-content {
          padding-top: 10px;
        }

        .section-subtitle {
          font-size: 18px;
          color: #555;
          max-width: 720px;
          margin: 0 auto;
          line-height: 1.6;
        }

        .subtitle-icon {
          font-size: 0.8rem;
          color: #1a1a2e;
        }

        .about-title {
          font-size: 3.2rem;
          margin-bottom: 20px;
          line-height: 1.2;
          color: #1a1a2e;
        }

        .thin-text {
          font-weight: 200;
          display: block;
        }

        .about-title strong {
          font-weight: 500;
          position: relative;
        }

        .elegant-divider {
          position: relative;
          width: 80px;
          height: 2px;
          background: linear-gradient(90deg, rgba(26, 26, 46, 0.1), #1a1a2e, rgba(26, 26, 46, 0.1));
          margin: 0 0 25px;
          display: flex;
          align-items: center;
        }

        .divider-dot {
          position: absolute;
          width: 8px;
          height: 8px;
          background-color: #1a1a2e;
          border-radius: 50%;
          top: 50%;
          left: 25%;
          transform: translate(-50%, -50%);
        }

        .about-description {
          margin-bottom: 20px;
          font-size: 1.1rem;
          line-height: 1.7;
          color: #555;
          font-weight: 300;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 20px;
          margin: 35px 0 30px;
        }

        .stat-item {
          display: flex;
          align-items: center;
          gap: 15px;
          padding: 15px;
          background: rgba(26, 26, 46, 0.02);
          border-radius: 12px;
          transition: all 0.3s ease;
          border: 1px solid rgba(26, 26, 46, 0.05);
        }

        .stat-item:hover {
          background: white;
          box-shadow: 0 8px 25px rgba(26, 26, 46, 0.05);
          transform: translateY(-3px);
        }

        .stat-icon {
          width: 50px;
          height: 50px;
          border-radius: 10px;
          background: linear-gradient(135deg, #1a1a2e, #2a2a5e);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 1.2rem;
          flex-shrink: 0;
          box-shadow: 0 8px 20px rgba(26, 26, 46, 0.15);
        }

        .stat-content {
          flex-grow: 1;
        }

        .stat-number {
          display: flex;
          align-items: baseline;
          font-size: 2.4rem;
          font-weight: 700;
          color: #1a1a2e;
          line-height: 1;
          margin-bottom: 5px;
        }

        .counter {
          display: inline-block;
          background: linear-gradient(135deg, #1a1a2e 0%, #3a3a6e 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .plus {
          font-size: 1.5rem;
          margin-left: 2px;
          color: #555;
          font-weight: 300;
        }

        .stat-text {
          font-size: 0.95rem;
          color: #555;
          font-weight: 400;
        }

        .quote-form-section {
          max-width: 1240px;
          margin: 120px auto 80px;
          padding: 0 20px;
          position: relative;
        }

        .form-decoration {
          position: absolute;
          width: 150px;
          height: 150px;
          border-radius: 50%;
          z-index: -1;
          opacity: 0.1;
        }

        .form-decoration.left {
          top: 20%;
          left: -5%;
          background: linear-gradient(135deg, #1a1a2e, #5050a5);
          animation: float 8s ease-in-out infinite;
        }

        .form-decoration.right {
          bottom: 10%;
          right: -5%;
          background: linear-gradient(135deg, #5050a5, #1a1a2e);
          animation: float 10s ease-in-out infinite;
        }

        @keyframes float {
          0% {
            transform: translateY(0) scale(1);
          }
          50% {
            transform: translateY(-20px) scale(1.05);
          }
          100% {
            transform: translateY(0) scale(1);
          }
        }

        .container {
          width: 100%;
          position: relative;
        }

        .quote-form-title {
          text-align: center;
          margin-bottom: 40px;
          font-size: 32px;
          font-weight: 600;
          color: #1a1a2e;
          position: relative;
          padding-bottom: 15px;
          margin-top: 30px;
        }

        .quote-form-title:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 80px;
          height: 3px;
          background: linear-gradient(90deg, rgba(26, 26, 46, 0.1), #1a1a2e, rgba(26, 26, 46, 0.1));
        }

        .quote-form {
          max-width: 800px;
          margin: 0 auto;
          background-color: #fff;
          padding: 40px;
          border-radius: 15px;
          box-shadow: 0 10px 30px rgba(26, 26, 46, 0.08);
          border: 1px solid rgba(26, 26, 46, 0.05);
          transition:
            transform 0.3s ease,
            box-shadow 0.3s ease;
        }

        .quote-form:hover {
          transform: translateY(-5px);
          box-shadow: 0 15px 40px rgba(26, 26, 46, 0.12);
        }

        .form-row {
          display: flex;
          gap: 30px;
          margin-bottom: 25px;
        }

        .form-row .form-group {
          flex: 1;
          margin-bottom: 0;
        }

        .form-group {
          margin-bottom: 25px;
        }

        .form-group label {
          display: block;
          margin-bottom: 10px;
          font-weight: 500;
          color: #1a1a2e;
          font-size: 17px;
        }

        .required {
          color: #f53d3d;
          margin-left: 4px;
        }

        .form-control {
          width: 100%;
          padding: 16px 20px;
          font-size: 16px;
          border: 1px solid rgba(26, 26, 46, 0.1);
          border-radius: 8px;
          transition: all 0.3s ease;
          background-color: #f9f9fd;
        }

        .form-control:focus {
          border-color: #1a1a2e;
          outline: none;
          box-shadow: 0 0 0 3px rgba(26, 26, 46, 0.1);
          background-color: #fff;
        }

        textarea.form-control {
          resize: vertical;
          min-height: 120px;
        }

        .submit-btn {
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          width: 100%;
          height: 50px !important;
          padding: 0 20px !important;
          background-color: hsl(210deg 100% 44%) !important;
          color: white;
          border: none;
          border-radius: 12px !important;
          font-size: 18px !important;
          font-weight: bold !important;
          letter-spacing: 4px !important;
          text-transform: uppercase !important;
          cursor: pointer;
          transition: 31ms cubic-bezier(.5, .7, .4, 1) !important;
          box-shadow: hsl(210deg 87% 36%) 0px 7px 0px 0px !important;
          position: relative;
          overflow: hidden;
        }

        .submit-btn::before {
          content: attr(data-text) !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          position: absolute !important;
          inset: 0 !important;
          font-size: 15px !important;
          font-weight: bold !important;
          color: white !important;
          letter-spacing: 4px !important;
          opacity: 1 !important;
          transition: all 0.3s ease !important;
        }

        .submit-btn:active {
          box-shadow: none !important;
          transform: translateY(7px) !important;
          transition: 35ms cubic-bezier(.5, .7, .4, 1) !important;
        }

        .submit-btn:hover::before {
          transition: all 0.1s !important;
          transform: translateY(100%) !important;
          opacity: 0 !important;
        }

        .submit-btn i {
          color: white !important;
          font-size: 15px !important;
          font-weight: bold !important;
          letter-spacing: 4px !important;
          font-style: normal !important;
          transition: all 2s ease !important;
          transform: translateY(-20px) !important;
          opacity: 0 !important;
        }

        .submit-btn:hover i {
          transition: all 0.2s ease !important;
          transform: translateY(0px) !important;
          opacity: 1 !important;
        }

        .submit-btn:hover i:nth-child(1) { transition-delay: 0.045s !important; }
        .submit-btn:hover i:nth-child(2) { transition-delay: calc(0.045s * 2) !important; }
        .submit-btn:hover i:nth-child(3) { transition-delay: calc(0.045s * 3) !important; }
        .submit-btn:hover i:nth-child(4) { transition-delay: calc(0.045s * 4) !important; }
        .submit-btn:hover i:nth-child(5) { transition-delay: calc(0.045s * 5) !important; }
        .submit-btn:hover i:nth-child(6) { transition-delay: calc(0.045s * 6) !important; }
        .submit-btn:hover i:nth-child(7) { transition-delay: calc(0.045s * 7) !important; }
        .submit-btn:hover i:nth-child(8) { transition-delay: calc(0.045s * 8) !important; }
        .submit-btn:hover i:nth-child(9) { transition-delay: calc(0.045s * 9) !important; }
        .submit-btn:hover i:nth-child(10) { transition-delay: calc(0.045s * 10) !important; }
        .submit-btn:hover i:nth-child(11) { transition-delay: calc(0.045s * 11) !important; }
        .submit-btn:hover i:nth-child(12) { transition-delay: calc(0.045s * 12) !important; }
        .submit-btn:hover i:nth-child(13) { transition-delay: calc(0.045s * 13) !important; }
        .submit-btn:hover i:nth-child(14) { transition-delay: calc(0.045s * 14) !important; }
        .submit-btn:hover i:nth-child(15) { transition-delay: calc(0.045s * 15) !important; }

        .submit-btn:disabled {
          opacity: 0.7;
          cursor: not-allowed;
          transform: none;
        }

        .form-success {
          max-width: 800px;
          margin: 0 auto;
          background-color: #fff;
          padding: 40px;
          border-radius: 15px;
          box-shadow: 0 10px 30px rgba(26, 26, 46, 0.08);
          text-align: center;
          border: 1px solid rgba(26, 26, 46, 0.05);
        }

        .form-success h3 {
          color: #28a745;
          margin-bottom: 20px;
          font-size: 26px;
        }

        .form-success p {
          margin-bottom: 30px;
          color: #555;
          font-size: 18px;
          line-height: 1.6;
        }

        .reset-btn {
          padding: 14px 30px;
          background-color: #6c757d;
          color: white;
          border: none;
          border-radius: 8px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s;
          box-shadow: 0 5px 15px rgba(108, 117, 125, 0.2);
        }

        .reset-btn:hover {
          background-color: #5a6268;
          transform: translateY(-3px);
          box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
        }

        @media (max-width: 992px) {
          .quote-form,
          .form-success {
            max-width: 700px;
            padding: 30px;
          }
        }

        @media (max-width: 768px) {
          .quote-form,
          .form-success {
            max-width: 100%;
            padding: 25px;
          }

          .quote-form-title {
            font-size: 26px;
          }

          .form-control {
            padding: 14px 16px;
          }

          .form-row {
            flex-direction: column;
            gap: 20px;
          }

          .form-decoration {
            display: none;
          }
        }

        @media (max-width: 1100px) {
          .grid-layout {
            grid-template-columns: 1fr 1.1fr;
            gap: 40px;
          }

          .image-frame {
            height: 500px;
          }

          .about-title {
            font-size: 2.6rem;
          }

          .experience-badge {
            width: 130px;
            height: 130px;
            bottom: -30px;
            right: -30px;
          }

          .badge-inner {
            width: 120px;
            height: 120px;
          }

          .years {
            font-size: 2.6rem;
          }
        }

        @media (max-width: 1024px) {
          .grid-layout {
            grid-template-columns: 1fr 1fr;
            gap: 40px;
          }

          .image-frame {
            height: 550px;
          }
        }

        @media (max-width: 768px) {
          .grid-layout {
            grid-template-columns: 1fr;
            gap: 40px;
          }

          .about-content {
            order: 1;
          }

          .about-image {
            order: 2;
          }

          .image-frame {
            height: 550px;
            margin-bottom: 50px;
          }

          .experience-badge {
            right: 20px;
            bottom: -30px;
            width: 130px;
            height: 130px;
            font-size: 0.9em;
          }

          .quality-badge {
            left: 20px;
            width: 110px;
            height: 40px;
            font-size: 0.85em;
          }
        }

        @media (max-width: 576px) {
          .about-title {
            font-size: 2.2rem;
          }

          .stats-grid {
            grid-template-columns: 1fr;
            gap: 15px;
          }

          .experience-badge {
            width: 110px;
            height: 110px;
            bottom: -20px;
            right: -20px;
          }

          .badge-inner {
            width: 100px;
            height: 100px;
          }

          .years {
            font-size: 2.2rem;
          }

          .stat-item {
            padding: 15px;
          }

          .stat-icon {
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
          }

          .stat-number {
            font-size: 2.2rem;
          }
        }
      `}</style>
    </div>
  );
}
