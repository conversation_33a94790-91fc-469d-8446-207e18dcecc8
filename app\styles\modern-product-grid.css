/* Modern Product Grid Styles - 高优先级覆盖 */

/* 强制覆盖旧的产品网格样式 */
.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.gap-8,
.modern-product-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
  gap: 2rem !important;
  padding: 2rem 0 !important;
}

/* 强制覆盖所有旧的产品卡片样式 */
article.group.cursor-pointer,
.modern-product-card,
.product-card {
  background: white !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05) !important;
  height: auto !important;
  display: block !important;
}

article.group.cursor-pointer:hover,
.modern-product-card:hover,
.product-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
}

/* 强制覆盖产品图片容器样式 */
.relative.overflow-hidden.bg-neutral-50.rounded-lg,
.modern-product-image,
.product-image {
  position: relative !important;
  width: 100% !important;
  height: 240px !important;
  overflow: hidden !important;
  background: #f8f9fa !important;
  border-radius: 8px !important;
}

.relative.overflow-hidden.bg-neutral-50.rounded-lg img,
.modern-product-image img,
.product-image img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  transition: transform 0.7s ease !important;
}

article.group.cursor-pointer:hover .relative.overflow-hidden.bg-neutral-50.rounded-lg img,
.modern-product-card:hover .modern-product-image img,
.product-card:hover .product-image img {
  transform: scale(1.05) !important;
}

/* ID 标签 */
.product-id-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  z-index: 2;
}

/* 产品内容区域 */
.modern-product-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* 标题和箭头容器 */
.product-title-container {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 1rem;
}

.product-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
  line-height: 1.4;
  margin: 0;
  transition: color 0.2s ease;
}

.modern-product-card:hover .product-title {
  color: #6b7280;
}

/* 箭头图标 */
.product-arrow {
  width: 1rem;
  height: 1rem;
  color: #9ca3af;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.modern-product-card:hover .product-arrow {
  transform: translate(0.25rem, -0.25rem);
}

/* 产品描述 */
.product-description {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Tailwind line-clamp 支持 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 产品分类 */
.product-category {
  font-size: 0.75rem;
  color: #9ca3af;
  font-family: 'Courier New', monospace;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-product-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1rem 0;
  }

  .modern-product-image {
    height: 200px;
  }

  .modern-product-content {
    padding: 1.25rem;
  }

  .product-title {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .modern-product-grid {
    gap: 1rem;
  }

  .modern-product-image {
    height: 180px;
  }

  .modern-product-content {
    padding: 1rem;
  }
}

/* 加载状态 */
.product-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #6b7280;
}

/* 空状态 */
.product-empty {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.product-empty h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: #374151;
}

.product-empty p {
  font-size: 0.875rem;
  margin: 0;
}

/* 过滤器样式 */
.product-filters {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-button {
  padding: 0.5rem 1rem;
  border: 1px solid #e5e7eb;
  background: white;
  color: #6b7280;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-button:hover,
.filter-button.active {
  background: #111827;
  color: white;
  border-color: #111827;
}

/* 网格动画 */
.modern-product-card {
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.modern-product-card:nth-child(1) { animation-delay: 0.1s; }
.modern-product-card:nth-child(2) { animation-delay: 0.2s; }
.modern-product-card:nth-child(3) { animation-delay: 0.3s; }
.modern-product-card:nth-child(4) { animation-delay: 0.4s; }
.modern-product-card:nth-child(5) { animation-delay: 0.5s; }
.modern-product-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
