"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lang]/products/page",{

/***/ "(app-pages-browser)/./app/components/ProductGrid.tsx":
/*!****************************************!*\
  !*** ./app/components/ProductGrid.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductGrid; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _LanguageProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./LanguageProvider */ \"(app-pages-browser)/./app/components/LanguageProvider.tsx\");\n/* harmony import */ var _ModernProductCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ModernProductCard */ \"(app-pages-browser)/./app/components/ModernProductCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ProductGrid(param) {\n    let { searchQuery = \"\" } = param;\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [visibleCards, setVisibleCards] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Set());\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [totalProducts, setTotalProducts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const lang = (pathname === null || pathname === void 0 ? void 0 : pathname.split(\"/\")[1]) || \"zh\";\n    const cardRefs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);\n    const productsPerPage = 12; // 每页显示12个产品\n    const { t } = (0,_LanguageProvider__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    // 获取产品列表 - 使用API并传递语言参数\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchProducts = async ()=>{\n            try {\n                var _allProducts_;\n                setLoading(true);\n                console.log(\"[ProductGrid] Fetching products, language: \".concat(lang, \", page: \").concat(currentPage));\n                // 使用API获取产品数据，传递语言参数\n                const response = await fetch(\"/api/products?lang=\".concat(lang, \"&page=\").concat(currentPage, \"&limit=\").concat(productsPerPage));\n                if (!response.ok) {\n                    throw new Error(\"\".concat(t(\"products.loading_error\", {\n                        fallback: \"Error loading products: \"\n                    })).concat(response.status));\n                }\n                const data = await response.json();\n                const allProducts = data.products || [];\n                console.log(\"[ProductGrid] 收到产品数据，数量: \".concat(allProducts.length, \", 第一个产品: \").concat((_allProducts_ = allProducts[0]) === null || _allProducts_ === void 0 ? void 0 : _allProducts_.name));\n                // 如果有搜索查询，在客户端进行过滤\n                let filteredProducts = allProducts;\n                if (searchQuery.trim()) {\n                    const query = searchQuery.trim().toLowerCase();\n                    filteredProducts = allProducts.filter((product)=>{\n                        var _product_description, _product_category, _product_features;\n                        return product.name.toLowerCase().includes(query) || ((_product_description = product.description) === null || _product_description === void 0 ? void 0 : _product_description.toLowerCase().includes(query)) || ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.toLowerCase().includes(query)) || ((_product_features = product.features) === null || _product_features === void 0 ? void 0 : _product_features.some((feature)=>feature.toLowerCase().includes(query)));\n                    });\n                }\n                // 设置产品数据（API已经处理了分页）\n                setProducts(filteredProducts);\n                setTotalProducts(data.total || filteredProducts.length);\n                setTotalPages(data.totalPages || Math.ceil((data.total || filteredProducts.length) / productsPerPage));\n            } catch (err) {\n                console.error(\"获取产品错误:\", err);\n                setError(err instanceof Error ? err.message : t(\"products.loading_error\", {\n                    fallback: \"Unknown error\"\n                }));\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchProducts();\n    }, [\n        currentPage,\n        productsPerPage,\n        searchQuery,\n        lang\n    ]); // 当页面、每页数量、搜索查询或语言变化时重新获取\n    // 当搜索查询变化时，重置到第一页\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (searchQuery !== undefined) {\n            setCurrentPage(1);\n        }\n    }, [\n        searchQuery\n    ]);\n    // 卡片动画观察器\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const observerOptions = {\n            threshold: 0.2,\n            rootMargin: \"0px 0px -50px 0px\"\n        };\n        const observer = new IntersectionObserver((entries)=>{\n            entries.forEach((entry)=>{\n                if (entry.isIntersecting) {\n                    const index = parseInt(entry.target.getAttribute(\"data-index\") || \"0\");\n                    setVisibleCards((prev)=>new Set([\n                            ...prev,\n                            index\n                        ]));\n                }\n            });\n        }, observerOptions);\n        cardRefs.current.forEach((ref)=>{\n            if (ref) observer.observe(ref);\n        });\n        return ()=>observer.disconnect();\n    }, [\n        products\n    ]);\n    // 加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n            children: [\n                ...Array(12)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-100 rounded-lg h-96 animate-pulse\",\n                    style: {\n                        animationDelay: \"\".concat(i * 0.1, \"s\"),\n                        animationDuration: \"1.5s\"\n                    }\n                }, i, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this);\n    }\n    // 错误状态\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-700\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>window.location.reload(),\n                    className: \"mt-4 bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg\",\n                    children: t(\"products.retry\", {\n                        fallback: \"Retry\"\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    // 无产品状态\n    if (products.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 border border-gray-200 rounded-lg p-8 text-center\",\n            children: searchQuery ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700 text-lg mb-2\",\n                        children: t(\"products.no_search_results\", {\n                            fallback: \"No matching products found\"\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: [\n                            t(\"products.search_keyword\", {\n                                fallback: \"Search keyword: \"\n                            }),\n                            '\"',\n                            searchQuery,\n                            '\"'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 mt-2\",\n                        children: t(\"products.try_other_keywords\", {\n                            fallback: \"Please try other keywords or browse all products\"\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                lineNumber: 155,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-700\",\n                children: t(\"products.no_products\", {\n                    fallback: \"No products available\"\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                lineNumber: 161,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    }\n    // 分页处理函数\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n        setVisibleCards(new Set()); // 重置可见卡片\n        // 滚动到顶部\n        window.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    // 渲染产品网格\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"1b1507c3aa451a09\",\n                children: \".product-card-container.jsx-1b1507c3aa451a09{opacity:0;-webkit-transform:translatey(30px)scale(.95);-moz-transform:translatey(30px)scale(.95);-ms-transform:translatey(30px)scale(.95);-o-transform:translatey(30px)scale(.95);transform:translatey(30px)scale(.95);-webkit-transition:all.8s cubic-bezier(.4,0,.2,1);-moz-transition:all.8s cubic-bezier(.4,0,.2,1);-o-transition:all.8s cubic-bezier(.4,0,.2,1);transition:all.8s cubic-bezier(.4,0,.2,1)}.product-card-container.visible.jsx-1b1507c3aa451a09{opacity:1;-webkit-transform:translatey(0)scale(1);-moz-transform:translatey(0)scale(1);-ms-transform:translatey(0)scale(1);-o-transform:translatey(0)scale(1);transform:translatey(0)scale(1)}.stagger-animation.jsx-1b1507c3aa451a09{-webkit-transition-delay:-webkit-calc(var(--index)*.1s);-moz-transition-delay:-moz-calc(var(--index)*.1s);-o-transition-delay:calc(var(--index)*.1s);transition-delay:-webkit-calc(var(--index)*.1s);transition-delay:-moz-calc(var(--index)*.1s);transition-delay:calc(var(--index)*.1s)}.hover-lift.jsx-1b1507c3aa451a09{-webkit-transition:-webkit-transform.3s ease,box-shadow.3s ease;-moz-transition:-moz-transform.3s ease,box-shadow.3s ease;-o-transition:-o-transform.3s ease,box-shadow.3s ease;transition:-webkit-transform.3s ease,box-shadow.3s ease;transition:-moz-transform.3s ease,box-shadow.3s ease;transition:-o-transform.3s ease,box-shadow.3s ease;transition:transform.3s ease,box-shadow.3s ease}.hover-lift.jsx-1b1507c3aa451a09:hover{-webkit-transform:translatey(-8px)scale(1.02);-moz-transform:translatey(-8px)scale(1.02);-ms-transform:translatey(-8px)scale(1.02);-o-transform:translatey(-8px)scale(1.02);transform:translatey(-8px)scale(1.02);-webkit-box-shadow:0 20px 40px rgba(0,0,0,.15);-moz-box-shadow:0 20px 40px rgba(0,0,0,.15);box-shadow:0 20px 40px rgba(0,0,0,.15)}.pagination-button.jsx-1b1507c3aa451a09{-webkit-transition:all.3s ease;-moz-transition:all.3s ease;-o-transition:all.3s ease;transition:all.3s ease}.pagination-button.jsx-1b1507c3aa451a09:hover{-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px);-webkit-box-shadow:0 4px 12px rgba(0,0,0,.15);-moz-box-shadow:0 4px 12px rgba(0,0,0,.15);box-shadow:0 4px 12px rgba(0,0,0,.15)}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-1b1507c3aa451a09\" + \" \" + \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-1b1507c3aa451a09\" + \" \" + \"text-center text-gray-600\",\n                        children: searchQuery ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-1b1507c3aa451a09\",\n                            children: t(\"products.search_results\", {\n                                fallback: 'Search \"{{query}}\" found {{count}} products, page {{current}} of {{total}}',\n                                query: searchQuery,\n                                count: totalProducts,\n                                current: currentPage,\n                                total: totalPages\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-1b1507c3aa451a09\",\n                            children: t(\"products.total_products\", {\n                                fallback: \"Total {{count}} products, page {{current}} of {{total}}\",\n                                count: totalProducts,\n                                current: currentPage,\n                                total: totalPages\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-1b1507c3aa451a09\" + \" \" + \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n                        children: products.map((product, index)=>{\n                            var _product_images;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: (el)=>cardRefs.current[index] = el,\n                                \"data-index\": index,\n                                style: {\n                                    \"--index\": index\n                                },\n                                className: \"jsx-1b1507c3aa451a09\" + \" \" + \"product-card-container stagger-animation hover-lift \".concat(visibleCards.has(index) ? \"visible\" : \"\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModernProductCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    id: product.id,\n                                    title: product.name,\n                                    description: product.description || t(\"products.no_description\", {\n                                        fallback: \"No description available\"\n                                    }),\n                                    category: product.category || \"Interactive\",\n                                    image: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : _product_images[0]) || product.image_url,\n                                    slug: product.slug\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this)\n                            }, product.id, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-1b1507c3aa451a09\" + \" \" + \"flex justify-center items-center space-x-2 mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(currentPage - 1),\n                                disabled: currentPage === 1,\n                                className: \"jsx-1b1507c3aa451a09\" + \" \" + \"pagination-button px-4 py-2 rounded-lg border \".concat(currentPage === 1 ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                children: t(\"products.previous_page\", {\n                                    fallback: \"Previous\"\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this),\n                            Array.from({\n                                length: totalPages\n                            }, (_, i)=>i + 1).map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handlePageChange(page),\n                                    className: \"jsx-1b1507c3aa451a09\" + \" \" + \"pagination-button px-4 py-2 rounded-lg border \".concat(page === currentPage ? \"bg-blue-600 text-white border-blue-600\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                    children: page\n                                }, page, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(currentPage + 1),\n                                disabled: currentPage === totalPages,\n                                className: \"jsx-1b1507c3aa451a09\" + \" \" + \"pagination-button px-4 py-2 rounded-lg border \".concat(currentPage === totalPages ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                children: t(\"products.next_page\", {\n                                    fallback: \"Next\"\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ProductGrid, \"FimfZSYWdYcT2ZvgslAsX+xPbEs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _LanguageProvider__WEBPACK_IMPORTED_MODULE_4__.useLanguage\n    ];\n});\n_c = ProductGrid;\nvar _c;\n$RefreshReg$(_c, \"ProductGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ProductGrid.tsx\n"));

/***/ })

});