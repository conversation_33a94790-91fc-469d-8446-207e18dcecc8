@echo off
echo 🔧 修复API路由导出问题
echo.

echo 📋 问题: output: export 模式不支持API路由
echo 💡 解决: 临时重命名API目录，构建完成后恢复
echo.

echo 🔄 步骤1: 重命名API目录...
if exist "app\api" (
    if exist "app\api-backup" rmdir /s /q "app\api-backup"
    ren "app\api" "api-backup"
    echo ✅ API目录已重命名为 api-backup
) else (
    echo ⚠️ API目录不存在
)

echo.
echo 🔨 步骤2: 开始构建...
call npm run build

if errorlevel 1 (
    echo.
    echo ❌ 构建失败，恢复API目录...
    if exist "app\api-backup" (
        ren "app\api-backup" "api"
        echo ✅ API目录已恢复
    )
    echo.
    echo 💡 建议使用Vercel部署:
    echo 1. 访问 https://vercel.com
    echo 2. 拖拽整个项目文件夹
    echo 3. 自动处理API路由
    pause
    exit /b 1
) else (
    echo.
    echo ✅ 构建成功！
    echo.
    echo 📁 检查out目录...
    if exist "out" (
        dir out
        echo.
        echo 🔄 步骤3: 恢复API目录...
        if exist "app\api-backup" (
            ren "app\api-backup" "api"
            echo ✅ API目录已恢复
        )
        echo.
        echo 🚀 现在可以部署到Netlify:
        echo 1. 访问 https://app.netlify.com/drop
        echo 2. 拖拽 'out' 文件夹到部署区域
        echo 3. 等待部署完成
        echo.
        echo ⚠️ 注意: 静态版本不支持以下功能:
        echo - 用户登录/注册
        echo - 管理后台
        echo - 表单提交
        echo - 数据库操作
        echo.
        echo 💡 如需完整功能，推荐使用Vercel:
        echo https://vercel.com
        
        set /p deploy=现在打开Netlify部署页面? (y/n): 
        if /i "%deploy%"=="y" (
            start https://app.netlify.com/drop
        )
    ) else (
        echo ❌ out目录不存在，构建可能有问题
        if exist "app\api-backup" (
            ren "app\api-backup" "api"
            echo ✅ API目录已恢复
        )
    )
)

echo.
pause
