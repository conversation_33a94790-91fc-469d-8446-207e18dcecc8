# QQ邮箱SMTP配置指南

## 🎯 目标
配置QQ邮箱SMTP，让邮件直接发送到 <EMAIL>

## 📧 QQ邮箱SMTP设置步骤

### 1. 登录QQ邮箱
访问：https://mail.qq.com
使用您的QQ号：19402341594 登录

### 2. 开启SMTP服务
1. 点击邮箱页面上方的 **"设置"**
2. 选择 **"账户"** 选项卡
3. 找到 **"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"**
4. 开启 **"SMTP服务"**

### 3. 获取授权码
1. 在SMTP服务设置中，点击 **"生成授权码"**
2. 按照提示发送短信验证
3. 获得16位授权码（类似：abcdEFGH12345678）
4. **重要**：这个授权码就是SMTP密码，不是QQ密码！

### 4. 更新配置文件
在 `.env.local` 文件中更新：
```
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=你的16位授权码
FROM_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>
```

## 🔧 QQ邮箱SMTP参数

| 参数 | 值 |
|------|-----|
| SMTP服务器 | smtp.qq.com |
| 端口 | 587 (推荐) 或 465 |
| 加密方式 | STARTTLS 或 SSL |
| 用户名 | 完整QQ邮箱地址 |
| 密码 | 授权码（不是QQ密码） |

## 📱 获取授权码的详细步骤

### 方法1：网页版
1. 登录 https://mail.qq.com
2. 设置 → 账户 → POP3/IMAP/SMTP服务
3. 开启SMTP服务
4. 生成授权码

### 方法2：手机QQ邮箱APP
1. 打开QQ邮箱APP
2. 设置 → 账户与安全
3. 安全设置 → 生成授权码

## ⚠️ 常见问题

### 问题1：无法生成授权码
- **解决**：确保QQ邮箱已实名认证
- **解决**：检查手机号是否绑定QQ

### 问题2：授权码无效
- **解决**：重新生成授权码
- **解决**：确保复制完整的16位授权码

### 问题3：连接被拒绝
- **解决**：检查防火墙设置
- **解决**：尝试使用465端口+SSL

## 🧪 测试配置

配置完成后，使用以下API测试：
```
POST http://localhost:3000/api/simple-email-test
```

## 📞 技术支持

如果遇到问题：
1. 检查QQ邮箱设置是否正确
2. 确认授权码是否有效
3. 查看服务器日志错误信息
4. 联系QQ邮箱客服

## 🎉 配置成功标志

当看到以下日志时，表示配置成功：
```
SMTP连接验证成功
邮件发送成功: [message-id]
```

邮件将直接发送到：<EMAIL>
