"use client"

import { motion } from "framer-motion"
import { Facebook, Twitter, Instagram, Linkedin, Mail, Phone, MapPin } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useState } from "react"
import { useTranslation } from '../utils/useTranslation';

export default function Footer() {
  const { t, locale } = useTranslation();
  const [hoveredSection, setHoveredSection] = useState<string | null>(null)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  }

  const linkVariants = {
    rest: { x: 0 },
    hover: { x: 5, transition: { duration: 0.2 } },
  }

  const sections = [
    {
      title: t('footer.services'),
      links: [
        { text: t('footer.holographic_purchase_guide'), href: `/${locale}/pages/how-to-purchase-your-first-holographic-system` },
        { text: t('footer.custom_design_guide'), href: `/${locale}/pages/custom-playground-design` },
        { text: t('footer.quality_control'), href: `/${locale}/pages/quality-control` },
        { text: t('footer.safe_standard'), href: `/${locale}/pages/safe-standard` },
        { text: t('footer.marketing_support'), href: `/${locale}/pages/marketing-support` }
      ],
    },
    {
      title: t('footer.solutions'),
      links: [
        { text: t('footer.holographic'), href: `/${locale}/collections/holographic-projection` },
        { text: t('footer.custom_solutions'), href: `/${locale}/pages/custom-playground-design` },
        { text: t('footer.interactive'), href: `/${locale}/collections/interactive-projection` }
      ],
    },
    {
      title: t('footer.blog'),
      links: [
        { text: t('footer.blog_posts'), href: `/${locale}/blog` },
        { text: t('footer.news'), href: `/${locale}/blog/news` },
        { text: t('footer.case_studies'), href: `/${locale}/blog/case-studies` }
      ],
    },
  ]

  const socialIcons = [
    { icon: Facebook, href: "#", label: "Facebook" },
    { icon: Twitter, href: "#", label: "Twitter" },
    { icon: Instagram, href: "#", label: "Instagram" },
    { icon: Linkedin, href: "#", label: "LinkedIn" },
  ]

  return (
    <footer className="relative bg-gradient-to-br from-gray-50 via-white to-gray-100 text-gray-900 overflow-hidden">
      <motion.div
        className="relative z-10 max-w-7xl mx-auto px-6 py-16"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, margin: "-100px" }}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 lg:gap-12">
          {/* Services Sections */}
          {sections.map((section, index) => (
            <motion.div
              key={section.title}
              variants={itemVariants}
              className="space-y-4"
              onMouseEnter={() => setHoveredSection(section.title)}
              onMouseLeave={() => setHoveredSection(null)}
            >
              <motion.h3 className="text-lg font-semibold text-gray-900 relative" whileHover={{ scale: 1.05 }}>
                {section.title}
                <motion.div
                  className="absolute -bottom-1 left-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"
                  initial={{ width: "20%" }}
                  animate={{ width: hoveredSection === section.title ? "100%" : "20%" }}
                  transition={{ duration: 0.3 }}
                />
              </motion.h3>
              <ul className="space-y-3">
                {section.links.map((link, linkIndex) => (
                  <motion.li key={linkIndex} variants={linkVariants} initial="rest" whileHover="hover">
                    <Link
                      href={link.href}
                      className="text-gray-600 hover:text-gray-900 transition-colors duration-200 text-sm block"
                    >
                      {link.text}
                    </Link>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          ))}

          {/* Contact Section */}
          <motion.div variants={itemVariants} className="space-y-6">
            <motion.h3 className="text-lg font-semibold text-gray-900 relative" whileHover={{ scale: 1.05 }}>
              {t('footer.contact_us')}
              <motion.div
                className="absolute -bottom-1 left-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"
                initial={{ width: "20%" }}
                whileHover={{ width: "100%" }}
                transition={{ duration: 0.3 }}
              />
            </motion.h3>

            <div className="space-y-4">
              <motion.div
                className="flex items-start space-x-3 group"
                whileHover={{ x: 5 }}
                transition={{ duration: 0.2 }}
              >
                <MapPin className="w-5 h-5 text-blue-500 mt-0.5 group-hover:scale-110 transition-transform" />
                <span className="text-gray-600 text-sm">{t('contact.address')}</span>
              </motion.div>

              <motion.div
                className="flex items-center space-x-3 group"
                whileHover={{ x: 5 }}
                transition={{ duration: 0.2 }}
              >
                <Mail className="w-5 h-5 text-blue-500 group-hover:scale-110 transition-transform" />
                <a
                  href="mailto:<EMAIL>"
                  className="text-gray-600 hover:text-gray-900 text-sm transition-colors"
                >
                  <EMAIL>
                </a>
              </motion.div>

              <motion.div
                className="flex items-center space-x-3 group"
                whileHover={{ x: 5 }}
                transition={{ duration: 0.2 }}
              >
                <Phone className="w-5 h-5 text-blue-500 group-hover:scale-110 transition-transform" />
                <a href="tel:+8615989399197" className="text-gray-600 hover:text-gray-900 text-sm transition-colors">
                  +86 15989399197
                </a>
              </motion.div>
            </div>
          </motion.div>

          {/* Company Info & Logo */}
          <motion.div variants={itemVariants} className="lg:col-span-1 space-y-6">
            <motion.div className="relative" whileHover={{ scale: 1.05 }} transition={{ duration: 0.3 }}>
              <Image
                src="/images/junsheng-logo-1.png"
                alt="Junsheng Logo"
                width={200}
                height={80}
                priority
                className="object-contain"
                style={{ width: 'auto', height: 'auto' }}
              />
            </motion.div>

            <motion.p
              className="text-gray-600 text-sm leading-relaxed"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.8 }}
            >
              {t('footer.company_description')}
            </motion.p>
          </motion.div>
        </div>

        {/* Divider */}
        <motion.div
          className="my-12 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 1, delay: 0.5 }}
        />

        {/* Bottom Section */}
        <motion.div
          className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0"
          variants={itemVariants}
        >
          <motion.p
            className="text-gray-500 text-sm"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.7 }}
          >
            © 2025 Guangzhou Junsheng Technology Co., Ltd. {t('footer.rights_reserved')}
          </motion.p>

          <motion.div
            className="flex space-x-4"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.8 }}
          >
            {socialIcons.map((social, index) => (
              <motion.a
                key={social.label}
                href={social.href}
                className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-gray-600 hover:bg-blue-500 hover:text-white transition-all duration-300"
                whileHover={{
                  scale: 1.1,
                  rotate: 5,
                  boxShadow: "0 0 20px rgba(59, 130, 246, 0.3)",
                }}
                whileTap={{ scale: 0.95 }}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.9 + index * 0.1 }}
              >
                <social.icon className="w-5 h-5" />
              </motion.a>
            ))}
          </motion.div>
        </motion.div>
      </motion.div>
    </footer>
  );
}
