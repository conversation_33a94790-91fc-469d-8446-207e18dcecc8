import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';

export async function POST(request: NextRequest) {
  try {
    console.log('开始Resend邮件服务测试...');
    
    // 初始化Resend
    const resend = new Resend(process.env.RESEND_API_KEY);
    
    if (!process.env.RESEND_API_KEY) {
      return NextResponse.json({
        success: false,
        message: 'Resend API密钥未配置',
        suggestion: '请在.env.local中设置RESEND_API_KEY'
      }, { status: 400 });
    }

    // 测试数据
    const testData = {
      id: 2025,
      name: "王总",
      email: "<EMAIL>",
      phone: "+86 138-0000-8888",
      country: "中国",
      playground_size: "1000+ sqm",
      product: "全息互动投影系统",
      message: "您好，我们是一家大型连锁儿童乐园企业，在全国有50多家门店。我们对贵公司的全息互动投影系统非常感兴趣，希望能够了解：1. 详细的技术参数和功能介绍 2. 批量采购的价格方案 3. 安装和维护服务 4. 成功案例展示。我们计划在今年内完成首批10家门店的设备采购和安装。",
      created_at: new Date().toISOString()
    };

    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const fromEmail = '<EMAIL>'; // Resend验证的发件人地址
    const fromName = process.env.FROM_NAME || '跨境电商网站';

    console.log('发送邮件配置:', {
      from: fromEmail,
      to: adminEmail,
      apiKeyExists: !!process.env.RESEND_API_KEY
    });

    try {
      // 发送邮件
      const result = await resend.emails.send({
        from: `${fromName} <${fromEmail}>`,
        to: [adminEmail],
        subject: `🔔 新的表单提交 - ${testData.name}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
              <h1 style="margin: 0; font-size: 28px;">🔔 新的表单提交</h1>
              <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">来自跨境电商网站</p>
            </div>
            
            <div style="background-color: #ffffff; padding: 30px; border: 1px solid #e1e5e9; border-top: none;">
              <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
                <h2 style="color: #495057; margin-top: 0; margin-bottom: 20px; font-size: 20px;">👤 客户信息</h2>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="padding: 10px 0; font-weight: bold; color: #6c757d; width: 120px;">姓名:</td>
                    <td style="padding: 10px 0; color: #495057;">${testData.name}</td>
                  </tr>
                  <tr>
                    <td style="padding: 10px 0; font-weight: bold; color: #6c757d;">邮箱:</td>
                    <td style="padding: 10px 0; color: #495057;">${testData.email}</td>
                  </tr>
                  <tr>
                    <td style="padding: 10px 0; font-weight: bold; color: #6c757d;">电话:</td>
                    <td style="padding: 10px 0; color: #495057;">${testData.phone}</td>
                  </tr>
                  <tr>
                    <td style="padding: 10px 0; font-weight: bold; color: #6c757d;">国家:</td>
                    <td style="padding: 10px 0; color: #495057;">${testData.country}</td>
                  </tr>
                  <tr>
                    <td style="padding: 10px 0; font-weight: bold; color: #6c757d;">场地大小:</td>
                    <td style="padding: 10px 0; color: #495057;">${testData.playground_size}</td>
                  </tr>
                  <tr>
                    <td style="padding: 10px 0; font-weight: bold; color: #6c757d;">感兴趣的产品:</td>
                    <td style="padding: 10px 0; color: #495057; font-weight: bold;">${testData.product}</td>
                  </tr>
                </table>
              </div>

              <div style="background-color: #fff; padding: 25px; border: 2px solid #e9ecef; border-radius: 8px; margin-bottom: 25px;">
                <h2 style="color: #495057; margin-top: 0; margin-bottom: 15px; font-size: 20px;">💬 客户留言</h2>
                <p style="line-height: 1.8; color: #495057; margin: 0; font-size: 16px;">${testData.message}</p>
              </div>

              <div style="background-color: #f1f3f4; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea;">
                <p style="margin: 0; color: #6c757d; font-size: 14px;">
                  <strong>📅 提交时间:</strong> ${new Date(testData.created_at).toLocaleString('zh-CN')}<br>
                  <strong>🆔 提交ID:</strong> #${testData.id}<br>
                  <strong>📧 邮件服务:</strong> Resend
                </p>
              </div>
            </div>

            <div style="background-color: #6c757d; color: white; padding: 20px; border-radius: 0 0 10px 10px; text-align: center;">
              <p style="margin: 0; font-size: 14px;">此邮件由跨境电商网站系统通过 Resend 服务自动发送</p>
              <p style="margin: 10px 0 0 0; font-size: 12px; opacity: 0.8;">如有疑问，请联系技术支持</p>
            </div>
          </div>
        `,
        text: `
新的表单提交通知

客户信息:
- 姓名: ${testData.name}
- 邮箱: ${testData.email}
- 电话: ${testData.phone}
- 国家: ${testData.country}
- 场地大小: ${testData.playground_size}
- 感兴趣的产品: ${testData.product}

客户留言:
${testData.message}

提交时间: ${new Date(testData.created_at).toLocaleString('zh-CN')}
提交ID: #${testData.id}
邮件服务: Resend

此邮件由跨境电商网站系统自动发送
        `
      });

      console.log('Resend邮件发送成功:', result);

      return NextResponse.json({
        success: true,
        message: 'Resend邮件发送成功！',
        messageId: result.data?.id,
        recipient: adminEmail,
        service: 'Resend',
        details: {
          from: fromEmail,
          to: adminEmail,
          subject: `🔔 新的表单提交 - ${testData.name}`,
          timestamp: new Date().toISOString()
        }
      });

    } catch (emailError) {
      console.error('Resend邮件发送失败:', emailError);
      
      // 记录到控制台作为备用
      console.log('='.repeat(60));
      console.log('Resend邮件发送失败，记录到控制台:');
      console.log(`收件人: ${adminEmail}`);
      console.log(`发件人: ${fromEmail}`);
      console.log('邮件内容:');
      console.log(`新的表单提交通知

客户信息:
- 姓名: ${testData.name}
- 邮箱: ${testData.email}
- 电话: ${testData.phone}
- 国家: ${testData.country}
- 场地大小: ${testData.playground_size}
- 感兴趣的产品: ${testData.product}

客户留言:
${testData.message}

提交时间: ${new Date(testData.created_at).toLocaleString('zh-CN')}
提交ID: #${testData.id}`);
      console.log('='.repeat(60));

      return NextResponse.json({
        success: false,
        message: 'Resend邮件发送失败，但已记录到控制台',
        error: emailError instanceof Error ? emailError.message : 'Unknown error',
        fallback: 'logged_to_console',
        recipient: adminEmail
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Resend邮件测试API错误:', error);
    return NextResponse.json({
      success: false,
      message: 'Resend邮件测试API发生错误',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Resend邮件测试API',
    endpoint: '/api/resend-email-test',
    method: 'POST',
    service: 'Resend',
    freeLimit: '3000封/月',
    configured: !!process.env.RESEND_API_KEY,
    recipient: process.env.ADMIN_EMAIL || '<EMAIL>'
  });
}
