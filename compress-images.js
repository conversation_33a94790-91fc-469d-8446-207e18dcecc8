const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// 图片压缩脚本
async function compressImages() {
  const inputDir = './public/images';
  const outputDir = './public/images-compressed';
  
  // 创建输出目录
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  console.log('🔄 开始压缩图片...');
  
  await processDirectory(inputDir, outputDir);
  
  console.log('✅ 图片压缩完成！');
  console.log('💡 请将 public/images 重命名为 public/images-backup');
  console.log('💡 将 public/images-compressed 重命名为 public/images');
}

async function processDirectory(inputDir, outputDir) {
  const items = fs.readdirSync(inputDir);
  
  for (const item of items) {
    const inputPath = path.join(inputDir, item);
    const outputPath = path.join(outputDir, item);
    
    const stat = fs.statSync(inputPath);
    
    if (stat.isDirectory()) {
      // 创建子目录
      if (!fs.existsSync(outputPath)) {
        fs.mkdirSync(outputPath, { recursive: true });
      }
      await processDirectory(inputPath, outputPath);
    } else if (isImageFile(item)) {
      await compressImage(inputPath, outputPath);
    } else {
      // 复制非图片文件
      fs.copyFileSync(inputPath, outputPath);
    }
  }
}

async function compressImage(inputPath, outputPath) {
  try {
    const ext = path.extname(inputPath).toLowerCase();
    
    if (ext === '.jpg' || ext === '.jpeg') {
      await sharp(inputPath)
        .jpeg({ quality: 70, progressive: true })
        .resize(1200, 800, { 
          fit: 'inside', 
          withoutEnlargement: true 
        })
        .toFile(outputPath);
    } else if (ext === '.png') {
      await sharp(inputPath)
        .png({ quality: 70, progressive: true })
        .resize(1200, 800, { 
          fit: 'inside', 
          withoutEnlargement: true 
        })
        .toFile(outputPath);
    } else {
      // 其他格式直接复制
      fs.copyFileSync(inputPath, outputPath);
    }
    
    const originalSize = fs.statSync(inputPath).size;
    const compressedSize = fs.statSync(outputPath).size;
    const savings = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);
    
    console.log(`✅ ${path.basename(inputPath)} - 压缩 ${savings}%`);
  } catch (error) {
    console.error(`❌ 压缩失败: ${inputPath}`, error.message);
    // 如果压缩失败，直接复制原文件
    fs.copyFileSync(inputPath, outputPath);
  }
}

function isImageFile(filename) {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
  return imageExtensions.includes(path.extname(filename).toLowerCase());
}

// 运行压缩
compressImages().catch(console.error);
