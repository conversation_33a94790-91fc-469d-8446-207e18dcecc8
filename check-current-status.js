const fetch = require('node-fetch');

async function checkCurrentStatus() {
  try {
    console.log('🔍 检查当前页面状态...\n');
    
    const response = await fetch('http://localhost:3000/zh/products/motion-sensing-climbing');
    const html = await response.text();
    
    console.log(`📄 页面大小: ${(html.length / 1024).toFixed(2)} KB`);
    
    // 检查布局修复
    console.log('\n🎨 布局检查:');
    console.log(`   py-12: ${html.includes('py-12') ? '✅' : '❌'}`);
    console.log(`   gap-8: ${html.includes('gap-8') ? '✅' : '❌'}`);
    console.log(`   py-64: ${html.includes('py-64') ? '❌ 仍存在' : '✅ 已修复'}`);
    console.log(`   scale-125: ${html.includes('scale-125') ? '❌ 仍存在' : '✅ 已修复'}`);
    
    // 检查产品信息区域
    console.log('\n📋 产品信息区域:');
    const infoChecks = [
      'Product Information Section',
      '产品信息',
      '产品概述', 
      '技术规格',
      '应用场景',
      '商业场所',
      '教育机构',
      '体验中心'
    ];
    
    infoChecks.forEach(check => {
      const found = html.includes(check);
      console.log(`   ${found ? '✅' : '❌'} ${check}`);
    });
    
    // 检查页面结构
    console.log('\n🏗️ 页面结构:');
    const divCount = (html.match(/<div/g) || []).length;
    console.log(`   div标签数量: ${divCount}`);
    
    const hasContainer = html.includes('product-detail-container');
    console.log(`   产品详情容器: ${hasContainer ? '✅' : '❌'}`);
    
    // 检查是否有产品信息区域的HTML
    const hasInfoSection = html.includes('产品信息') || html.includes('Product Information');
    console.log(`   产品信息区域: ${hasInfoSection ? '✅' : '❌'}`);
    
    // 如果没有产品信息区域，检查页面在哪里结束
    if (!hasInfoSection) {
      console.log('\n⚠️ 产品信息区域缺失，检查页面结束位置:');
      
      // 查找最后的有意义内容
      const lastContent = [
        '获取报价',
        '质保3年',
        '专业安装',
        'product-detail-container'
      ];
      
      lastContent.forEach(content => {
        const found = html.includes(content);
        console.log(`   ${content}: ${found ? '✅' : '❌'}`);
      });
      
      // 显示页面结尾
      console.log('\n📝 页面结尾 (最后300字符):');
      console.log(html.substring(html.length - 300));
    }
    
    // 总结
    console.log('\n📊 状态总结:');
    const layoutFixed = html.includes('py-12') && html.includes('gap-8') && !html.includes('py-64');
    const contentExists = html.includes('体感攀岩系统') && html.includes('获取报价');
    const infoSectionExists = html.includes('产品信息');
    
    console.log(`   布局修复: ${layoutFixed ? '✅' : '❌'}`);
    console.log(`   基本内容: ${contentExists ? '✅' : '❌'}`);
    console.log(`   信息区域: ${infoSectionExists ? '✅' : '❌'}`);
    
    if (layoutFixed && contentExists && !infoSectionExists) {
      console.log('\n🎯 下一步: 需要添加产品信息区域');
    } else if (layoutFixed && contentExists && infoSectionExists) {
      console.log('\n🎉 页面完整，可能需要调整样式');
    } else {
      console.log('\n⚠️ 仍有问题需要解决');
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  }
}

checkCurrentStatus();
