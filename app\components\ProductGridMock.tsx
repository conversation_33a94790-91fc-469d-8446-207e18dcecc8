'use client';

import { useState, useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import ModernProductCard from './ModernProductCard';
import { getProductsWithPagination } from '../../lib/mock-products';

// 产品类型定义
interface Product {
  id: number;
  name: string;
  slug: string;
  description?: string;
  image_url?: string;
  images?: string[];
  category?: string;
  features?: any;
  is_published?: boolean;
  is_featured?: boolean;
  in_stock?: boolean;
  price?: number;
}

// 产品网格组件
interface ProductGridProps {
  searchQuery?: string;
}

export default function ProductGridMock({ searchQuery = '' }: ProductGridProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [visibleCards, setVisibleCards] = useState<Set<number>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const pathname = usePathname();
  const lang = pathname?.split('/')[1] || 'zh';
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);
  const productsPerPage = 12; // 每页显示12个产品

  // 获取产品列表 - 使用模拟数据
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 300));
        
        // 使用模拟数据
        const result = getProductsWithPagination(currentPage, productsPerPage, searchQuery.trim() || undefined);
        
        // 为每个产品设置主图片
        const productsWithImages = result.products.map(product => ({
          ...product,
          image_url: product.images && product.images.length > 0 ? product.images[0] : '/images/placeholder.jpg'
        }));
        
        setProducts(productsWithImages);
        setTotalPages(result.pagination.pages);
        setTotalProducts(result.pagination.total);
        setError(null);
        
      } catch (err) {
        console.error('获取产品错误:', err);
        setError(err instanceof Error ? err.message : '未知错误');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [currentPage, productsPerPage, searchQuery]); // 当页面、每页数量或搜索查询变化时重新获取

  // 当搜索查询变化时，重置到第一页
  useEffect(() => {
    if (searchQuery !== undefined) {
      setCurrentPage(1);
    }
  }, [searchQuery]);

  // 卡片动画观察器
  useEffect(() => {
    const observerOptions = {
      threshold: 0.2,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const index = parseInt(entry.target.getAttribute('data-index') || '0');
          setVisibleCards(prev => new Set([...prev, index]));
        }
      });
    }, observerOptions);

    cardRefs.current.forEach((ref) => {
      if (ref) observer.observe(ref);
    });

    return () => observer.disconnect();
  }, [products]);

  // 加载状态
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="bg-gray-100 rounded-lg h-96 animate-pulse"
            style={{
              animationDelay: `${i * 0.1}s`,
              animationDuration: '1.5s'
            }}
          ></div>
        ))}
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
        <p className="text-red-700">加载产品时出错: {error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg"
        >
          重试
        </button>
      </div>
    );
  }

  // 无产品状态
  if (products.length === 0) {
    return (
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
        {searchQuery ? (
          <div>
            <p className="text-gray-700 text-lg mb-2">未找到匹配的产品</p>
            <p className="text-gray-500">搜索关键词: "{searchQuery}"</p>
            <p className="text-gray-500 mt-2">请尝试其他关键词或浏览所有产品</p>
          </div>
        ) : (
          <p className="text-gray-700">暂无产品</p>
        )}
      </div>
    );
  }

  // 分页处理函数
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setVisibleCards(new Set()); // 重置可见卡片
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // 渲染产品网格
  return (
    <>
      <style jsx>{`
        .product-card-container {
          opacity: 0;
          transform: translateY(30px) scale(0.95);
          transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .product-card-container.visible {
          opacity: 1;
          transform: translateY(0) scale(1);
        }

        .stagger-animation {
          transition-delay: calc(var(--index) * 0.1s);
        }

        .hover-lift {
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .hover-lift:hover {
          transform: translateY(-8px) scale(1.02);
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .pagination-button {
          transition: all 0.3s ease;
        }

        .pagination-button:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      `}</style>

      <div className="space-y-8">
        {/* 产品统计信息 */}
        <div className="text-center text-gray-600">
          {searchQuery ? (
            <p>搜索 "{searchQuery}" 找到 {totalProducts} 个产品，第 {currentPage} 页，共 {totalPages} 页</p>
          ) : (
            <p>共 {totalProducts} 个产品，第 {currentPage} 页，共 {totalPages} 页</p>
          )}
        </div>

        {/* 产品网格 - 4列布局以更好地显示12个产品 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {products.map((product, index) => (
            <div
              key={product.id}
              ref={(el) => (cardRefs.current[index] = el)}
              data-index={index}
              className={`product-card-container stagger-animation hover-lift ${
                visibleCards.has(index) ? 'visible' : ''
              }`}
              style={{
                '--index': index
              } as React.CSSProperties}
            >
              <ModernProductCard
                id={product.id}
                title={product.name}
                description={product.description || '暂无描述'}
                category={product.category || 'Interactive'}
                image={product.image_url}
                slug={product.slug}
              />
            </div>
          ))}
        </div>

        {/* 分页组件 */}
        {totalPages > 1 && (
          <div className="flex justify-center items-center space-x-2 mt-12">
            {/* 上一页按钮 */}
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={`pagination-button px-4 py-2 rounded-lg border ${
                currentPage === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              上一页
            </button>

            {/* 页码按钮 */}
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`pagination-button px-4 py-2 rounded-lg border ${
                  page === currentPage
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            ))}

            {/* 下一页按钮 */}
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`pagination-button px-4 py-2 rounded-lg border ${
                currentPage === totalPages
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              下一页
            </button>
          </div>
        )}
      </div>
    </>
  );
}
