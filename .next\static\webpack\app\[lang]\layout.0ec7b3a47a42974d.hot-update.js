/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lang]/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Ccomponents%5CHtmlLangSetter.tsx&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Ccomponents%5CHtmlLangSetter.tsx&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/HtmlLangSetter.tsx */ \"(app-pages-browser)/./app/components/HtmlLangSetter.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz1EJTNBJTVDQUlHQy1kbSU1Q0Nyb3NzLWJvcmRlciUyMEUtY29tbWVyY2UlMjBXZWJzaXRlJTIwUHJvamVjdCU1Q25leHRqcyU1Q2FwcCU1Q2NvbXBvbmVudHMlNUNIdG1sTGFuZ1NldHRlci50c3gmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzllNmUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxBSUdDLWRtXFxcXENyb3NzLWJvcmRlciBFLWNvbW1lcmNlIFdlYnNpdGUgUHJvamVjdFxcXFxuZXh0anNcXFxcYXBwXFxcXGNvbXBvbmVudHNcXFxcSHRtbExhbmdTZXR0ZXIudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Ccomponents%5CHtmlLangSetter.tsx&server=false!\n"));

/***/ })

});