{"c": ["app/[lang]/layout", "app/[lang]/products/[slug]/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./app/[lang]/products/[slug]/page.tsx", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5C%5Blang%5D%5Cproducts%5C%5Bslug%5D%5Cpage.tsx&server=false!"]}