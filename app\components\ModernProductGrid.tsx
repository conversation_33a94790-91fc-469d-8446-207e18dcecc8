'use client';

import ModernProductCard from './ModernProductCard';

interface Product {
  id?: string | number;
  title: string;
  description?: string;
  category?: string;
  image: string;
  url: string;
  slug?: string;
}

interface ModernProductGridProps {
  products: Product[];
  className?: string;
}

export default function ModernProductGrid({ products, className = '' }: ModernProductGridProps) {
  // 从URL中提取slug
  const extractSlugFromUrl = (url: string): string => {
    const parts = url.split('/');
    return parts[parts.length - 1] || 'default';
  };

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 ${className}`}>
      {products.map((product, index) => (
        <ModernProductCard
          key={product.id || index}
          id={product.id || (index + 1).toString().padStart(2, '0')}
          title={product.title}
          description={product.description || '暂无描述'}
          category={product.category || 'Interactive'}
          image={product.image}
          slug={product.slug || extractSlugFromUrl(product.url)}
        />
      ))}
    </div>
  );
}
