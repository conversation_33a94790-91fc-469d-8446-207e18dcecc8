<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品删除功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .product-card {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .product-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .product-card p {
            margin: 5px 0;
            color: #666;
        }
        .delete-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .delete-btn:hover {
            background-color: #c82333;
        }
        .delete-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>产品删除功能测试</h1>
    
    <div class="container">
        <h2>测试说明</h2>
        <p>此页面用于测试产品删除功能，包括：</p>
        <ul>
            <li>获取产品列表</li>
            <li>删除指定产品</li>
            <li>验证删除后数据库同步</li>
            <li>检查相关数据是否正确删除</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>1. 加载产品列表</h3>
        <button onclick="loadProducts()">加载产品</button>
        <div id="load-status" class="status info">点击按钮加载产品列表</div>
        <div id="products-grid"></div>
    </div>

    <div class="test-section">
        <h3>2. 删除测试</h3>
        <p>在上面的产品列表中点击"删除"按钮来测试删除功能</p>
        <div id="delete-status" class="status info">等待删除操作...</div>
    </div>

    <script>
        let products = [];

        async function loadProducts() {
            const statusEl = document.getElementById('load-status');
            const gridEl = document.getElementById('products-grid');
            
            statusEl.className = 'status info';
            statusEl.textContent = '正在加载产品...';
            gridEl.innerHTML = '';

            try {
                const response = await fetch('/api/products');
                const data = await response.json();
                
                console.log('产品数据:', data);
                
                if (data.success && data.products && data.products.length > 0) {
                    products = data.products;
                    statusEl.className = 'status success';
                    statusEl.textContent = `✅ 成功加载 ${data.products.length} 个产品`;
                    
                    data.products.forEach(product => {
                        const card = document.createElement('div');
                        card.className = 'product-card';
                        card.id = `product-${product._id}`;
                        card.innerHTML = `
                            <h3>${product.title || product.name || '未命名产品'}</h3>
                            <p><strong>ID:</strong> ${product._id || product.id}</p>
                            <p><strong>Slug:</strong> ${product.slug}</p>
                            <p><strong>类型:</strong> ${product.type}</p>
                            <p><strong>尺寸:</strong> ${product.size}</p>
                            <p><strong>状态:</strong> ${product.isPublished ? '已发布' : '草稿'}</p>
                            <button class="delete-btn" onclick="deleteProduct('${product._id || product.id}', '${product.title || product.name}')">
                                删除此产品
                            </button>
                        `;
                        gridEl.appendChild(card);
                    });
                } else {
                    statusEl.className = 'status error';
                    statusEl.textContent = '❌ 没有找到产品或加载失败';
                    console.log('响应数据:', data);
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ 加载失败: ${error.message}`;
                console.error('加载产品时出错:', error);
            }
        }

        async function deleteProduct(productId, productName) {
            const statusEl = document.getElementById('delete-status');
            const productCard = document.getElementById(`product-${productId}`);
            const deleteBtn = productCard.querySelector('.delete-btn');
            
            // 确认删除
            if (!confirm(`确定要删除产品 "${productName}" 吗？\n\n此操作将永久删除产品及其所有相关数据！`)) {
                return;
            }

            // 更新状态
            statusEl.className = 'status info';
            statusEl.textContent = `正在删除产品: ${productName}...`;
            deleteBtn.disabled = true;
            deleteBtn.textContent = '删除中...';

            try {
                console.log(`开始删除产品: ${productName} (ID: ${productId})`);
                
                const response = await fetch(`/api/products/${productId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                });

                const data = await response.json();
                console.log('删除响应:', data);

                if (response.ok && data.success) {
                    statusEl.className = 'status success';
                    statusEl.textContent = `✅ 产品 "${productName}" 删除成功！`;
                    
                    // 从页面中移除产品卡片
                    productCard.style.transition = 'opacity 0.3s';
                    productCard.style.opacity = '0.5';
                    setTimeout(() => {
                        productCard.remove();
                    }, 300);
                    
                    // 更新产品数组
                    products = products.filter(p => (p._id || p.id) !== productId);
                    
                    console.log(`产品删除成功: ${productName}`);
                } else {
                    throw new Error(data.message || '删除失败');
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ 删除失败: ${error.message}`;
                
                // 恢复按钮状态
                deleteBtn.disabled = false;
                deleteBtn.textContent = '删除此产品';
                
                console.error('删除产品时出错:', error);
            }
        }

        // 页面加载时自动加载产品
        window.addEventListener('load', () => {
            loadProducts();
        });
    </script>
</body>
</html>
