import { getDictionary } from '../../../utils/i18n';
import Link from 'next/link';
import { solutionPlaceholders } from '../../../utils/imagePlaceholder';
import ModernProductGrid from '../../../components/ModernProductGrid';

export default async function LargeSizePage({ params }: { params: { lang: string } }) {
  const { lang } = params;
  const dict = await getDictionary(lang);

  // 1000+ sqm 产品数据
  const products = [
    {
      url: `/${lang}/products/1200-sqm-customizable-indoor-playground-adventure-family-entertainment`,
      image: solutionPlaceholders[3],
      title:
        dict.solutions?.product4 ||
        '1200㎡ Customizable Indoor Playground Adventure & Family Entertainment',
    },
    {
      url: `/${lang}/products/2300-sqm-trampoline-park-custom-indoor-sports-arena`,
      image: solutionPlaceholders[2],
      title: dict.solutions?.product3 || '2300 Sqm Trampoline Park | Custom Indoor Sports Arena',
    },
  ];

  return (
    <>
      <section className="page-banner">
        <div className="container">
          <h1 className="page-title">
            {dict.products?.categories?.large || '1000+ SQM Playgrounds'}
          </h1>
          <div className="breadcrumb">
            <Link href={`/${lang}`}>{dict.common?.home || 'Home'}</Link> /
            <Link href={`/${lang}/products`}>{dict.common?.products || 'Products'}</Link> /
            <span>{dict.products?.filter?.large || '1000+ sqm'}</span>
          </div>
        </div>
      </section>

      <section className="products-page">
        <div className="container">
          <div className="page-header">
            <p className="page-description">
              {dict.products?.large?.description ||
                'Our 1000+ sqm playground solutions represent the ultimate in indoor play experiences. These large-scale entertainment destinations offer comprehensive play options, multiple attractions, and the capacity to become regional entertainment destinations.'}
            </p>
          </div>

          <div className="product-filters">
            <Link href={`/${lang}/products`} className="filter-item">
              {dict.products?.filter?.all || 'All Products'}
            </Link>
            <Link href={`/${lang}/products/indoor-playground`} className="filter-item">
              {dict.products?.filter?.indoor || 'Indoor Playgrounds'}
            </Link>
            <Link href={`/${lang}/products/trampoline-park`} className="filter-item">
              {dict.products?.filter?.trampoline || 'Trampoline Parks'}
            </Link>
          </div>

          <ModernProductGrid products={products} />

          <div className="size-benefits">
            <h2>{dict.products?.large?.benefits?.title || 'Benefits of 1000+ SQM Playgrounds'}</h2>
            <div className="benefits-grid">
              <div className="benefit-item">
                <div className="benefit-icon">
                  <i className="fas fa-globe"></i>
                </div>
                <h3>
                  {dict.products?.large?.benefits?.destination?.title || 'Destination Appeal'}
                </h3>
                <p>
                  {dict.products?.large?.benefits?.destination?.description ||
                    'Become a regional entertainment destination that attracts visitors from a wider geographic area.'}
                </p>
              </div>

              <div className="benefit-item">
                <div className="benefit-icon">
                  <i className="fas fa-layer-group"></i>
                </div>
                <h3>
                  {dict.products?.large?.benefits?.attractions?.title || 'Multiple Attractions'}
                </h3>
                <p>
                  {dict.products?.large?.benefits?.attractions?.description ||
                    'Space for diverse play zones, multiple attractions, and complementary entertainment options.'}
                </p>
              </div>

              <div className="benefit-item">
                <div className="benefit-icon">
                  <i className="fas fa-utensils"></i>
                </div>
                <h3>{dict.products?.large?.benefits?.amenities?.title || 'Expanded Amenities'}</h3>
                <p>
                  {dict.products?.large?.benefits?.amenities?.description ||
                    'Room for full-service restaurants, cafes, retail areas, and extensive seating for parents.'}
                </p>
              </div>

              <div className="benefit-item">
                <div className="benefit-icon">
                  <i className="fas fa-chart-bar"></i>
                </div>
                <h3>
                  {dict.products?.large?.benefits?.revenue?.title || 'Maximum Revenue Potential'}
                </h3>
                <p>
                  {dict.products?.large?.benefits?.revenue?.description ||
                    'Higher capacity and longer visit durations translate to greater revenue opportunities.'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="cta-section cta-particles">
        <div className="container">
          <div className="cta-content">
            <h2>准备好讨论您的全息定制解决方案？</h2>
            <p>今天就联系我们的团队，探索我们如何为您的需求创造完美的全息解决方案。</p>
            <Link
              href={`/${lang}/pages/contact-us`}
              className="btn-primary"
              data-text="立即联系我们"
            >
              {'立即联系我们'.split('').map((char, index) => (
                <i key={index}>{char === ' ' ? '\u00A0' : char}</i>
              ))}
            </Link>
          </div>
        </div>
      </section>
    </>
  );
}
