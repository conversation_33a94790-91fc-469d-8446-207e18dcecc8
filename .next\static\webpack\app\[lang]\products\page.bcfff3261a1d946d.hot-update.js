"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lang]/products/page",{

/***/ "(app-pages-browser)/./app/components/ProductGrid.tsx":
/*!****************************************!*\
  !*** ./app/components/ProductGrid.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductGrid; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _LanguageProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./LanguageProvider */ \"(app-pages-browser)/./app/components/LanguageProvider.tsx\");\n/* harmony import */ var _ModernProductCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ModernProductCard */ \"(app-pages-browser)/./app/components/ModernProductCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ProductGrid(param) {\n    let { searchQuery = \"\" } = param;\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [visibleCards, setVisibleCards] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Set());\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [totalProducts, setTotalProducts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const lang = (pathname === null || pathname === void 0 ? void 0 : pathname.split(\"/\")[1]) || \"zh\";\n    const cardRefs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);\n    const productsPerPage = 12; // 每页显示12个产品\n    const { t } = (0,_LanguageProvider__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    // 获取产品列表 - 使用API并传递语言参数\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const fetchProducts = async ()=>{\n            try {\n                var _allProducts_;\n                setLoading(true);\n                console.log(\"[ProductGrid] Fetching products, language: \".concat(lang, \", page: \").concat(currentPage));\n                // 使用API获取产品数据，传递语言参数\n                const response = await fetch(\"/api/products?lang=\".concat(lang, \"&page=\").concat(currentPage, \"&limit=\").concat(productsPerPage));\n                if (!response.ok) {\n                    throw new Error(\"\".concat(t(\"products.loading_error\", {\n                        fallback: \"Error loading products: \"\n                    })).concat(response.status));\n                }\n                const data = await response.json();\n                const allProducts = data.products || [];\n                console.log(\"[ProductGrid] Received products data, count: \".concat(allProducts.length, \", first product: \").concat((_allProducts_ = allProducts[0]) === null || _allProducts_ === void 0 ? void 0 : _allProducts_.name));\n                // 如果有搜索查询，在客户端进行过滤\n                let filteredProducts = allProducts;\n                if (searchQuery.trim()) {\n                    const query = searchQuery.trim().toLowerCase();\n                    filteredProducts = allProducts.filter((product)=>{\n                        var _product_description, _product_category, _product_features;\n                        return product.name.toLowerCase().includes(query) || ((_product_description = product.description) === null || _product_description === void 0 ? void 0 : _product_description.toLowerCase().includes(query)) || ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.toLowerCase().includes(query)) || ((_product_features = product.features) === null || _product_features === void 0 ? void 0 : _product_features.some((feature)=>feature.toLowerCase().includes(query)));\n                    });\n                }\n                // 设置产品数据（API已经处理了分页）\n                setProducts(filteredProducts);\n                setTotalProducts(data.total || filteredProducts.length);\n                setTotalPages(data.totalPages || Math.ceil((data.total || filteredProducts.length) / productsPerPage));\n            } catch (err) {\n                console.error(\"获取产品错误:\", err);\n                setError(err instanceof Error ? err.message : t(\"products.loading_error\", {\n                    fallback: \"Unknown error\"\n                }));\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchProducts();\n    }, [\n        currentPage,\n        productsPerPage,\n        searchQuery,\n        lang\n    ]); // 当页面、每页数量、搜索查询或语言变化时重新获取\n    // 当搜索查询变化时，重置到第一页\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (searchQuery !== undefined) {\n            setCurrentPage(1);\n        }\n    }, [\n        searchQuery\n    ]);\n    // 卡片动画观察器\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const observerOptions = {\n            threshold: 0.2,\n            rootMargin: \"0px 0px -50px 0px\"\n        };\n        const observer = new IntersectionObserver((entries)=>{\n            entries.forEach((entry)=>{\n                if (entry.isIntersecting) {\n                    const index = parseInt(entry.target.getAttribute(\"data-index\") || \"0\");\n                    setVisibleCards((prev)=>new Set([\n                            ...prev,\n                            index\n                        ]));\n                }\n            });\n        }, observerOptions);\n        cardRefs.current.forEach((ref)=>{\n            if (ref) observer.observe(ref);\n        });\n        return ()=>observer.disconnect();\n    }, [\n        products\n    ]);\n    // 加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n            children: [\n                ...Array(12)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-100 rounded-lg h-96 animate-pulse\",\n                    style: {\n                        animationDelay: \"\".concat(i * 0.1, \"s\"),\n                        animationDuration: \"1.5s\"\n                    }\n                }, i, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this);\n    }\n    // 错误状态\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-red-50 border border-red-200 rounded-lg p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-700\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>window.location.reload(),\n                    className: \"mt-4 bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg\",\n                    children: t(\"products.retry\", {\n                        fallback: \"Retry\"\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    // 无产品状态\n    if (products.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 border border-gray-200 rounded-lg p-8 text-center\",\n            children: searchQuery ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700 text-lg mb-2\",\n                        children: t(\"products.no_search_results\", {\n                            fallback: \"No matching products found\"\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: [\n                            t(\"products.search_keyword\", {\n                                fallback: \"Search keyword: \"\n                            }),\n                            '\"',\n                            searchQuery,\n                            '\"'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 mt-2\",\n                        children: t(\"products.try_other_keywords\", {\n                            fallback: \"Please try other keywords or browse all products\"\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                lineNumber: 155,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-700\",\n                children: t(\"products.no_products\", {\n                    fallback: \"No products available\"\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                lineNumber: 161,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    }\n    // 分页处理函数\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n        setVisibleCards(new Set()); // 重置可见卡片\n        // 滚动到顶部\n        window.scrollTo({\n            top: 0,\n            behavior: \"smooth\"\n        });\n    };\n    // 渲染产品网格\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"1b1507c3aa451a09\",\n                children: \".product-card-container.jsx-1b1507c3aa451a09{opacity:0;-webkit-transform:translatey(30px)scale(.95);-moz-transform:translatey(30px)scale(.95);-ms-transform:translatey(30px)scale(.95);-o-transform:translatey(30px)scale(.95);transform:translatey(30px)scale(.95);-webkit-transition:all.8s cubic-bezier(.4,0,.2,1);-moz-transition:all.8s cubic-bezier(.4,0,.2,1);-o-transition:all.8s cubic-bezier(.4,0,.2,1);transition:all.8s cubic-bezier(.4,0,.2,1)}.product-card-container.visible.jsx-1b1507c3aa451a09{opacity:1;-webkit-transform:translatey(0)scale(1);-moz-transform:translatey(0)scale(1);-ms-transform:translatey(0)scale(1);-o-transform:translatey(0)scale(1);transform:translatey(0)scale(1)}.stagger-animation.jsx-1b1507c3aa451a09{-webkit-transition-delay:-webkit-calc(var(--index)*.1s);-moz-transition-delay:-moz-calc(var(--index)*.1s);-o-transition-delay:calc(var(--index)*.1s);transition-delay:-webkit-calc(var(--index)*.1s);transition-delay:-moz-calc(var(--index)*.1s);transition-delay:calc(var(--index)*.1s)}.hover-lift.jsx-1b1507c3aa451a09{-webkit-transition:-webkit-transform.3s ease,box-shadow.3s ease;-moz-transition:-moz-transform.3s ease,box-shadow.3s ease;-o-transition:-o-transform.3s ease,box-shadow.3s ease;transition:-webkit-transform.3s ease,box-shadow.3s ease;transition:-moz-transform.3s ease,box-shadow.3s ease;transition:-o-transform.3s ease,box-shadow.3s ease;transition:transform.3s ease,box-shadow.3s ease}.hover-lift.jsx-1b1507c3aa451a09:hover{-webkit-transform:translatey(-8px)scale(1.02);-moz-transform:translatey(-8px)scale(1.02);-ms-transform:translatey(-8px)scale(1.02);-o-transform:translatey(-8px)scale(1.02);transform:translatey(-8px)scale(1.02);-webkit-box-shadow:0 20px 40px rgba(0,0,0,.15);-moz-box-shadow:0 20px 40px rgba(0,0,0,.15);box-shadow:0 20px 40px rgba(0,0,0,.15)}.pagination-button.jsx-1b1507c3aa451a09{-webkit-transition:all.3s ease;-moz-transition:all.3s ease;-o-transition:all.3s ease;transition:all.3s ease}.pagination-button.jsx-1b1507c3aa451a09:hover{-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px);-webkit-box-shadow:0 4px 12px rgba(0,0,0,.15);-moz-box-shadow:0 4px 12px rgba(0,0,0,.15);box-shadow:0 4px 12px rgba(0,0,0,.15)}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-1b1507c3aa451a09\" + \" \" + \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-1b1507c3aa451a09\" + \" \" + \"text-center text-gray-600\",\n                        children: searchQuery ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-1b1507c3aa451a09\",\n                            children: t(\"products.search_results\", {\n                                fallback: 'Search \"{{query}}\" found {{count}} products, page {{current}} of {{total}}',\n                                query: searchQuery,\n                                count: totalProducts,\n                                current: currentPage,\n                                total: totalPages\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-1b1507c3aa451a09\",\n                            children: t(\"products.total_products\", {\n                                fallback: \"Total {{count}} products, page {{current}} of {{total}}\",\n                                count: totalProducts,\n                                current: currentPage,\n                                total: totalPages\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-1b1507c3aa451a09\" + \" \" + \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\",\n                        children: products.map((product, index)=>{\n                            var _product_images;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: (el)=>cardRefs.current[index] = el,\n                                \"data-index\": index,\n                                style: {\n                                    \"--index\": index\n                                },\n                                className: \"jsx-1b1507c3aa451a09\" + \" \" + \"product-card-container stagger-animation hover-lift \".concat(visibleCards.has(index) ? \"visible\" : \"\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ModernProductCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    id: product.id,\n                                    title: product.name,\n                                    description: product.description || t(\"products.no_description\", {\n                                        fallback: \"No description available\"\n                                    }),\n                                    category: product.category || \"Interactive\",\n                                    image: ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : _product_images[0]) || product.image_url,\n                                    slug: product.slug\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this)\n                            }, product.id, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-1b1507c3aa451a09\" + \" \" + \"flex justify-center items-center space-x-2 mt-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(currentPage - 1),\n                                disabled: currentPage === 1,\n                                className: \"jsx-1b1507c3aa451a09\" + \" \" + \"pagination-button px-4 py-2 rounded-lg border \".concat(currentPage === 1 ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                children: t(\"products.previous_page\", {\n                                    fallback: \"Previous\"\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this),\n                            Array.from({\n                                length: totalPages\n                            }, (_, i)=>i + 1).map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handlePageChange(page),\n                                    className: \"jsx-1b1507c3aa451a09\" + \" \" + \"pagination-button px-4 py-2 rounded-lg border \".concat(page === currentPage ? \"bg-blue-600 text-white border-blue-600\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                    children: page\n                                }, page, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(currentPage + 1),\n                                disabled: currentPage === totalPages,\n                                className: \"jsx-1b1507c3aa451a09\" + \" \" + \"pagination-button px-4 py-2 rounded-lg border \".concat(currentPage === totalPages ? \"bg-gray-100 text-gray-400 cursor-not-allowed\" : \"bg-white text-gray-700 border-gray-300 hover:bg-gray-50\"),\n                                children: t(\"products.next_page\", {\n                                    fallback: \"Next\"\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductGrid.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ProductGrid, \"FimfZSYWdYcT2ZvgslAsX+xPbEs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _LanguageProvider__WEBPACK_IMPORTED_MODULE_4__.useLanguage\n    ];\n});\n_c = ProductGrid;\nvar _c;\n$RefreshReg$(_c, \"ProductGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ProductGrid.tsx\n"));

/***/ })

});