/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lang]/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Ccomponents%5CHtmlLangSetter.tsx&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cglobals.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cglobal-quote-form.css&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Ccomponents%5CHtmlLangSetter.tsx&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cglobals.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cglobal-quote-form.css&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/HtmlLangSetter.tsx */ \"(app-pages-browser)/./app/components/HtmlLangSetter.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/styles/globals.css */ \"(app-pages-browser)/./app/styles/globals.css\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/styles/global-quote-form.css */ \"(app-pages-browser)/./app/styles/global-quote-form.css\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz1EJTNBJTVDQUlHQy1kbSU1Q0Nyb3NzLWJvcmRlciUyMEUtY29tbWVyY2UlMjBXZWJzaXRlJTIwUHJvamVjdCU1Q25leHRqcyU1Q2FwcCU1Q2NvbXBvbmVudHMlNUNIdG1sTGFuZ1NldHRlci50c3gmbW9kdWxlcz1EJTNBJTVDQUlHQy1kbSU1Q0Nyb3NzLWJvcmRlciUyMEUtY29tbWVyY2UlMjBXZWJzaXRlJTIwUHJvamVjdCU1Q25leHRqcyU1Q2FwcCU1Q3N0eWxlcyU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9RCUzQSU1Q0FJR0MtZG0lNUNDcm9zcy1ib3JkZXIlMjBFLWNvbW1lcmNlJTIwV2Vic2l0ZSUyMFByb2plY3QlNUNuZXh0anMlNUNhcHAlNUNzdHlsZXMlNUNnbG9iYWwtcXVvdGUtZm9ybS5jc3Mmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBc0k7QUFDdEksMEtBQTJIO0FBQzNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/NjJlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEFJR0MtZG1cXFxcQ3Jvc3MtYm9yZGVyIEUtY29tbWVyY2UgV2Vic2l0ZSBQcm9qZWN0XFxcXG5leHRqc1xcXFxhcHBcXFxcY29tcG9uZW50c1xcXFxIdG1sTGFuZ1NldHRlci50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEFJR0MtZG1cXFxcQ3Jvc3MtYm9yZGVyIEUtY29tbWVyY2UgV2Vic2l0ZSBQcm9qZWN0XFxcXG5leHRqc1xcXFxhcHBcXFxcc3R5bGVzXFxcXGdsb2JhbHMuY3NzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxBSUdDLWRtXFxcXENyb3NzLWJvcmRlciBFLWNvbW1lcmNlIFdlYnNpdGUgUHJvamVjdFxcXFxuZXh0anNcXFxcYXBwXFxcXHN0eWxlc1xcXFxnbG9iYWwtcXVvdGUtZm9ybS5jc3NcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Ccomponents%5CHtmlLangSetter.tsx&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cglobals.css&modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5Cstyles%5Cglobal-quote-form.css&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/styles/global-quote-form.css":
/*!******************************************!*\
  !*** ./app/styles/global-quote-form.css ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"e7cb044dc5d9\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9zdHlsZXMvZ2xvYmFsLXF1b3RlLWZvcm0uY3NzIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9hcHAvc3R5bGVzL2dsb2JhbC1xdW90ZS1mb3JtLmNzcz82NmUzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZTdjYjA0NGRjNWQ5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/styles/global-quote-form.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/styles/globals.css":
/*!********************************!*\
  !*** ./app/styles/globals.css ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"292d996b3ace\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9zdHlsZXMvZ2xvYmFscy5jc3M/YmRlMiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjI5MmQ5OTZiM2FjZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/styles/globals.css\n"));

/***/ })

});