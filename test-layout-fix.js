const fetch = require('node-fetch');

async function testLayoutFix() {
  try {
    console.log('🎯 测试布局修复后的页面...\n');
    
    // 等待页面更新
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const response = await fetch('http://localhost:3000/zh/products/motion-sensing-climbing');
    const html = await response.text();
    
    console.log('📄 页面基本信息:');
    console.log(`   状态码: ${response.status}`);
    console.log(`   页面大小: ${(html.length / 1024).toFixed(2)} KB`);
    
    // 检查布局问题是否修复
    console.log('\n🔧 布局修复检查:');
    
    const hasPy64 = html.includes('py-64');
    const hasScale125 = html.includes('scale-125');
    const hasGap24 = html.includes('gap-24');
    const hasPy12 = html.includes('py-12');
    const hasGap8 = html.includes('gap-8');
    
    console.log(`   ❌ 巨大padding (py-64): ${hasPy64 ? '仍存在' : '已修复'}`);
    console.log(`   ❌ 缩放问题 (scale-125): ${hasScale125 ? '仍存在' : '已修复'}`);
    console.log(`   ❌ 过大间距 (gap-24): ${hasGap24 ? '仍存在' : '已修复'}`);
    console.log(`   ✅ 正常padding (py-12): ${hasPy12 ? '已应用' : '未应用'}`);
    console.log(`   ✅ 正常间距 (gap-8): ${hasGap8 ? '已应用' : '未应用'}`);
    
    // 检查产品信息区域
    console.log('\n📋 产品信息区域检查:');
    
    const checks = [
      { name: '产品标题', pattern: /体感攀岩系统/, found: false },
      { name: '产品信息标题', pattern: /产品信息/, found: false },
      { name: '产品概述', pattern: /产品概述/, found: false },
      { name: '技术规格', pattern: /技术规格/, found: false },
      { name: '应用场景', pattern: /应用场景/, found: false },
      { name: '获取报价按钮', pattern: /获取报价/, found: false }
    ];
    
    checks.forEach(check => {
      check.found = check.pattern.test(html);
    });
    
    checks.forEach(check => {
      console.log(`   ${check.found ? '✅' : '❌'} ${check.name}: ${check.found ? '存在' : '缺失'}`);
    });
    
    // 统计结果
    const foundCount = checks.filter(c => c.found).length;
    const totalCount = checks.length;
    const percentage = Math.round((foundCount / totalCount) * 100);
    
    console.log(`\n📊 内容完整度: ${foundCount}/${totalCount} (${percentage}%)`);
    
    // 布局修复评估
    const layoutIssues = [hasPy64, hasScale125, hasGap24].filter(Boolean).length;
    const layoutFixes = [hasPy12, hasGap8].filter(Boolean).length;
    
    console.log('\n🎨 布局修复评估:');
    if (layoutIssues === 0 && layoutFixes === 2) {
      console.log('✅ 布局问题已完全修复！');
    } else if (layoutIssues <= 1) {
      console.log('⚠️ 布局基本修复，仍有少量问题');
    } else {
      console.log('❌ 布局问题仍然存在');
    }
    
    // 检查页面是否能正常滚动
    const hasProductDetailContainer = html.includes('product-detail-container');
    const hasProductInfoSection = html.includes('Product Information Section');
    
    console.log('\n📱 页面结构检查:');
    console.log(`   产品详情容器: ${hasProductDetailContainer ? '✅' : '❌'}`);
    console.log(`   产品信息区域: ${hasProductInfoSection ? '✅' : '❌'}`);
    
    if (percentage >= 80 && layoutIssues === 0) {
      console.log('\n🎉 页面修复成功！内容和布局都正常！');
    } else if (percentage >= 50) {
      console.log('\n✅ 页面部分修复，主要内容可见');
    } else {
      console.log('\n⚠️ 页面仍需进一步修复');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testLayoutFix();
