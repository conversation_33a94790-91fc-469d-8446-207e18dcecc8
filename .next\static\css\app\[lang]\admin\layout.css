/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/admin.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/* 管理员后台专用样式 */

/* 管理员后台基础样式 */
.admin-container {
  min-height: 100vh;
  background-color: #f3f4f6;
}

/* 侧边栏样式 */
.admin-sidebar {
  background-color: #1f2937;
  color: white;
  width: 256px;
  min-height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 40;
  overflow-y: auto;
}

.admin-sidebar-header {
  padding: 1rem;
  background-color: #111827;
  border-bottom: 1px solid #374151;
}

.admin-sidebar-nav {
  padding: 1rem 0;
}

.admin-nav-item {
  display: block;
  padding: 0.75rem 1rem;
  color: #d1d5db;
  text-decoration: none;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.admin-nav-item:hover {
  background-color: #374151;
  color: white;
  border-left-color: #3b82f6;
}

.admin-nav-item.active {
  background-color: #1f2937;
  color: white;
  border-left-color: #3b82f6;
}

.admin-nav-item i {
  margin-right: 0.75rem;
  width: 1rem;
  text-align: center;
}

/* 主内容区域 */
.admin-main {
  margin-left: 256px;
  min-height: 100vh;
}

.admin-header {
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 2rem;
  display: flex;
  justify-content: between;
  align-items: center;
}

.admin-content {
  padding: 2rem;
}

/* 卡片样式 */
.admin-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.admin-card-header {
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}

.admin-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

/* 统计卡片 */
.stats-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  border-left: 4px solid #3b82f6;
}

.stats-card.green {
  border-left-color: #10b981;
}

.stats-card.purple {
  border-left-color: #8b5cf6;
}

.stats-card.red {
  border-left-color: #ef4444;
}

.stats-number {
  font-size: 2rem;
  font-weight: 700;
  color: #111827;
  line-height: 1;
}

.stats-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-success {
  background-color: #10b981;
  color: white;
}

.btn-success:hover {
  background-color: #059669;
}

.btn-danger {
  background-color: #ef4444;
  color: white;
}

.btn-danger:hover {
  background-color: #dc2626;
}

/* 表格样式 */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.admin-table th {
  background-color: #f9fafb;
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.admin-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #f3f4f6;
  color: #111827;
}

.admin-table tr:hover {
  background-color: #f9fafb;
}

/* 表单样式 */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  resize: vertical;
  min-height: 100px;
}

.form-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background-color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .admin-sidebar.open {
    transform: translateX(0);
  }

  .admin-main {
    margin-left: 0;
  }

  .admin-header {
    padding: 1rem;
  }

  .admin-content {
    padding: 1rem;
  }
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 通知样式 */
.notification {
  padding: 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.notification.success {
  background-color: #d1fae5;
  border: 1px solid #a7f3d0;
  color: #065f46;
}

.notification.error {
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  color: #991b1b;
}

.notification.warning {
  background-color: #fef3c7;
  border: 1px solid #fde68a;
  color: #92400e;
}

.notification.info {
  background-color: #dbeafe;
  border: 1px solid #bfdbfe;
  color: #1e40af;
}

