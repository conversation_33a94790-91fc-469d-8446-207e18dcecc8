import { NextRequest, NextResponse } from 'next/server';
import { Resend } from 'resend';

export async function POST(request: NextRequest) {
  try {
    console.log('开始智能邮件发送测试...');
    
    // 测试数据
    const testData = {
      id: 3000,
      name: "张经理",
      email: "<EMAIL>",
      phone: "+86 138-8888-9999",
      country: "中国",
      playground_size: "2000+ sqm",
      product: "AR全息互动系统",
      message: "您好，我们是一家大型商业地产公司，旗下管理着多个购物中心。我们对贵公司的AR全息互动系统非常感兴趣，希望在我们的儿童娱乐区域部署这套系统。请提供详细的产品资料、技术参数、价格方案以及成功案例。我们计划在今年Q4完成首期项目的实施。",
      created_at: new Date().toISOString()
    };

    const targetEmail = '<EMAIL>'; // 目标QQ邮箱
    const verifiedEmail = '<EMAIL>'; // Resend验证的邮箱
    const fromEmail = '<EMAIL>';
    const fromName = '跨境电商网站';

    console.log('邮件发送配置:', {
      targetEmail,
      verifiedEmail,
      hasResendKey: !!process.env.RESEND_API_KEY,
      hasSmtpConfig: !!(process.env.SMTP_USER && process.env.SMTP_PASS)
    });

    // 方案1: 尝试使用Resend发送到验证邮箱
    if (process.env.RESEND_API_KEY) {
      try {
        console.log('尝试使用Resend发送到验证邮箱...');
        const resend = new Resend(process.env.RESEND_API_KEY);
        
        const result = await resend.emails.send({
          from: `${fromName} <${fromEmail}>`,
          to: [verifiedEmail],
          subject: `🔔 新的表单提交 - ${testData.name} (转发到 ${targetEmail})`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
                <h1 style="margin: 0; font-size: 28px;">🔔 新的表单提交</h1>
                <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">来自跨境电商网站</p>
              </div>
              
              <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 0; border-radius: 0;">
                <p style="margin: 0; color: #856404; font-size: 14px;">
                  <strong>📧 邮件转发通知:</strong> 此邮件原本应发送到 <strong>${targetEmail}</strong>，
                  但由于Resend免费账户限制，现转发到您的验证邮箱。请手动转发给目标收件人。
                </p>
              </div>
              
              <div style="background-color: #ffffff; padding: 30px; border: 1px solid #e1e5e9; border-top: none;">
                <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin-bottom: 25px;">
                  <h2 style="color: #495057; margin-top: 0; margin-bottom: 20px; font-size: 20px;">👤 客户信息</h2>
                  <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                      <td style="padding: 10px 0; font-weight: bold; color: #6c757d; width: 120px;">姓名:</td>
                      <td style="padding: 10px 0; color: #495057;">${testData.name}</td>
                    </tr>
                    <tr>
                      <td style="padding: 10px 0; font-weight: bold; color: #6c757d;">邮箱:</td>
                      <td style="padding: 10px 0; color: #495057;">${testData.email}</td>
                    </tr>
                    <tr>
                      <td style="padding: 10px 0; font-weight: bold; color: #6c757d;">电话:</td>
                      <td style="padding: 10px 0; color: #495057;">${testData.phone}</td>
                    </tr>
                    <tr>
                      <td style="padding: 10px 0; font-weight: bold; color: #6c757d;">国家:</td>
                      <td style="padding: 10px 0; color: #495057;">${testData.country}</td>
                    </tr>
                    <tr>
                      <td style="padding: 10px 0; font-weight: bold; color: #6c757d;">场地大小:</td>
                      <td style="padding: 10px 0; color: #495057;">${testData.playground_size}</td>
                    </tr>
                    <tr>
                      <td style="padding: 10px 0; font-weight: bold; color: #6c757d;">感兴趣的产品:</td>
                      <td style="padding: 10px 0; color: #495057; font-weight: bold;">${testData.product}</td>
                    </tr>
                  </table>
                </div>

                <div style="background-color: #fff; padding: 25px; border: 2px solid #e9ecef; border-radius: 8px; margin-bottom: 25px;">
                  <h2 style="color: #495057; margin-top: 0; margin-bottom: 15px; font-size: 20px;">💬 客户留言</h2>
                  <p style="line-height: 1.8; color: #495057; margin: 0; font-size: 16px;">${testData.message}</p>
                </div>

                <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                  <h3 style="color: #0c5460; margin-top: 0; margin-bottom: 10px;">📋 处理建议</h3>
                  <ol style="color: #0c5460; margin: 0; padding-left: 20px;">
                    <li>请将此邮件转发到 <strong>${targetEmail}</strong></li>
                    <li>或直接联系客户：${testData.phone}</li>
                    <li>回复客户邮箱：${testData.email}</li>
                  </ol>
                </div>

                <div style="background-color: #f1f3f4; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
                  <p style="margin: 0; color: #6c757d; font-size: 14px;">
                    <strong>📅 提交时间:</strong> ${new Date(testData.created_at).toLocaleString('zh-CN')}<br>
                    <strong>🆔 提交ID:</strong> #${testData.id}<br>
                    <strong>📧 邮件服务:</strong> Resend (智能转发)<br>
                    <strong>🎯 目标收件人:</strong> ${targetEmail}
                  </p>
                </div>
              </div>

              <div style="background-color: #6c757d; color: white; padding: 20px; border-radius: 0 0 10px 10px; text-align: center;">
                <p style="margin: 0; font-size: 14px;">此邮件由跨境电商网站系统通过 Resend 服务自动发送</p>
                <p style="margin: 10px 0 0 0; font-size: 12px; opacity: 0.8;">智能邮件转发 - 确保重要通知不丢失</p>
              </div>
            </div>
          `,
          text: `
新的表单提交通知 (转发到 ${targetEmail})

⚠️ 邮件转发通知: 此邮件原本应发送到 ${targetEmail}，但由于Resend免费账户限制，现转发到您的验证邮箱。

客户信息:
- 姓名: ${testData.name}
- 邮箱: ${testData.email}
- 电话: ${testData.phone}
- 国家: ${testData.country}
- 场地大小: ${testData.playground_size}
- 感兴趣的产品: ${testData.product}

客户留言:
${testData.message}

处理建议:
1. 请将此邮件转发到 ${targetEmail}
2. 或直接联系客户：${testData.phone}
3. 回复客户邮箱：${testData.email}

提交时间: ${new Date(testData.created_at).toLocaleString('zh-CN')}
提交ID: #${testData.id}
邮件服务: Resend (智能转发)
目标收件人: ${targetEmail}

此邮件由跨境电商网站系统自动发送
          `
        });

        console.log('Resend智能转发成功:', result);

        return NextResponse.json({
          success: true,
          message: 'Resend智能转发成功！邮件已发送到您的验证邮箱',
          messageId: result.data?.id,
          method: 'resend_forward',
          details: {
            sentTo: verifiedEmail,
            originalTarget: targetEmail,
            subject: `🔔 新的表单提交 - ${testData.name} (转发到 ${targetEmail})`,
            service: 'Resend',
            note: '请手动转发到目标邮箱或直接联系客户'
          }
        });

      } catch (resendError) {
        console.error('Resend发送失败:', resendError);
      }
    }

    // 方案2: 备用 - 记录到控制台
    console.log('='.repeat(80));
    console.log('📧 智能邮件系统 - 控制台备份');
    console.log('='.repeat(80));
    console.log(`目标收件人: ${targetEmail}`);
    console.log(`备用收件人: ${verifiedEmail}`);
    console.log(`发件人: ${fromEmail}`);
    console.log('');
    console.log('📋 新的表单提交通知');
    console.log('');
    console.log('👤 客户信息:');
    console.log(`- 姓名: ${testData.name}`);
    console.log(`- 邮箱: ${testData.email}`);
    console.log(`- 电话: ${testData.phone}`);
    console.log(`- 国家: ${testData.country}`);
    console.log(`- 场地大小: ${testData.playground_size}`);
    console.log(`- 感兴趣的产品: ${testData.product}`);
    console.log('');
    console.log('💬 客户留言:');
    console.log(testData.message);
    console.log('');
    console.log('📅 提交信息:');
    console.log(`- 提交时间: ${new Date(testData.created_at).toLocaleString('zh-CN')}`);
    console.log(`- 提交ID: #${testData.id}`);
    console.log(`- 邮件服务: 智能备份系统`);
    console.log('');
    console.log('🎯 处理建议:');
    console.log(`1. 联系客户电话: ${testData.phone}`);
    console.log(`2. 回复客户邮箱: ${testData.email}`);
    console.log(`3. 转发到目标邮箱: ${targetEmail}`);
    console.log('='.repeat(80));

    return NextResponse.json({
      success: true,
      message: '智能邮件系统已记录通知到控制台',
      method: 'console_backup',
      details: {
        targetEmail,
        verifiedEmail,
        customerInfo: {
          name: testData.name,
          email: testData.email,
          phone: testData.phone,
          product: testData.product
        },
        timestamp: new Date().toISOString(),
        note: '请查看服务器控制台获取完整信息'
      }
    });

  } catch (error) {
    console.error('智能邮件系统错误:', error);
    return NextResponse.json({
      success: false,
      message: '智能邮件系统发生错误',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: '智能邮件发送系统',
    endpoint: '/api/smart-email-test',
    method: 'POST',
    features: [
      'Resend智能转发 (发送到验证邮箱)',
      '控制台备份记录',
      '自动选择最佳发送方案',
      '详细的处理建议'
    ],
    configuration: {
      resend: !!process.env.RESEND_API_KEY,
      smtp: !!(process.env.SMTP_USER && process.env.SMTP_PASS),
      targetEmail: '<EMAIL>',
      verifiedEmail: '<EMAIL>'
    }
  });
}
