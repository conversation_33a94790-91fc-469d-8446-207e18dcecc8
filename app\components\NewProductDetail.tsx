'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

// 产品接口定义
interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  price?: number;
  category: string;
  image_url: string;
  images?: string[];
  detail_images?: string[];
}

interface NewProductDetailProps {
  product: Product;
  lang: string;
}

const NewProductDetail: React.FC<NewProductDetailProps> = ({ product, lang }) => {
  const [selectedThumbnail, setSelectedThumbnail] = useState(0);
  
  // 处理缩略图点击
  const handleThumbnailClick = (index: number) => {
    setSelectedThumbnail(index);
  };

  // 构建图片数组
  const images = product.images?.slice() || [];
  if (product.image_url && !images.includes(product.image_url)) {
    images.unshift(product.image_url);
  }
  
  // 确保至少有一张图片
  if (images.length === 0) {
    images.push('/images/placeholder.jpg');
  }
  
  // 构建详情大图数组
  const detailImages = product.detail_images || [];
  
  // 翻译文本
  const translate = (key: string): string => {
    const translations: Record<string, Record<string, string>> = {
      introduction: {
        en: 'Introduction:',
        zh: '介绍:',
      },
      contactPurchase: {
        en: 'Contact for Purchase',
        zh: '联系购买',
      },
    };
    
    return translations[key]?.[lang] || key;
  };

  return (
    <div className="product-detail-container w-full">
      {/* 上部产品展示区 */}
      <div className="product-showcase w-full border-2 border-black p-4 mb-6 flex bg-white" style={{ minHeight: '400px' }}>
        {/* 左侧缩略图列表 */}
        <div className="thumbnails-column w-1/5 pr-4">
          {images.slice(0, 4).map((image, index) => (
            <div 
              key={index}
              className={`thumbnail-item w-full h-20 mb-2 border ${selectedThumbnail === index ? 'border-blue-500' : 'border-gray-300'} cursor-pointer`}
              onClick={() => handleThumbnailClick(index)}
            >
              <div className="relative w-full h-full">
                <Image 
                  src={image}
                  alt={`${product.name} thumbnail ${index + 1}`}
                  fill
                  sizes="100px"
                  style={{ objectFit: 'cover' }}
                />
              </div>
            </div>
          ))}
        </div>
        
        {/* 右侧主图显示区 */}
        <div className="main-image-container w-4/5 bg-white">
          <div className="relative w-full h-full" style={{ minHeight: '350px' }}>
            <Image 
              src={images[selectedThumbnail]}
              alt={product.name}
              fill
              sizes="(max-width: 768px) 100vw, 70vw"
              style={{ objectFit: 'contain' }}
              priority
            />
          </div>
        </div>
      </div>
      
      {/* 产品介绍和购买按钮 */}
      <div className="product-info-section mb-8 flex justify-between items-center">
        <div className="product-intro flex-1 mr-4">
          <span className="font-medium">{translate('introduction')}</span>
          <p className="text-gray-700">{product.description}</p>
        </div>
        
        <button className="contact-purchase-btn flex-shrink-0 bg-white border border-gray-300 rounded-full px-6 py-2 shadow-md hover:shadow-lg transition-shadow">
          {translate('contactPurchase')}
        </button>
      </div>
      
      {/* 产品详情大图展示 */}
      <div className="product-detail-images w-full">
        {(detailImages.length > 0 ? detailImages : images).map((image, index) => (
          <div key={index} className="detail-image-container w-full mb-6 bg-black">
            <div className="relative aspect-video w-full">
              <Image 
                src={image}
                alt={`${product.name} detail ${index + 1}`}
                fill
                sizes="100vw"
                style={{ objectFit: 'contain' }}
              />
              <div className="absolute inset-0 flex items-center justify-center text-white text-2xl font-bold">
                产品大图片
              </div>
            </div>
          </div>
        ))}
      </div>

      <style jsx>{`
        .product-showcase {
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .thumbnail-item {
          transition: all 0.2s ease;
        }
        .thumbnail-item:hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .contact-purchase-btn {
          transition: all 0.3s ease;
        }
        .contact-purchase-btn:hover {
          background-color: #f8f8f8;
          transform: translateY(-1px);
        }
        .detail-image-container {
          border-radius: 4px;
          overflow: hidden;
        }
      `}</style>
    </div>
  );
};

export default NewProductDetail; 