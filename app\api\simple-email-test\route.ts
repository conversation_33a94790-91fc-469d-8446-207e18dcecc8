import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('开始简单邮件测试...');
    
    // 测试数据
    const testData = {
      id: 999,
      name: "张先生",
      email: "<EMAIL>",
      phone: "+86 138-0013-8000",
      country: "中国",
      playground_size: "200-500 sqm",
      product: "AR互动蹦床系统",
      message: "您好，我对贵公司的AR互动蹦床产品很感兴趣，希望了解更多详细信息、技术参数和报价。我们计划在儿童乐园中安装此设备。",
      created_at: new Date().toISOString()
    };

    // 获取邮件配置
    const emailConfig = {
      smtp_host: process.env.SMTP_HOST || 'smtp.163.com',
      smtp_port: parseInt(process.env.SMTP_PORT || '587'),
      smtp_user: process.env.SMTP_USER || '',
      smtp_pass: process.env.SMTP_PASS || '',
      from_email: process.env.FROM_EMAIL || process.env.SMTP_USER || '',
      from_name: process.env.FROM_NAME || '跨境电商网站',
      admin_email: process.env.ADMIN_EMAIL || '<EMAIL>',
    };

    console.log('邮件配置:', {
      smtp_host: emailConfig.smtp_host,
      smtp_port: emailConfig.smtp_port,
      smtp_user: emailConfig.smtp_user,
      from_email: emailConfig.from_email,
      admin_email: emailConfig.admin_email,
      has_password: !!emailConfig.smtp_pass
    });

    // 检查配置
    if (!emailConfig.smtp_user || !emailConfig.smtp_pass || !emailConfig.admin_email) {
      return NextResponse.json({
        success: false,
        message: '邮件配置不完整',
        config: {
          has_smtp_user: !!emailConfig.smtp_user,
          has_smtp_pass: !!emailConfig.smtp_pass,
          has_admin_email: !!emailConfig.admin_email
        }
      }, { status: 400 });
    }

    // 尝试发送邮件
    try {
      const nodemailer = require('nodemailer');
      
      console.log('创建SMTP传输器...');
      const transporter = nodemailer.createTransport({
        host: emailConfig.smtp_host,
        port: emailConfig.smtp_port,
        secure: false, // 使用STARTTLS
        requireTLS: true, // 要求TLS
        auth: {
          user: emailConfig.smtp_user,
          pass: emailConfig.smtp_pass,
        },
        tls: {
          ciphers: 'SSLv3',
          rejectUnauthorized: false
        },
        debug: true, // 启用调试
        logger: true // 启用日志
      });

      // 验证连接
      console.log('验证SMTP连接...');
      await transporter.verify();
      console.log('SMTP连接验证成功');

      // 邮件内容
      const subject = `🔔 新的表单提交 - ${testData.name}`;
      const htmlContent = `
        <h2>新的表单提交通知</h2>
        <h3>客户信息:</h3>
        <ul>
          <li><strong>姓名:</strong> ${testData.name}</li>
          <li><strong>邮箱:</strong> ${testData.email}</li>
          <li><strong>电话:</strong> ${testData.phone}</li>
          <li><strong>国家:</strong> ${testData.country}</li>
          <li><strong>场地大小:</strong> ${testData.playground_size}</li>
          <li><strong>感兴趣的产品:</strong> ${testData.product}</li>
        </ul>
        <h3>客户留言:</h3>
        <p>${testData.message}</p>
        <hr>
        <p><strong>提交时间:</strong> ${new Date(testData.created_at).toLocaleString('zh-CN')}</p>
        <p><strong>提交ID:</strong> #${testData.id}</p>
        <p>此邮件由跨境电商网站系统自动发送</p>
      `;

      const textContent = `
新的表单提交通知

客户信息:
- 姓名: ${testData.name}
- 邮箱: ${testData.email}
- 电话: ${testData.phone}
- 国家: ${testData.country}
- 场地大小: ${testData.playground_size}
- 感兴趣的产品: ${testData.product}

客户留言:
${testData.message}

提交时间: ${new Date(testData.created_at).toLocaleString('zh-CN')}
提交ID: #${testData.id}

此邮件由跨境电商网站系统自动发送
      `;

      const mailOptions = {
        from: `"${emailConfig.from_name}" <${emailConfig.from_email}>`,
        to: emailConfig.admin_email,
        subject: subject,
        text: textContent,
        html: htmlContent,
      };

      console.log('发送邮件到:', emailConfig.admin_email);
      const result = await transporter.sendMail(mailOptions);
      console.log('邮件发送成功:', result.messageId);

      return NextResponse.json({
        success: true,
        message: '测试邮件发送成功',
        messageId: result.messageId,
        recipient: emailConfig.admin_email
      });

    } catch (emailError) {
      console.error('邮件发送失败:', emailError);
      
      // 记录到控制台作为备用
      console.log('='.repeat(50));
      console.log('邮件发送失败，记录到控制台:');
      console.log(`收件人: ${emailConfig.admin_email}`);
      console.log(`发件人: ${emailConfig.from_email}`);
      console.log('邮件内容:');
      console.log(`新的表单提交通知

客户信息:
- 姓名: ${testData.name}
- 邮箱: ${testData.email}
- 电话: ${testData.phone}
- 国家: ${testData.country}
- 场地大小: ${testData.playground_size}
- 感兴趣的产品: ${testData.product}

客户留言:
${testData.message}

提交时间: ${new Date(testData.created_at).toLocaleString('zh-CN')}
提交ID: #${testData.id}`);
      console.log('='.repeat(50));

      return NextResponse.json({
        success: false,
        message: '邮件发送失败，但已记录到控制台',
        error: emailError instanceof Error ? emailError.message : 'Unknown error',
        fallback: 'logged_to_console'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('测试邮件API错误:', error);
    return NextResponse.json({
      success: false,
      message: '测试邮件API发生错误',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: '邮件测试API - 使用POST方法发送测试邮件',
    endpoint: '/api/simple-email-test',
    method: 'POST'
  });
}
