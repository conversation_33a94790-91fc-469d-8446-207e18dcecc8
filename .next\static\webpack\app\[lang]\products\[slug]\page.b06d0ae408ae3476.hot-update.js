"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lang]/products/[slug]/page",{

/***/ "(app-pages-browser)/./app/[lang]/products/[slug]/page.tsx":
/*!*********************************************!*\
  !*** ./app/[lang]/products/[slug]/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _utils_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/i18n */ \"(app-pages-browser)/./app/utils/i18n.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// 移除复杂的导入\n// 产品骨架屏组件\nfunction ProductSkeleton() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-16 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-4 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-20 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-4 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-24 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 md:px-8 lg:px-12 py-8 md:py-12 lg:py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-200 rounded-2xl product-image-1920x1080\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-3\",\n                                            children: [\n                                                ...Array(4)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-200 rounded-xl aspect-video\"\n                                                }, i, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8 lg:pl-8 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-12 w-4/5 bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 44,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-8 w-24 bg-gray-200 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 w-full bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 w-full bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 w-3/4 bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 w-20 bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3\",\n                                                    children: [\n                                                        ...Array(4)\n                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-12 bg-gray-200 rounded-xl\"\n                                                        }, i, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 60,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4 pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-14 w-48 bg-gray-200 rounded-2xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 w-20 bg-gray-200 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 69,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 w-16 bg-gray-200 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 70,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_c = ProductSkeleton;\n// 产品详情页面组件\nfunction ProductPage(param) {\n    let { params } = param;\n    var _dict_common, _dict_common1, _product_images, _dict_common2, _dict_common3, _dict_common4, _dict_common5, _dict_common6, _dict_common7, _dict_common8, _dict_common9;\n    _s();\n    const { slug, lang } = params;\n    // 状态\n    const [dict, setDict] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedImageIndex, setSelectedImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    // 获取数据 - 使用国际化API\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // 定义一个异步函数来获取数据\n        const fetchData = async ()=>{\n            console.log(\"[产品详情] 开始加载产品数据: \".concat(slug, \", 语言: \").concat(lang));\n            try {\n                setLoading(true);\n                setError(null);\n                // 获取字典\n                const dictionary = await (0,_utils_i18n__WEBPACK_IMPORTED_MODULE_1__.getDictionary)(lang);\n                setDict(dictionary);\n                // 使用国际化API获取产品详情\n                const response = await fetch(\"/api/products/by-slug/\".concat(slug, \"?lang=\").concat(lang));\n                if (response.ok) {\n                    const productData = await response.json();\n                    if (productData.product) {\n                        console.log(\"[产品详情] 成功获取产品数据:\", productData.product.name);\n                        setProduct(productData.product);\n                        setLoading(false);\n                        return;\n                    }\n                }\n                // 如果API失败，回退到直接读取JSON文件\n                console.log(\"[产品详情] API失败，回退到JSON文件\");\n                const jsonResponse = await fetch(\"/mock-products.json\");\n                if (jsonResponse.ok) {\n                    const products = await jsonResponse.json();\n                    const foundProduct = products.find((p)=>p.slug === slug);\n                    if (foundProduct) {\n                        // 根据语言选择相应的字段\n                        const localizedProduct = {\n                            ...foundProduct,\n                            name: lang === \"en\" ? foundProduct.name_en || foundProduct.name : foundProduct.name,\n                            description: lang === \"en\" ? foundProduct.description_en || foundProduct.description : foundProduct.description,\n                            category: lang === \"en\" ? foundProduct.category_en || foundProduct.category : foundProduct.category,\n                            features: lang === \"en\" ? foundProduct.features_en || foundProduct.features : foundProduct.features\n                        };\n                        console.log(\"[产品详情] 从JSON文件获取产品数据:\", localizedProduct.name);\n                        setProduct(localizedProduct);\n                        setLoading(false);\n                        return;\n                    }\n                }\n                // 如果没有找到产品，抛出错误\n                throw new Error(lang === \"zh\" ? \"产品不存在\" : \"Product not found\");\n            } catch (error) {\n                console.error(\"[产品详情] 加载失败:\", error);\n                setError(error instanceof Error ? error.message : lang === \"zh\" ? \"加载失败\" : \"Loading failed\");\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, [\n        lang,\n        slug\n    ]);\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    style: {\n                        marginTop: \"0\",\n                        display: \"block\",\n                        lineHeight: 0\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-200 w-full\",\n                        style: {\n                            height: \"300px\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"product-detail-container product-detail-fix\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSkeleton, {}, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // 渲染错误状态\n    if (error || !product) {\n        var _dict_common10, _dict_common11;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    style: {\n                        marginTop: \"0\",\n                        display: \"block\",\n                        lineHeight: 0\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 w-full\",\n                        style: {\n                            height: \"100px\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"product-detail-container product-detail-fix\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 py-12 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 text-red-500\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-16 w-16 mx-auto\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold mb-4 text-gray-800\",\n                                children: ((_dict_common10 = dict.common) === null || _dict_common10 === void 0 ? void 0 : _dict_common10.product_not_found) || (lang === \"zh\" ? \"产品不存在\" : \"Product Not Found\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-8\",\n                                children: error || (lang === \"zh\" ? \"无法找到请求的产品\" : \"The requested product could not be found\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\".concat(lang, \"/products\"),\n                                className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: ((_dict_common11 = dict.common) === null || _dict_common11 === void 0 ? void 0 : _dict_common11.products) || (lang === \"zh\" ? \"浏览产品\" : \"Browse Products\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // 获取产品图片 - 使用实际图片\n    const getProductImages = (product)=>{\n        if (product.images && product.images.length > 0) {\n            return product.images;\n        }\n        // 如果没有图片，返回默认占位符\n        return [\n            \"/images/products/placeholder.jpg\"\n        ];\n    };\n    // 获取产品规格信息\n    const getProductSpecifications = (product)=>{\n        var _product_category, _product_name, _product_category1, _product_name1, _product_category2, _product_name2;\n        // 根据产品类型返回不同的规格\n        if (product.slug === \"ktv\" || ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.includes(\"KTV\")) || ((_product_name = product.name) === null || _product_name === void 0 ? void 0 : _product_name.includes(\"KTV\"))) {\n            return [\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    title: \"投影技术\",\n                    desc: \"4K全息投影系统\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    title: \"容纳人数\",\n                    desc: \"最多20人同时体验\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    title: \"音响配置\",\n                    desc: \"7.1环绕立体声\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    title: \"处理器\",\n                    desc: \"实时渲染引擎\"\n                }\n            ];\n        } else if (((_product_category1 = product.category) === null || _product_category1 === void 0 ? void 0 : _product_category1.includes(\"蹦床\")) || ((_product_name1 = product.name) === null || _product_name1 === void 0 ? void 0 : _product_name1.includes(\"蹦床\"))) {\n            return [\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    title: \"AR技术\",\n                    desc: \"增强现实互动系统\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    title: \"适用年龄\",\n                    desc: \"3-15岁儿童\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    title: \"安全等级\",\n                    desc: \"欧盟CE认证\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    title: \"传感器\",\n                    desc: \"高精度动作捕捉\"\n                }\n            ];\n        } else if (((_product_category2 = product.category) === null || _product_category2 === void 0 ? void 0 : _product_category2.includes(\"沙盘\")) || ((_product_name2 = product.name) === null || _product_name2 === void 0 ? void 0 : _product_name2.includes(\"沙盘\"))) {\n            return [\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    title: \"投影技术\",\n                    desc: \"3D立体投影\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    title: \"互动方式\",\n                    desc: \"手势识别控制\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    title: \"教育内容\",\n                    desc: \"多学科课程包\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    title: \"系统配置\",\n                    desc: \"智能学习算法\"\n                }\n            ];\n        } else {\n            return [\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    title: \"显示技术\",\n                    desc: \"高清数字显示\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    title: \"用户体验\",\n                    desc: \"多人互动支持\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    title: \"音效系统\",\n                    desc: \"立体声音响\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    title: \"控制系统\",\n                    desc: \"智能化管理\"\n                }\n            ];\n        }\n    };\n    const specifications = getProductSpecifications(product);\n    const mappedImages = getProductImages(product);\n    const thumbnails = mappedImages;\n    // 正常渲染产品详情 - 现代化设计\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative product-detail-page\",\n        style: {\n            marginTop: 0,\n            paddingTop: 0\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full absolute top-0 left-0 right-0\",\n                style: {\n                    height: \"450px\",\n                    marginTop: \"0\",\n                    zIndex: 0\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/images/products/product-banner.png\",\n                        alt: \"Product Banner\",\n                        className: \"w-full h-full object-cover\",\n                        loading: \"eager\",\n                        fetchPriority: \"high\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        style: {\n                            zIndex: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"product-detail-navigation-overlay\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"breadcrumbs-overlay animate-slide-in-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\".concat(lang),\n                                                children: ((_dict_common = dict.common) === null || _dict_common === void 0 ? void 0 : _dict_common.home) || (lang === \"zh\" ? \"首页\" : \"Home\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"separator\",\n                                                children: \"/\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\".concat(lang, \"/products\"),\n                                                children: ((_dict_common1 = dict.common) === null || _dict_common1 === void 0 ? void 0 : _dict_common1.products) || (lang === \"zh\" ? \"产品\" : \"Products\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"separator\",\n                                                children: \"/\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"current\",\n                                                children: product.title || product.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"product-detail-container bg-white relative\",\n                style: {\n                    marginTop: \"450px\",\n                    zIndex: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-8 py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-2 gap-8 items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative aspect-[16/9] bg-gray-100 rounded-lg overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: thumbnails[selectedImageIndex] || ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : _product_images[0]) || \"/images/products/placeholder.jpg\",\n                                                        alt: product.title || product.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    product.video_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-900 shadow-lg px-6 py-3 rounded-lg transition-all group\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-5 h-5 group-hover:scale-110 transition-transform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ((_dict_common2 = dict.common) === null || _dict_common2 === void 0 ? void 0 : _dict_common2.watch_demo) || (lang === \"zh\" ? \"观看演示\" : \"Watch Demo\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this),\n                                            thumbnails && thumbnails.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2 overflow-x-auto pb-2 -mx-2 px-2\",\n                                                children: thumbnails.map((thumb, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedImageIndex(index),\n                                                        className: \"flex-shrink-0 w-24 h-20 rounded border-2 overflow-hidden transition-colors \".concat(selectedImageIndex === index ? \"border-gray-900\" : \"border-gray-200 hover:border-gray-400\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: thumb || \"/images/products/placeholder.jpg\",\n                                                            alt: \"视图 \".concat(index + 1),\n                                                            className: \"object-cover w-full h-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-gray-900 text-white px-3 py-1 rounded text-sm\",\n                                                            children: ((_dict_common3 = dict.common) === null || _dict_common3 === void 0 ? void 0 : _dict_common3.professional) || (lang === \"zh\" ? \"专业级\" : \"Professional\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                [\n                                                                    ...Array(5)\n                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4 fill-gray-900 text-gray-900\"\n                                                                    }, i, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 25\n                                                                    }, this)),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 ml-2\",\n                                                                    children: [\n                                                                        \"4.9 (128 \",\n                                                                        ((_dict_common4 = dict.common) === null || _dict_common4 === void 0 ? void 0 : _dict_common4.reviews) || (lang === \"zh\" ? \"评价\" : \"reviews\"),\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-4xl lg:text-5xl font-light text-gray-900 mb-4 leading-tight\",\n                                                            children: product.title || product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl text-gray-600 leading-relaxed\",\n                                                            children: product.description || (lang === \"zh\" ? \"专业级互动设备，采用先进技术为用户提供沉浸式体验解决方案。\" : \"Professional interactive equipment using advanced technology to provide immersive experience solutions.\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6\",\n                                                    children: specifications.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(spec.icon, {\n                                                                            className: \"w-5 h-5 text-gray-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: spec.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 text-sm\",\n                                                                    children: spec.desc\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/\".concat(lang, \"/pages/contact-us\"),\n                                                                className: \"flex-1 bg-gray-900 hover:bg-gray-800 text-white h-12 px-6 rounded-lg flex items-center justify-center transition-colors\",\n                                                                children: ((_dict_common5 = dict.common) === null || _dict_common5 === void 0 ? void 0 : _dict_common5.get_quote) || (lang === \"zh\" ? \"获取报价\" : \"Get Quote\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        ((_dict_common6 = dict.common) === null || _dict_common6 === void 0 ? void 0 : _dict_common6.in_stock) || (lang === \"zh\" ? \"现货供应\" : \"In Stock\")\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: ((_dict_common7 = dict.common) === null || _dict_common7 === void 0 ? void 0 : _dict_common7.professional_installation) || (lang === \"zh\" ? \"专业安装\" : \"Professional Installation\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: ((_dict_common8 = dict.common) === null || _dict_common8 === void 0 ? void 0 : _dict_common8.three_year_warranty) || (lang === \"zh\" ? \"质保3年\" : \"3-Year Warranty\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl lg:text-5xl font-light text-gray-900 mb-6\",\n                                        children: ((_dict_common9 = dict.common) === null || _dict_common9 === void 0 ? void 0 : _dict_common9.product_gallery) || (lang === \"zh\" ? \"产品展示\" : \"Product Gallery\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed\",\n                                        children: lang === \"zh\" ? \"专业级\".concat(product.category || \"互动设备\", \"在不同应用场景中的实际效果展示\") : \"Professional \".concat(product.category || \"interactive equipment\", \" showcased in various application scenarios\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-0\",\n                                children: mappedImages.filter(Boolean).map((image, index)=>{\n                                    var _dict_common, _dict_common1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full h-screen bg-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: image || \"/images/products/placeholder.jpg\",\n                                                alt: \"\".concat(product.title || product.name, \" - \").concat(((_dict_common = dict.common) === null || _dict_common === void 0 ? void 0 : _dict_common.application_scenario) || (lang === \"zh\" ? \"应用场景\" : \"Application scenario\"), \" \").concat(index + 1),\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"container mx-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white text-lg font-medium\",\n                                                        children: [\n                                                            product.title || product.name,\n                                                            \" - \",\n                                                            ((_dict_common1 = dict.common) === null || _dict_common1 === void 0 ? void 0 : _dict_common1.application_scenario) || (lang === \"zh\" ? \"应用场景\" : \"Application Scenario\"),\n                                                            \" \",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n        lineNumber: 266,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductPage, \"Uzsa2ydllt3CX7Io1AHebWWioLk=\");\n_c1 = ProductPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ProductSkeleton\");\n$RefreshReg$(_c1, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[lang]/products/[slug]/page.tsx\n"));

/***/ })

});