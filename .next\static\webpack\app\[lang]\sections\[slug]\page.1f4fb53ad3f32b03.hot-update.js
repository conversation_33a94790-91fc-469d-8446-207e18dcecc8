"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lang]/sections/[slug]/page",{

/***/ "(app-pages-browser)/./app/[lang]/sections/[slug]/page.tsx":
/*!*********************************************!*\
  !*** ./app/[lang]/sections/[slug]/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SectionPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_LanguageProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/LanguageProvider */ \"(app-pages-browser)/./app/components/LanguageProvider.tsx\");\n/* harmony import */ var _components_PageHeader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/PageHeader */ \"(app-pages-browser)/./app/components/PageHeader.tsx\");\n/* harmony import */ var _components_SeriesGallery__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../components/SeriesGallery */ \"(app-pages-browser)/./app/components/SeriesGallery.tsx\");\n/* harmony import */ var _utils_i18n__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../utils/i18n */ \"(app-pages-browser)/./app/utils/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// 获取本地化的板块数据\nconst getSectionData = (slug, locale, dict)=>{\n    var _dict_productSections_section1, _dict_productSections, _dict_productSections_section11, _dict_productSections1, _dict_productSections_section2, _dict_productSections2, _dict_productSections_section21, _dict_productSections3, _dict_productSections_section3, _dict_productSections4, _dict_productSections_section31, _dict_productSections5, _dict_productSections_section4, _dict_productSections6, _dict_productSections_section41, _dict_productSections7, _dict_productSections_section5, _dict_productSections8, _dict_productSections_section51, _dict_productSections9;\n    const sectionsConfig = {\n        \"interactive-projection\": {\n            slug: \"interactive-projection\",\n            title: ((_dict_productSections = dict.productSections) === null || _dict_productSections === void 0 ? void 0 : (_dict_productSections_section1 = _dict_productSections.section1) === null || _dict_productSections_section1 === void 0 ? void 0 : _dict_productSections_section1.title) || (locale === \"zh\" ? \"互动投影系列\" : \"Interactive Projection Series\"),\n            description: ((_dict_productSections1 = dict.productSections) === null || _dict_productSections1 === void 0 ? void 0 : (_dict_productSections_section11 = _dict_productSections1.section1) === null || _dict_productSections_section11 === void 0 ? void 0 : _dict_productSections_section11.description) || (locale === \"zh\" ? \"创新的互动投影技术，打造沉浸式体验空间，让用户与数字内容进行自然交互\" : \"Innovative interactive projection technology creating immersive experience spaces for natural interaction with digital content\"),\n            category: \"interactive\",\n            images: [\n                \"/images/product-sections/0007.jpg\",\n                \"/images/products/interactive-ball/interactive-ball-1.jpg\",\n                \"/images/products/interactive-ball/interactive-ball-2.jpg\",\n                \"/images/products/interactive-ball/interactive-ball-3.jpg\",\n                \"/images/products/interactive-ball/interactive-ball-5.jpg\",\n                \"/images/products/interactive-ball/interactive-ball-6.jpg\",\n                \"/images/products/interactive-football-1.jpg\"\n            ],\n            products: [\n                {\n                    id: \"1\",\n                    name: locale === \"zh\" ? \"互动砸球系统\" : \"Interactive Ball System\",\n                    image: \"/images/products/interactive-ball/interactive-ball-1.jpg\",\n                    slug: \"interactive-ball-system\",\n                    description: locale === \"zh\" ? \"创新的互动砸球游戏系统，结合投影技术和体感识别\" : \"Innovative interactive ball game system combining projection technology and motion sensing\"\n                },\n                {\n                    id: \"2\",\n                    name: locale === \"zh\" ? \"互动足球游戏\" : \"Interactive Football Game\",\n                    image: \"/images/products/interactive-football-1.jpg\",\n                    slug: \"interactive-football\",\n                    description: locale === \"zh\" ? \"沉浸式足球互动体验，让运动更加有趣\" : \"Immersive football interactive experience making sports more engaging\"\n                },\n                {\n                    id: \"3\",\n                    name: locale === \"zh\" ? \"蹦床互动系统\" : \"Trampoline Interactive System\",\n                    image: \"/images/products/trampoline-1.jpg\",\n                    slug: \"trampoline-interactive\",\n                    description: locale === \"zh\" ? \"结合蹦床运动的互动投影娱乐系统\" : \"Interactive projection entertainment system combined with trampoline sports\"\n                }\n            ]\n        },\n        \"holographic-display\": {\n            slug: \"holographic-display\",\n            title: ((_dict_productSections2 = dict.productSections) === null || _dict_productSections2 === void 0 ? void 0 : (_dict_productSections_section2 = _dict_productSections2.section2) === null || _dict_productSections_section2 === void 0 ? void 0 : _dict_productSections_section2.title) || (locale === \"zh\" ? \"全息展示系列\" : \"Holographic Display Series\"),\n            description: ((_dict_productSections3 = dict.productSections) === null || _dict_productSections3 === void 0 ? void 0 : (_dict_productSections_section21 = _dict_productSections3.section2) === null || _dict_productSections_section21 === void 0 ? void 0 : _dict_productSections_section21.description) || (locale === \"zh\" ? \"先进的全息投影技术，呈现震撼视觉效果，创造前所未有的视觉体验\" : \"Advanced holographic projection technology presenting stunning visual effects and creating unprecedented visual experiences\"),\n            category: \"holographic\",\n            images: [\n                \"/images/product-sections/0012.jpg\",\n                \"/images/products/hologram-dining-1.jpg\",\n                \"/images/products/hologram-dining-2.jpg\",\n                \"/images/products/hologram-stage-1.jpg\",\n                \"/images/products/hologram-stage-2.jpg\",\n                \"/images/products/产品介绍模板_01.jpg\",\n                \"/images/products/产品介绍模板_02.jpg\"\n            ],\n            products: [\n                {\n                    id: \"2\",\n                    name: locale === \"zh\" ? \"全息沙盘\" : \"Holographic Sand Table\",\n                    image: \"/images/products/hologram-dining-1.jpg\",\n                    slug: \"holographic-sand-table\",\n                    description: locale === \"zh\" ? \"全息投影沙盘展示系统，实现立体化信息展示\" : \"Holographic projection sand table display system for three-dimensional information presentation\"\n                },\n                {\n                    id: \"3\",\n                    name: locale === \"zh\" ? \"全息舞台\" : \"Holographic Stage\",\n                    image: \"/images/products/hologram-stage-1.jpg\",\n                    slug: \"holographic-stage\",\n                    description: locale === \"zh\" ? \"震撼的全息舞台表演系统，打造视觉盛宴\" : \"Stunning holographic stage performance system creating visual spectacles\"\n                },\n                {\n                    id: \"4\",\n                    name: locale === \"zh\" ? \"全息餐厅\" : \"Holographic Dining\",\n                    image: \"/images/products/hologram-dining-2.jpg\",\n                    slug: \"holographic-dining\",\n                    description: locale === \"zh\" ? \"沉浸式全息餐厅体验，重新定义用餐环境\" : \"Immersive holographic dining experience redefining the dining environment\"\n                }\n            ]\n        },\n        \"digital-sandbox\": {\n            slug: \"digital-sandbox\",\n            title: ((_dict_productSections4 = dict.productSections) === null || _dict_productSections4 === void 0 ? void 0 : (_dict_productSections_section3 = _dict_productSections4.section3) === null || _dict_productSections_section3 === void 0 ? void 0 : _dict_productSections_section3.title) || (locale === \"zh\" ? \"数字沙盘系列\" : \"Digital Sandbox Series\"),\n            description: ((_dict_productSections5 = dict.productSections) === null || _dict_productSections5 === void 0 ? void 0 : (_dict_productSections_section31 = _dict_productSections5.section3) === null || _dict_productSections_section31 === void 0 ? void 0 : _dict_productSections_section31.description) || (locale === \"zh\" ? \"智能数字沙盘，实现精准展示与互动控制，为规划展示提供创新解决方案\" : \"Smart digital sandbox enabling precise display and interactive control, providing innovative solutions for planning exhibitions\"),\n            category: \"digital\",\n            images: [\n                \"/images/product-sections/0021.jpg\",\n                \"/images/products/3d-1.jpg\",\n                \"/images/products/3d-2.jpg\",\n                \"/images/products/3d-3.jpg\",\n                \"/images/products/3d-4.jpg\",\n                \"/images/products/3d-5.jpg\",\n                \"/images/products/3d-6.jpg\",\n                \"/images/products/3d-sandbox-1.jpg\"\n            ],\n            products: [\n                {\n                    id: \"5\",\n                    name: locale === \"zh\" ? \"智能规划沙盘\" : \"Smart Planning Sandbox\",\n                    image: \"/images/products/3d-2.jpg\",\n                    slug: \"3d-planning\",\n                    description: locale === \"zh\" ? \"智能化城市规划展示沙盘，支持实时数据更新\" : \"Intelligent urban planning display sandbox supporting real-time data updates\"\n                },\n                {\n                    id: \"6\",\n                    name: locale === \"zh\" ? \"互动数字沙盘\" : \"Interactive Digital Sandbox\",\n                    image: \"/images/products/3d-sandbox-1.jpg\",\n                    slug: \"interactive-sandbox\",\n                    description: locale === \"zh\" ? \"支持多点触控的互动数字沙盘系统\" : \"Interactive digital sandbox system supporting multi-touch control\"\n                }\n            ]\n        },\n        \"ar-reality\": {\n            slug: \"ar-reality\",\n            title: ((_dict_productSections6 = dict.productSections) === null || _dict_productSections6 === void 0 ? void 0 : (_dict_productSections_section4 = _dict_productSections6.section4) === null || _dict_productSections_section4 === void 0 ? void 0 : _dict_productSections_section4.title) || (locale === \"zh\" ? \"AR增强现实系列\" : \"AR Augmented Reality Series\"),\n            description: ((_dict_productSections7 = dict.productSections) === null || _dict_productSections7 === void 0 ? void 0 : (_dict_productSections_section41 = _dict_productSections7.section4) === null || _dict_productSections_section41 === void 0 ? void 0 : _dict_productSections_section41.description) || (locale === \"zh\" ? \"AR增强现实技术，融合虚拟与现实世界，创造全新的交互体验\" : \"AR augmented reality technology merging virtual and real worlds, creating innovative interactive experiences\"),\n            category: \"ar\",\n            images: [\n                \"/images/product-sections/0035.jpg\",\n                \"/images/products/ar-1.jpg\",\n                \"/images/products/ar-2.jpg\",\n                \"/images/products/ar-3.jpg\",\n                \"/images/products/ar-4.jpg\",\n                \"/images/products/ar-education-1.jpg\",\n                \"/images/products/ar-education-2.jpg\",\n                \"/images/products/ar-education-3.jpg\"\n            ],\n            products: [\n                {\n                    id: \"4\",\n                    name: locale === \"zh\" ? \"AR教育系统\" : \"AR Education System\",\n                    image: \"/images/products/ar-1.jpg\",\n                    slug: \"ar\",\n                    description: locale === \"zh\" ? \"AR增强现实教育系统，提供沉浸式学习体验\" : \"AR augmented reality education system providing immersive learning experiences\"\n                },\n                {\n                    id: \"7\",\n                    name: locale === \"zh\" ? \"AR展示系统\" : \"AR Display System\",\n                    image: \"/images/products/ar-education-1.jpg\",\n                    slug: \"ar-display\",\n                    description: locale === \"zh\" ? \"专业AR展示解决方案，适用于展览展示\" : \"Professional AR display solutions suitable for exhibitions and presentations\"\n                },\n                {\n                    id: \"8\",\n                    name: locale === \"zh\" ? \"AR互动体验\" : \"AR Interactive Experience\",\n                    image: \"/images/products/ar-education-2.jpg\",\n                    slug: \"ar-interactive\",\n                    description: locale === \"zh\" ? \"创新AR互动体验系统，打造未来科技感\" : \"Innovative AR interactive experience system creating futuristic technology ambiance\"\n                }\n            ]\n        },\n        \"smart-integrated\": {\n            slug: \"smart-integrated\",\n            title: ((_dict_productSections8 = dict.productSections) === null || _dict_productSections8 === void 0 ? void 0 : (_dict_productSections_section5 = _dict_productSections8.section5) === null || _dict_productSections_section5 === void 0 ? void 0 : _dict_productSections_section5.title) || (locale === \"zh\" ? \"智能一体机系列\" : \"Smart Integrated Series\"),\n            description: ((_dict_productSections9 = dict.productSections) === null || _dict_productSections9 === void 0 ? void 0 : (_dict_productSections_section51 = _dict_productSections9.section5) === null || _dict_productSections_section51 === void 0 ? void 0 : _dict_productSections_section51.description) || (locale === \"zh\" ? \"集成化智能设备，提供完整解决方案，满足多样化应用需求\" : \"Integrated smart devices providing complete solutions to meet diverse application needs\"),\n            category: \"smart\",\n            images: [\n                \"/images/product-sections/0046.jpg\",\n                \"/images/products/ktv-1.jpg\",\n                \"/images/products/ktv-2.jpg\",\n                \"/images/products/ktv-3.jpg\",\n                \"/images/products/ktv-4.jpg\",\n                \"/images/products/ktv-5.jpg\",\n                \"/images/products/ktv-6.jpg\",\n                \"/images/products/-1.jpg\"\n            ],\n            products: [\n                {\n                    id: \"5\",\n                    name: locale === \"zh\" ? \"KTV智能系统\" : \"KTV Smart System\",\n                    image: \"/images/products/ktv-1.jpg\",\n                    slug: \"ktv\",\n                    description: locale === \"zh\" ? \"KTV智能娱乐系统，提供全方位娱乐体验\" : \"KTV smart entertainment system providing comprehensive entertainment experiences\"\n                },\n                {\n                    id: \"9\",\n                    name: locale === \"zh\" ? \"智能会议系统\" : \"Smart Meeting System\",\n                    image: \"/images/products/ktv-2.jpg\",\n                    slug: \"smart-meeting\",\n                    description: locale === \"zh\" ? \"高效智能会议解决方案，提升办公效率\" : \"Efficient smart meeting solutions enhancing office productivity\"\n                },\n                {\n                    id: \"10\",\n                    name: locale === \"zh\" ? \"多媒体一体机\" : \"Multimedia All-in-One\",\n                    image: \"/images/products/-1.jpg\",\n                    slug: \"multimedia-system\",\n                    description: locale === \"zh\" ? \"集成多媒体功能的智能一体机设备\" : \"Smart all-in-one device integrating multimedia functions\"\n                }\n            ]\n        }\n    };\n    return sectionsConfig[slug] || null;\n};\nfunction SectionPage() {\n    var _dict_common, _dict_common1, _dict_common2, _dict_common3, _dict_common4, _dict_common5, _dict_common6;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { locale } = (0,_components_LanguageProvider__WEBPACK_IMPORTED_MODULE_6__.useLanguage)();\n    const [sectionData, setSectionData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [dict, setDict] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    // 加载字典\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const loadDictionary = async ()=>{\n            try {\n                const dictionary = await (0,_utils_i18n__WEBPACK_IMPORTED_MODULE_9__.getDictionary)(locale);\n                setDict(dictionary);\n            } catch (error) {\n                console.error(\"Failed to load dictionary:\", error);\n                // 设置默认字典\n                setDict({\n                    common: {\n                        home: locale === \"zh\" ? \"首页\" : \"Home\",\n                        products: locale === \"zh\" ? \"产品中心\" : \"Products\",\n                        related_products: locale === \"zh\" ? \"相关产品\" : \"Related Products\",\n                        product_details: locale === \"zh\" ? \"产品详情\" : \"Product Details\",\n                        view_more_products: locale === \"zh\" ? \"查看更多产品\" : \"View More Products\",\n                        explore_complete_series: locale === \"zh\" ? \"探索我们的完整产品系列\" : \"Explore our complete product series\",\n                        browse_all: locale === \"zh\" ? \"浏览全部\" : \"Browse All\",\n                        back_to_products: locale === \"zh\" ? \"返回产品中心\" : \"Back to Products\"\n                    }\n                });\n            }\n        };\n        loadDictionary();\n    }, [\n        locale\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!params) {\n            router.push(\"/\".concat(locale, \"/products\"));\n            return;\n        }\n        const slug = params.slug;\n        const data = getSectionData(slug, locale, dict);\n        if (data) {\n            setSectionData(data);\n        } else {\n            // 如果找不到对应的板块，重定向到产品页面\n            router.push(\"/\".concat(locale, \"/products\"));\n        }\n    }, [\n        params === null || params === void 0 ? void 0 : params.slug,\n        router,\n        locale,\n        dict\n    ]);\n    if (!sectionData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: locale === \"zh\" ? \"加载中...\" : \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                lineNumber: 277,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"section-page\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PageHeader__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        title: \"\",\n                        subtitle: \"\",\n                        bgImage: sectionData.images[0],\n                        height: \"md:h-[400px] h-[300px]\",\n                        overlayOpacity: 0.7,\n                        useImageComponent: true,\n                        animationEffect: \"up\",\n                        className: \"mb-16\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"absolute inset-0 flex items-center justify-center z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"container mx-auto px-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 animate-fade-in-up\",\n                                            children: sectionData.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        sectionData.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-lg md:text-xl text-gray-200 max-w-3xl mx-auto mb-6 animate-fade-in-up-delay\",\n                                            children: sectionData.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"breadcrumbs-overlay flex items-center justify-center space-x-2 text-sm animate-slide-in-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/\".concat(locale),\n                                            className: \"text-white hover:text-white transition-all duration-200 px-3 py-2 rounded-lg bg-black/30 backdrop-blur-sm border border-white/20 hover:bg-black/50 hover:border-white/30 hover:-translate-y-0.5 font-medium text-shadow-lg\",\n                                            children: ((_dict_common = dict.common) === null || _dict_common === void 0 ? void 0 : _dict_common.home) || (locale === \"zh\" ? \"首页\" : \"Home\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-white/80 mx-2 text-base font-bold text-shadow-lg\",\n                                            children: \"/\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/\".concat(locale, \"/products\"),\n                                            className: \"text-white hover:text-white transition-all duration-200 px-3 py-2 rounded-lg bg-black/30 backdrop-blur-sm border border-white/20 hover:bg-black/50 hover:border-white/30 hover:-translate-y-0.5 font-medium text-shadow-lg\",\n                                            children: ((_dict_common1 = dict.common) === null || _dict_common1 === void 0 ? void 0 : _dict_common1.products) || (locale === \"zh\" ? \"产品中心\" : \"Products\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-white/80 mx-2 text-base font-bold text-shadow-lg\",\n                                            children: \"/\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-white font-semibold px-3 py-2 rounded-lg bg-white/15 backdrop-blur-sm border border-white/30 text-shadow-lg\",\n                                            children: sectionData.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"container mx-auto px-4 mb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SeriesGallery__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sectionSlug: sectionData.slug\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this),\n            sectionData.products.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"container mx-auto px-4 mb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"related-products\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-2xl font-bold text-gray-900 mb-8 text-center\",\n                            children: ((_dict_common2 = dict.common) === null || _dict_common2 === void 0 ? void 0 : _dict_common2.related_products) || (locale === \"zh\" ? \"相关产品\" : \"Related Products\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"products-horizontal-scroll\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"flex gap-6 overflow-x-auto pb-4 scrollbar-hide\",\n                                children: [\n                                    sectionData.products.map((product)=>{\n                                        var _dict_common;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/\".concat(locale, \"/products\"),\n                                            className: \"product-mini-card group flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 group-hover:shadow-lg group-hover:-translate-y-1 w-72\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"relative w-full h-40\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                                src: product.image,\n                                                                alt: product.name,\n                                                                fill: true,\n                                                                style: {\n                                                                    objectFit: \"cover\"\n                                                                },\n                                                                className: \"transition-transform duration-300 group-hover:scale-105\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"absolute top-3 left-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium\",\n                                                                    children: sectionData.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-base font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors line-clamp-1\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-gray-600 text-sm line-clamp-2 mb-3\",\n                                                                children: product.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                                        children: ((_dict_common = dict.common) === null || _dict_common === void 0 ? void 0 : _dict_common.product_details) || (locale === \"zh\" ? \"产品详情\" : \"Product Details\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"fas fa-arrow-right text-blue-500 text-sm transition-transform group-hover:translate-x-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 386,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, product.id, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 19\n                                        }, this);\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/\".concat(locale, \"/products\"),\n                                        className: \"view-more-card group flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border-2 border-dashed border-blue-200 overflow-hidden transition-all duration-300 group-hover:border-blue-400 group-hover:shadow-lg w-72 h-full flex items-center justify-center min-h-[240px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-center p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-200 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"fas fa-plus text-blue-600 text-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-base font-semibold text-gray-700 mb-2\",\n                                                        children: ((_dict_common3 = dict.common) === null || _dict_common3 === void 0 ? void 0 : _dict_common3.view_more_products) || (locale === \"zh\" ? \"查看更多产品\" : \"View More Products\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-sm text-gray-500 mb-3\",\n                                                        children: ((_dict_common4 = dict.common) === null || _dict_common4 === void 0 ? void 0 : _dict_common4.explore_complete_series) || (locale === \"zh\" ? \"探索我们的完整产品系列\" : \"Explore our complete product series\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"flex items-center justify-center text-blue-600 text-sm font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-3d32dd8e0cf21aba\",\n                                                                children: ((_dict_common5 = dict.common) === null || _dict_common5 === void 0 ? void 0 : _dict_common5.browse_all) || (locale === \"zh\" ? \"浏览全部\" : \"Browse All\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"fas fa-arrow-right ml-2 transition-transform group-hover:translate-x-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                lineNumber: 346,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"container mx-auto px-4 mb-16 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                    href: \"/\".concat(locale, \"/products\"),\n                    className: \"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"fas fa-arrow-left mr-2\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 11\n                        }, this),\n                        ((_dict_common6 = dict.common) === null || _dict_common6 === void 0 ? void 0 : _dict_common6.back_to_products) || (locale === \"zh\" ? \"返回产品中心\" : \"Back to Products\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"3d32dd8e0cf21aba\",\n                children: '.section-gallery.jsx-3d32dd8e0cf21aba{max-width:1e3px;margin:0 auto}.main-image-container.jsx-3d32dd8e0cf21aba{max-width:800px;margin:0 auto}.thumbnails-grid.jsx-3d32dd8e0cf21aba{max-width:600px;margin:0 auto}.line-clamp-1.jsx-3d32dd8e0cf21aba{display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;overflow:hidden}.line-clamp-2.jsx-3d32dd8e0cf21aba{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.scrollbar-hide.jsx-3d32dd8e0cf21aba{-ms-overflow-style:none;scrollbar-width:none}.scrollbar-hide.jsx-3d32dd8e0cf21aba::-webkit-scrollbar{display:none}.products-horizontal-scroll.jsx-3d32dd8e0cf21aba{position:relative}.products-horizontal-scroll.jsx-3d32dd8e0cf21aba::before,.products-horizontal-scroll.jsx-3d32dd8e0cf21aba::after{content:\"\";position:absolute;top:0;bottom:0;width:20px;z-index:10;pointer-events:none}.products-horizontal-scroll.jsx-3d32dd8e0cf21aba::before{left:0;background:-webkit-linear-gradient(left,rgba(255,255,255,1),rgba(255,255,255,0));background:-moz-linear-gradient(left,rgba(255,255,255,1),rgba(255,255,255,0));background:-o-linear-gradient(left,rgba(255,255,255,1),rgba(255,255,255,0));background:linear-gradient(to right,rgba(255,255,255,1),rgba(255,255,255,0))}.products-horizontal-scroll.jsx-3d32dd8e0cf21aba::after{right:0;background:-webkit-linear-gradient(right,rgba(255,255,255,1),rgba(255,255,255,0));background:-moz-linear-gradient(right,rgba(255,255,255,1),rgba(255,255,255,0));background:-o-linear-gradient(right,rgba(255,255,255,1),rgba(255,255,255,0));background:linear-gradient(to left,rgba(255,255,255,1),rgba(255,255,255,0))}.product-mini-card.jsx-3d32dd8e0cf21aba:hover .bg-blue-100.jsx-3d32dd8e0cf21aba{background-color:rgb(219 234 254)}.animate-slide-in-left.jsx-3d32dd8e0cf21aba{-webkit-animation:slideInLeft 1s ease-out.5s both;-moz-animation:slideInLeft 1s ease-out.5s both;-o-animation:slideInLeft 1s ease-out.5s both;animation:slideInLeft 1s ease-out.5s both}.animate-fade-in-up.jsx-3d32dd8e0cf21aba{-webkit-animation:fadeInUp.8s ease-out;-moz-animation:fadeInUp.8s ease-out;-o-animation:fadeInUp.8s ease-out;animation:fadeInUp.8s ease-out}.animate-fade-in-up-delay.jsx-3d32dd8e0cf21aba{-webkit-animation:fadeInUp.8s ease-out.2s both;-moz-animation:fadeInUp.8s ease-out.2s both;-o-animation:fadeInUp.8s ease-out.2s both;animation:fadeInUp.8s ease-out.2s both}@-webkit-keyframes slideInLeft{from{opacity:0;-webkit-transform:translatex(-30px);transform:translatex(-30px)}to{opacity:1;-webkit-transform:translatex(0);transform:translatex(0)}}@-moz-keyframes slideInLeft{from{opacity:0;-moz-transform:translatex(-30px);transform:translatex(-30px)}to{opacity:1;-moz-transform:translatex(0);transform:translatex(0)}}@-o-keyframes slideInLeft{from{opacity:0;-o-transform:translatex(-30px);transform:translatex(-30px)}to{opacity:1;-o-transform:translatex(0);transform:translatex(0)}}@keyframes slideInLeft{from{opacity:0;-webkit-transform:translatex(-30px);-moz-transform:translatex(-30px);-o-transform:translatex(-30px);transform:translatex(-30px)}to{opacity:1;-webkit-transform:translatex(0);-moz-transform:translatex(0);-o-transform:translatex(0);transform:translatex(0)}}@-webkit-keyframes fadeInUp{from{opacity:0;-webkit-transform:translatey(20px);transform:translatey(20px)}to{opacity:1;-webkit-transform:translatey(0);transform:translatey(0)}}@-moz-keyframes fadeInUp{from{opacity:0;-moz-transform:translatey(20px);transform:translatey(20px)}to{opacity:1;-moz-transform:translatey(0);transform:translatey(0)}}@-o-keyframes fadeInUp{from{opacity:0;-o-transform:translatey(20px);transform:translatey(20px)}to{opacity:1;-o-transform:translatey(0);transform:translatey(0)}}@keyframes fadeInUp{from{opacity:0;-webkit-transform:translatey(20px);-moz-transform:translatey(20px);-o-transform:translatey(20px);transform:translatey(20px)}to{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}}.text-shadow-lg.jsx-3d32dd8e0cf21aba{text-shadow:0 2px 4px rgba(0,0,0,.8),0 0 8px rgba(0,0,0,.5)}.breadcrumbs-overlay.jsx-3d32dd8e0cf21aba a.jsx-3d32dd8e0cf21aba{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px);-webkit-box-shadow:0 2px 8px rgba(0,0,0,.3);-moz-box-shadow:0 2px 8px rgba(0,0,0,.3);box-shadow:0 2px 8px rgba(0,0,0,.3)}.breadcrumbs-overlay.jsx-3d32dd8e0cf21aba a.jsx-3d32dd8e0cf21aba:hover{-webkit-box-shadow:0 4px 12px rgba(0,0,0,.4);-moz-box-shadow:0 4px 12px rgba(0,0,0,.4);box-shadow:0 4px 12px rgba(0,0,0,.4);-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px)}.breadcrumbs-overlay.jsx-3d32dd8e0cf21aba span.jsx-3d32dd8e0cf21aba:last-child{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px);-webkit-box-shadow:0 2px 8px rgba(0,0,0,.2);-moz-box-shadow:0 2px 8px rgba(0,0,0,.2);box-shadow:0 2px 8px rgba(0,0,0,.2)}@media(max-width:768px){.products-horizontal-scroll.jsx-3d32dd8e0cf21aba .flex.jsx-3d32dd8e0cf21aba{padding-left:1rem;padding-right:1rem}.product-mini-card.jsx-3d32dd8e0cf21aba .w-72.jsx-3d32dd8e0cf21aba{width:16rem}.breadcrumbs-overlay.jsx-3d32dd8e0cf21aba{font-size:.75rem}.breadcrumbs-overlay.jsx-3d32dd8e0cf21aba a.jsx-3d32dd8e0cf21aba,.breadcrumbs-overlay.jsx-3d32dd8e0cf21aba span.jsx-3d32dd8e0cf21aba{padding:.375rem .5rem}}'\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n        lineNumber: 286,\n        columnNumber: 5\n    }, this);\n}\n_s(SectionPage, \"WenloY4mgKOnIMLW/ZrN91EYxFI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _components_LanguageProvider__WEBPACK_IMPORTED_MODULE_6__.useLanguage\n    ];\n});\n_c = SectionPage;\nvar _c;\n$RefreshReg$(_c, \"SectionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[lang]/sections/[slug]/page.tsx\n"));

/***/ })

});