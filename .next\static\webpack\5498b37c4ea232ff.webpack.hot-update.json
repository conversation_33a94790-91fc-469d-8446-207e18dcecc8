{"c": ["app/[lang]/layout", "app/[lang]/sections/[slug]/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./app/[lang]/sections/[slug]/page.tsx", "(app-pages-browser)/./app/components/SeriesGallery.tsx", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5C%5Blang%5D%5Csections%5C%5Bslug%5D%5Cpage.tsx&server=false!"]}