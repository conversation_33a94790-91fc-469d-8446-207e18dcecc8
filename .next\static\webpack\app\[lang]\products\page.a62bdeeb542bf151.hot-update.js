"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lang]/products/page",{

/***/ "(app-pages-browser)/./app/components/ProductSearch.tsx":
/*!******************************************!*\
  !*** ./app/components/ProductSearch.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductSearch; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _LanguageProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LanguageProvider */ \"(app-pages-browser)/./app/components/LanguageProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ProductSearch(param) {\n    let { onSearch, placeholder, className = \"\" } = param;\n    _s();\n    const [query, setQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showSuggestions, setShowSuggestions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const suggestionsRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const { t } = (0,_LanguageProvider__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    // 使用国际化的占位符\n    const searchPlaceholder = placeholder || t(\"products.search_placeholder\", {\n        fallback: \"Search product name, model or keywords...\"\n    });\n    const handleSearch = (value)=>{\n        setQuery(value);\n        if (onSearch) {\n            onSearch(value);\n        }\n    };\n    const clearSearch = ()=>{\n        var _inputRef_current;\n        setQuery(\"\");\n        if (onSearch) {\n            onSearch(\"\");\n        }\n        (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\") {\n            e.preventDefault();\n            if (onSearch) {\n                onSearch(query);\n            }\n        }\n    };\n    // 处理点击外部关闭建议\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            const target = event.target;\n            // 检查点击是否在建议框或输入框内\n            const isInsideSuggestions = suggestionsRef.current && suggestionsRef.current.contains(target);\n            const isInsideInput = inputRef.current && inputRef.current.contains(target);\n            // 如果点击在外部，关闭建议框\n            if (!isInsideSuggestions && !isInsideInput) {\n                setShowSuggestions(false);\n                setIsFocused(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    // 处理输入框焦点\n    const handleFocus = ()=>{\n        setIsFocused(true);\n        setShowSuggestions(true);\n    };\n    const handleBlur = ()=>{\n        // 延迟关闭，让点击事件有时间触发\n        setTimeout(()=>{\n            setIsFocused(false);\n            setShowSuggestions(false);\n        }, 200);\n    };\n    // 处理热门搜索点击\n    const handleTagClick = (tag)=>{\n        console.log(\"Popular search tag clicked:\", tag);\n        setQuery(tag);\n        setShowSuggestions(false);\n        setIsFocused(false);\n        if (onSearch) {\n            onSearch(tag);\n        }\n        // 短暂延迟后重新聚焦输入框\n        setTimeout(()=>{\n            var _inputRef_current;\n            (_inputRef_current = inputRef.current) === null || _inputRef_current === void 0 ? void 0 : _inputRef_current.focus();\n        }, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-17b552e6a5168f0e\" + \" \" + \"w-full max-w-4xl mx-auto \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"17b552e6a5168f0e\",\n                children: \".search-container.jsx-17b552e6a5168f0e{position:relative;-webkit-transition:all.3s cubic-bezier(.4,0,.2,1);-moz-transition:all.3s cubic-bezier(.4,0,.2,1);-o-transition:all.3s cubic-bezier(.4,0,.2,1);transition:all.3s cubic-bezier(.4,0,.2,1)}.search-input.jsx-17b552e6a5168f0e{background:rgba(255,255,255,.98);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);border:1px solid rgba(255,255,255,.8);-webkit-transition:all.3s cubic-bezier(.4,0,.2,1);-moz-transition:all.3s cubic-bezier(.4,0,.2,1);-o-transition:all.3s cubic-bezier(.4,0,.2,1);transition:all.3s cubic-bezier(.4,0,.2,1);-webkit-box-shadow:0 8px 32px rgba(0,0,0,.08),0 2px 16px rgba(59,130,246,.04);-moz-box-shadow:0 8px 32px rgba(0,0,0,.08),0 2px 16px rgba(59,130,246,.04);box-shadow:0 8px 32px rgba(0,0,0,.08),0 2px 16px rgba(59,130,246,.04)}.search-input.jsx-17b552e6a5168f0e:focus{background:rgba(255,255,255,1);border-color:#3b82f6;-webkit-box-shadow:0 8px 30px rgba(59,130,246,.15);-moz-box-shadow:0 8px 30px rgba(59,130,246,.15);box-shadow:0 8px 30px rgba(59,130,246,.15);-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px)}.search-input.has-value.jsx-17b552e6a5168f0e{border-color:#10b981}.clear-button.jsx-17b552e6a5168f0e{opacity:0;-webkit-transform:scale(.8);-moz-transform:scale(.8);-ms-transform:scale(.8);-o-transform:scale(.8);transform:scale(.8);-webkit-transition:all.2s ease;-moz-transition:all.2s ease;-o-transition:all.2s ease;transition:all.2s ease}.clear-button.visible.jsx-17b552e6a5168f0e{opacity:1;-webkit-transform:scale(1);-moz-transform:scale(1);-ms-transform:scale(1);-o-transform:scale(1);transform:scale(1)}.search-glow.jsx-17b552e6a5168f0e{position:absolute;top:0;left:0;right:0;bottom:0;-webkit-border-radius:16px;-moz-border-radius:16px;border-radius:16px;background:-webkit-linear-gradient(45deg,#3b82f6,#8b5cf6,#06b6d4);background:-moz-linear-gradient(45deg,#3b82f6,#8b5cf6,#06b6d4);background:-o-linear-gradient(45deg,#3b82f6,#8b5cf6,#06b6d4);background:linear-gradient(45deg,#3b82f6,#8b5cf6,#06b6d4);opacity:0;z-index:-1;-webkit-transition:opacity.3s ease;-moz-transition:opacity.3s ease;-o-transition:opacity.3s ease;transition:opacity.3s ease;-webkit-filter:blur(20px);filter:blur(20px)}.search-container.jsx-17b552e6a5168f0e:focus-within .search-glow.jsx-17b552e6a5168f0e{opacity:.3}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-17b552e6a5168f0e\" + \" \" + \"search-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-17b552e6a5168f0e\" + \" \" + \"search-glow\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductSearch.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-17b552e6a5168f0e\" + \" \" + \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: inputRef,\n                                type: \"text\",\n                                value: query,\n                                onChange: (e)=>handleSearch(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                onFocus: handleFocus,\n                                onBlur: handleBlur,\n                                placeholder: searchPlaceholder,\n                                className: \"jsx-17b552e6a5168f0e\" + \" \" + \"search-input w-full pl-6 pr-12 py-4 rounded-xl text-gray-700 placeholder-gray-400 outline-none text-lg font-medium \".concat(query ? \"has-value\" : \"\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductSearch.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this),\n                            query && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearSearch,\n                                className: \"jsx-17b552e6a5168f0e\" + \" \" + \"clear-button absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 hover:text-gray-600 \".concat(query ? \"visible\" : \"\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-full h-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductSearch.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductSearch.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductSearch.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    showSuggestions && !query && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: suggestionsRef,\n                        className: \"jsx-17b552e6a5168f0e\" + \" \" + \"absolute top-full left-0 right-0 mt-2 bg-white/98 backdrop-blur-sm rounded-xl shadow-2xl border border-white/80 p-4 z-[100]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-17b552e6a5168f0e\" + \" \" + \"text-sm text-gray-500 mb-2\",\n                                children: t(\"products.popular_searches\", {\n                                    fallback: \"Popular Searches\"\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductSearch.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-17b552e6a5168f0e\" + \" \" + \"flex flex-wrap gap-2\",\n                                children: t(\"products.search_tags\", {\n                                    fallback: \"Interactive Projection,Digital Sandbox,Holographic Projection,AR Experience,VR Equipment\"\n                                }).split(\",\").map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onMouseDown: (e)=>{\n                                            e.preventDefault(); // 防止失焦\n                                            e.stopPropagation(); // 阻止事件冒泡\n                                        },\n                                        onClick: (e)=>{\n                                            e.preventDefault();\n                                            e.stopPropagation();\n                                            handleTagClick(tag);\n                                        },\n                                        className: \"jsx-17b552e6a5168f0e\" + \" \" + \"px-3 py-1 bg-gray-50 hover:bg-blue-50 hover:text-blue-600 rounded-full text-sm transition-colors duration-200 cursor-pointer\",\n                                        children: tag.trim()\n                                    }, tag, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductSearch.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductSearch.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductSearch.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductSearch.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ProductSearch.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductSearch, \"9m6aydnuLiSfozAj1Dlg7tRqWSk=\", false, function() {\n    return [\n        _LanguageProvider__WEBPACK_IMPORTED_MODULE_3__.useLanguage\n    ];\n});\n_c = ProductSearch;\nvar _c;\n$RefreshReg$(_c, \"ProductSearch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ProductSearch.tsx\n"));

/***/ })

});