'use client';

import { useState } from 'react';

export default function EmailJSTest() {
  const [sending, setSending] = useState(false);
  const [result, setResult] = useState<any>(null);

  const sendTestEmail = async () => {
    setSending(true);
    setResult(null);

    try {
      // 动态导入EmailJS
      const emailjs = await import('@emailjs/browser');

      // 测试数据
      const testData = {
        id: 9999,
        name: "王董事长",
        email: "<EMAIL>",
        phone: "+86 138-9999-8888",
        country: "中国",
        playground_size: "10000+ sqm",
        product: "全球领先AR互动娱乐生态系统",
        message: "您好，我们是一家跨国集团公司，在全球拥有200多家大型商业综合体。我们正在启动一个总投资50亿元的全球娱乐产业升级项目，计划在未来3年内在全球50个城市部署最先进的AR互动娱乐设施。我们对贵公司的技术实力和产品创新能力非常认可，希望能够建立战略合作伙伴关系。请提供：1. 全球化部署方案 2. 技术授权和本地化服务 3. 长期战略合作框架 4. 投资回报和市场前景分析。我们期待与您深入洽谈，共同开创AR娱乐产业的新纪元。",
        created_at: new Date().toISOString()
      };

      // EmailJS配置
      const serviceId = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID || 'service_gmail';
      const templateId = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID || 'template_form';
      const publicKey = process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY || 'NEGhUwls3EE6iiapf';

      console.log('EmailJS配置:', { serviceId, templateId, publicKey });

      // 邮件参数
      const templateParams = {
        to_email: '<EMAIL>',
        from_name: '跨境电商网站',
        subject: `🔔 重要客户咨询 - ${testData.name}`,
        customer_name: testData.name,
        customer_email: testData.email,
        customer_phone: testData.phone,
        customer_country: testData.country,
        playground_size: testData.playground_size,
        product_interest: testData.product,
        customer_message: testData.message,
        submission_time: new Date(testData.created_at).toLocaleString('zh-CN'),
        submission_id: testData.id,
        // 完整邮件内容
        email_content: `
🔔 新的表单提交通知

👤 客户信息:
- 姓名: ${testData.name}
- 邮箱: ${testData.email}
- 电话: ${testData.phone}
- 国家: ${testData.country}
- 场地大小: ${testData.playground_size}
- 感兴趣的产品: ${testData.product}

💬 客户留言:
${testData.message}

📅 提交时间: ${new Date(testData.created_at).toLocaleString('zh-CN')}
🆔 提交ID: #${testData.id}

此邮件由跨境电商网站系统通过 EmailJS 服务自动发送
        `
      };

      console.log('发送邮件参数:', templateParams);

      // 发送邮件
      const response = await emailjs.send(
        serviceId,
        templateId,
        templateParams,
        publicKey
      );

      console.log('EmailJS发送成功:', response);

      setResult({
        success: true,
        message: 'EmailJS邮件发送成功！',
        service: 'EmailJS',
        messageId: response.text,
        status: response.status,
        sentTo: '<EMAIL>',
        customerInfo: {
          name: testData.name,
          phone: testData.phone,
          email: testData.email,
          product: testData.product
        }
      });

    } catch (error) {
      console.error('EmailJS发送失败:', error);
      
      setResult({
        success: false,
        message: 'EmailJS发送失败',
        error: error instanceof Error ? error.message : 'Unknown error',
        suggestion: '请检查EmailJS配置或网络连接'
      });
    } finally {
      setSending(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">📧 EmailJS 邮件测试</h1>
        <p className="text-gray-600">
          测试EmailJS前端邮件发送功能，直接发送到 <strong><EMAIL></strong>
        </p>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">📋 EmailJS配置信息</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">Public Key:</span>
            <span className="ml-2 text-blue-600">NEGhUwls3EE6iiapf</span>
          </div>
          <div>
            <span className="font-medium">Service ID:</span>
            <span className="ml-2 text-blue-600">service_gmail</span>
          </div>
          <div>
            <span className="font-medium">Template ID:</span>
            <span className="ml-2 text-blue-600">template_form</span>
          </div>
          <div>
            <span className="font-medium">目标邮箱:</span>
            <span className="ml-2 text-blue-600"><EMAIL></span>
          </div>
        </div>
      </div>

      <div className="text-center mb-6">
        <button
          onClick={sendTestEmail}
          disabled={sending}
          className={`px-8 py-3 rounded-lg font-semibold text-white transition-all duration-200 ${
            sending
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800'
          }`}
        >
          {sending ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              发送中...
            </span>
          ) : (
            '🚀 发送测试邮件'
          )}
        </button>
      </div>

      {result && (
        <div className={`rounded-lg p-6 ${result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
          <h3 className={`text-lg font-semibold mb-4 ${result.success ? 'text-green-800' : 'text-red-800'}`}>
            {result.success ? '✅ 发送成功' : '❌ 发送失败'}
          </h3>
          
          <div className="space-y-2 text-sm">
            <div>
              <span className="font-medium">消息:</span>
              <span className="ml-2">{result.message}</span>
            </div>
            
            {result.success && (
              <>
                <div>
                  <span className="font-medium">服务:</span>
                  <span className="ml-2">{result.service}</span>
                </div>
                <div>
                  <span className="font-medium">发送到:</span>
                  <span className="ml-2">{result.sentTo}</span>
                </div>
                <div>
                  <span className="font-medium">消息ID:</span>
                  <span className="ml-2">{result.messageId}</span>
                </div>
                <div>
                  <span className="font-medium">状态码:</span>
                  <span className="ml-2">{result.status}</span>
                </div>
                {result.customerInfo && (
                  <div className="mt-4 p-3 bg-white rounded border">
                    <span className="font-medium">客户信息:</span>
                    <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                      <div>姓名: {result.customerInfo.name}</div>
                      <div>电话: {result.customerInfo.phone}</div>
                      <div>邮箱: {result.customerInfo.email}</div>
                      <div>产品: {result.customerInfo.product}</div>
                    </div>
                  </div>
                )}
              </>
            )}
            
            {!result.success && (
              <>
                <div>
                  <span className="font-medium">错误:</span>
                  <span className="ml-2 text-red-600">{result.error}</span>
                </div>
                <div>
                  <span className="font-medium">建议:</span>
                  <span className="ml-2">{result.suggestion}</span>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      <div className="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">📝 使用说明</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• EmailJS是前端邮件发送服务，可以直接发送到任何邮箱</li>
          <li>• 免费额度：200封邮件/月</li>
          <li>• 无需后端SMTP配置</li>
          <li>• 支持HTML邮件模板</li>
          <li>• 实时发送状态反馈</li>
        </ul>
      </div>
    </div>
  );
}
