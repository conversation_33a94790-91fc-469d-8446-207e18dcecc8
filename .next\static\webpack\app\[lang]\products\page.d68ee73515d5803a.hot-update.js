"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lang]/products/page",{

/***/ "(app-pages-browser)/./app/[lang]/products/page.tsx":
/*!**************************************!*\
  !*** ./app/[lang]/products/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/i18n */ \"(app-pages-browser)/./app/utils/i18n.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_components_ProductGrid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/components/ProductGrid */ \"(app-pages-browser)/./app/components/ProductGrid.tsx\");\n/* harmony import */ var _app_components_QuoteForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/components/QuoteForm */ \"(app-pages-browser)/./app/components/QuoteForm.tsx\");\n/* harmony import */ var _app_components_PageHeader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/components/PageHeader */ \"(app-pages-browser)/./app/components/PageHeader.tsx\");\n/* harmony import */ var _app_components_ProductSectionCarousel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/components/ProductSectionCarousel */ \"(app-pages-browser)/./app/components/ProductSectionCarousel.tsx\");\n/* harmony import */ var _app_components_ProductSearch__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/components/ProductSearch */ \"(app-pages-browser)/./app/components/ProductSearch.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// 产品列表页面\n\n\n\n\n\n\n\n\n// 默认字典值 - 添加必要的键以避免未定义错误\nconst defaultDictionary = {\n    products: {\n        title: \"Products\",\n        all_products_title: \"All Products\",\n        all_products_subtitle: \"Browse our complete product series\",\n        description: \"We provide various innovative product solutions\",\n        filter: {\n            all: \"All\"\n        }\n    },\n    common: {\n        home: \"Home\",\n        products: \"Products\",\n        loading: \"Loading...\",\n        view_details: \"View Details\"\n    }\n};\n// 产品列表页面组件\nfunction ProductsPage(param) {\n    let { params } = param;\n    var _dictionary_products, _dictionary_products1, _dictionary_products2;\n    _s();\n    // 状态和钩子\n    const [dictionary, setDictionary] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(defaultDictionary);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    // 引用元素用于动画\n    const separatorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const carouselRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const quoteRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 滚动动画观察器\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const observerOptions = {\n            threshold: 0.1,\n            rootMargin: \"0px 0px -50px 0px\"\n        };\n        const observer = new IntersectionObserver((entries)=>{\n            entries.forEach((entry)=>{\n                if (entry.isIntersecting) {\n                    entry.target.classList.add(\"animate-in\");\n                }\n            });\n        }, observerOptions);\n        // 观察所有需要动画的元素\n        const elementsToObserve = [\n            separatorRef.current,\n            carouselRef.current,\n            gridRef.current,\n            quoteRef.current\n        ].filter(Boolean);\n        elementsToObserve.forEach((el)=>{\n            if (el) observer.observe(el);\n        });\n        return ()=>observer.disconnect();\n    }, [\n        isLoading\n    ]);\n    // 页面加载动画\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            setIsVisible(true);\n        }, 100);\n        return ()=>clearTimeout(timer);\n    }, []);\n    // 加载词典\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        let isMounted = true; // 防止组件卸载后更新状态\n        const loadDictionary = async ()=>{\n            try {\n                setIsLoading(true);\n                setError(null);\n                // 获取字典，添加时间戳防止缓存问题\n                const dict = await (0,_utils_i18n__WEBPACK_IMPORTED_MODULE_3__.getDictionary)(params.lang);\n                if (isMounted) {\n                    // 确保合并默认字典，以避免未定义的键\n                    setDictionary({\n                        ...defaultDictionary,\n                        ...dict,\n                        products: {\n                            ...defaultDictionary.products,\n                            ...dict.products || {}\n                        },\n                        common: {\n                            ...defaultDictionary.common,\n                            ...dict.common || {}\n                        }\n                    });\n                }\n            } catch (error) {\n                console.error(\"Failed to load dictionary:\", error);\n                if (isMounted) {\n                    setError(params.lang === \"zh\" ? \"加载数据失败，使用默认内容\" : \"Failed to load data, using default content\");\n                // 保留默认字典\n                }\n            } finally{\n                if (isMounted) {\n                    setIsLoading(false);\n                }\n            }\n        };\n        loadDictionary();\n        // 清理函数\n        return ()=>{\n            isMounted = false;\n        };\n    }, [\n        params.lang\n    ]);\n    // 正在加载时显示骨架屏\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 mt-20\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-10 bg-gray-200 rounded w-3/4 mx-auto mb-8\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: [\n                            ...Array(6)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-200 rounded-lg h-80\"\n                            }, i, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, this);\n    }\n    // 显示错误信息（如果有）\n    if (error) {\n        console.warn(\"使用默认字典，因为:\", error);\n    }\n    // 确保字典数据已定义\n    const productsDict = dictionary.products || defaultDictionary.products || {\n        all_products_title: params.lang === \"zh\" ? \"产品列表\" : \"Product List\",\n        all_products_subtitle: params.lang === \"zh\" ? \"浏览我们的产品系列\" : \"Browse our product series\"\n    };\n    // 确保common数据已定义\n    const commonDict = dictionary.common || defaultDictionary.common || {\n        home: params.lang === \"zh\" ? \"首页\" : \"Home\",\n        products: params.lang === \"zh\" ? \"产品\" : \"Products\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"1c903f77e1426911\",\n                dynamic: [\n                    isVisible ? 1 : 0,\n                    isVisible ? \"0\" : \"20px\"\n                ],\n                children: \".product-list-page.__jsx-style-dynamic-selector{opacity:\".concat(isVisible ? 1 : 0, \";-webkit-transform:translatey(\").concat(isVisible ? \"0\" : \"20px\", \");-moz-transform:translatey(\").concat(isVisible ? \"0\" : \"20px\", \");-ms-transform:translatey(\").concat(isVisible ? \"0\" : \"20px\", \");-o-transform:translatey(\").concat(isVisible ? \"0\" : \"20px\", \");transform:translatey(\").concat(isVisible ? \"0\" : \"20px\", \");-webkit-transition:all.8s cubic-bezier(.4,0,.2,1);-moz-transition:all.8s cubic-bezier(.4,0,.2,1);-o-transition:all.8s cubic-bezier(.4,0,.2,1);transition:all.8s cubic-bezier(.4,0,.2,1)}.animate-separator.__jsx-style-dynamic-selector{opacity:0;-webkit-transform:scalex(0);-moz-transform:scalex(0);-ms-transform:scalex(0);-o-transform:scalex(0);transform:scalex(0);-webkit-transition:all 1s cubic-bezier(.4,0,.2,1);-moz-transition:all 1s cubic-bezier(.4,0,.2,1);-o-transition:all 1s cubic-bezier(.4,0,.2,1);transition:all 1s cubic-bezier(.4,0,.2,1)}.animate-separator.animate-in.__jsx-style-dynamic-selector{opacity:1;-webkit-transform:scalex(1);-moz-transform:scalex(1);-ms-transform:scalex(1);-o-transform:scalex(1);transform:scalex(1)}.animate-carousel.__jsx-style-dynamic-selector{opacity:0;-webkit-transform:translatey(50px);-moz-transform:translatey(50px);-ms-transform:translatey(50px);-o-transform:translatey(50px);transform:translatey(50px);-webkit-transition:all 1.2s cubic-bezier(.4,0,.2,1);-moz-transition:all 1.2s cubic-bezier(.4,0,.2,1);-o-transition:all 1.2s cubic-bezier(.4,0,.2,1);transition:all 1.2s cubic-bezier(.4,0,.2,1)}.animate-carousel.animate-in.__jsx-style-dynamic-selector{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-ms-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}.animate-grid.__jsx-style-dynamic-selector{opacity:0;-webkit-transform:translatey(40px);-moz-transform:translatey(40px);-ms-transform:translatey(40px);-o-transform:translatey(40px);transform:translatey(40px);-webkit-transition:all 1s cubic-bezier(.4,0,.2,1).2s;-moz-transition:all 1s cubic-bezier(.4,0,.2,1).2s;-o-transition:all 1s cubic-bezier(.4,0,.2,1).2s;transition:all 1s cubic-bezier(.4,0,.2,1).2s}.animate-grid.animate-in.__jsx-style-dynamic-selector{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-ms-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}.animate-quote.__jsx-style-dynamic-selector{opacity:0;-webkit-transform:translatey(30px)scale(.95);-moz-transform:translatey(30px)scale(.95);-ms-transform:translatey(30px)scale(.95);-o-transform:translatey(30px)scale(.95);transform:translatey(30px)scale(.95);-webkit-transition:all 1s cubic-bezier(.4,0,.2,1).4s;-moz-transition:all 1s cubic-bezier(.4,0,.2,1).4s;-o-transition:all 1s cubic-bezier(.4,0,.2,1).4s;transition:all 1s cubic-bezier(.4,0,.2,1).4s}.animate-quote.animate-in.__jsx-style-dynamic-selector{opacity:1;-webkit-transform:translatey(0)scale(1);-moz-transform:translatey(0)scale(1);-ms-transform:translatey(0)scale(1);-o-transform:translatey(0)scale(1);transform:translatey(0)scale(1)}.floating-particles.__jsx-style-dynamic-selector{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none;overflow:hidden}.particle.__jsx-style-dynamic-selector{position:absolute;width:4px;height:4px;background:-webkit-linear-gradient(45deg,#3b82f6,#8b5cf6);background:-moz-linear-gradient(45deg,#3b82f6,#8b5cf6);background:-o-linear-gradient(45deg,#3b82f6,#8b5cf6);background:linear-gradient(45deg,#3b82f6,#8b5cf6);-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;-webkit-animation:float 6s ease-in-out infinite;-moz-animation:float 6s ease-in-out infinite;-o-animation:float 6s ease-in-out infinite;animation:float 6s ease-in-out infinite;opacity:.6}.particle.__jsx-style-dynamic-selector:nth-child(1){left:10%;-webkit-animation-delay:0s;-moz-animation-delay:0s;-o-animation-delay:0s;animation-delay:0s}.particle.__jsx-style-dynamic-selector:nth-child(2){left:20%;-webkit-animation-delay:1s;-moz-animation-delay:1s;-o-animation-delay:1s;animation-delay:1s}.particle.__jsx-style-dynamic-selector:nth-child(3){left:30%;-webkit-animation-delay:2s;-moz-animation-delay:2s;-o-animation-delay:2s;animation-delay:2s}.particle.__jsx-style-dynamic-selector:nth-child(4){left:40%;-webkit-animation-delay:3s;-moz-animation-delay:3s;-o-animation-delay:3s;animation-delay:3s}.particle.__jsx-style-dynamic-selector:nth-child(5){left:50%;-webkit-animation-delay:4s;-moz-animation-delay:4s;-o-animation-delay:4s;animation-delay:4s}.particle.__jsx-style-dynamic-selector:nth-child(6){left:60%;-webkit-animation-delay:5s;-moz-animation-delay:5s;-o-animation-delay:5s;animation-delay:5s}.particle.__jsx-style-dynamic-selector:nth-child(7){left:70%;-webkit-animation-delay:.5s;-moz-animation-delay:.5s;-o-animation-delay:.5s;animation-delay:.5s}.particle.__jsx-style-dynamic-selector:nth-child(8){left:80%;-webkit-animation-delay:1.5s;-moz-animation-delay:1.5s;-o-animation-delay:1.5s;animation-delay:1.5s}.particle.__jsx-style-dynamic-selector:nth-child(9){left:90%;-webkit-animation-delay:2.5s;-moz-animation-delay:2.5s;-o-animation-delay:2.5s;animation-delay:2.5s}@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}25%{-webkit-transform:translatey(-20px)rotate(90deg);transform:translatey(-20px)rotate(90deg)}50%{-webkit-transform:translatey(-40px)rotate(180deg);transform:translatey(-40px)rotate(180deg)}75%{-webkit-transform:translatey(-20px)rotate(270deg);transform:translatey(-20px)rotate(270deg)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}25%{-moz-transform:translatey(-20px)rotate(90deg);transform:translatey(-20px)rotate(90deg)}50%{-moz-transform:translatey(-40px)rotate(180deg);transform:translatey(-40px)rotate(180deg)}75%{-moz-transform:translatey(-20px)rotate(270deg);transform:translatey(-20px)rotate(270deg)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}25%{-o-transform:translatey(-20px)rotate(90deg);transform:translatey(-20px)rotate(90deg)}50%{-o-transform:translatey(-40px)rotate(180deg);transform:translatey(-40px)rotate(180deg)}75%{-o-transform:translatey(-20px)rotate(270deg);transform:translatey(-20px)rotate(270deg)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}25%{-webkit-transform:translatey(-20px)rotate(90deg);-moz-transform:translatey(-20px)rotate(90deg);-o-transform:translatey(-20px)rotate(90deg);transform:translatey(-20px)rotate(90deg)}50%{-webkit-transform:translatey(-40px)rotate(180deg);-moz-transform:translatey(-40px)rotate(180deg);-o-transform:translatey(-40px)rotate(180deg);transform:translatey(-40px)rotate(180deg)}75%{-webkit-transform:translatey(-20px)rotate(270deg);-moz-transform:translatey(-20px)rotate(270deg);-o-transform:translatey(-20px)rotate(270deg);transform:translatey(-20px)rotate(270deg)}}.pulse-glow.__jsx-style-dynamic-selector{-webkit-animation:pulseGlow 3s ease-in-out infinite;-moz-animation:pulseGlow 3s ease-in-out infinite;-o-animation:pulseGlow 3s ease-in-out infinite;animation:pulseGlow 3s ease-in-out infinite}@-webkit-keyframes pulseGlow{0%,100%{-webkit-box-shadow:0 0 20px rgba(59,130,246,.3);box-shadow:0 0 20px rgba(59,130,246,.3)}50%{-webkit-box-shadow:0 0 40px rgba(59,130,246,.6);box-shadow:0 0 40px rgba(59,130,246,.6)}}@-moz-keyframes pulseGlow{0%,100%{-moz-box-shadow:0 0 20px rgba(59,130,246,.3);box-shadow:0 0 20px rgba(59,130,246,.3)}50%{-moz-box-shadow:0 0 40px rgba(59,130,246,.6);box-shadow:0 0 40px rgba(59,130,246,.6)}}@-o-keyframes pulseGlow{0%,100%{box-shadow:0 0 20px rgba(59,130,246,.3)}50%{box-shadow:0 0 40px rgba(59,130,246,.6)}}@keyframes pulseGlow{0%,100%{-webkit-box-shadow:0 0 20px rgba(59,130,246,.3);-moz-box-shadow:0 0 20px rgba(59,130,246,.3);box-shadow:0 0 20px rgba(59,130,246,.3)}50%{-webkit-box-shadow:0 0 40px rgba(59,130,246,.6);-moz-box-shadow:0 0 40px rgba(59,130,246,.6);box-shadow:0 0 40px rgba(59,130,246,.6)}}\")\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginTop: 0,\n                    paddingTop: 0\n                },\n                className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                    [\n                        \"1c903f77e1426911\",\n                        [\n                            isVisible ? 1 : 0,\n                            isVisible ? \"0\" : \"20px\"\n                        ]\n                    ]\n                ]) + \" \" + \"product-list-page w-full overflow-hidden relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                            [\n                                \"1c903f77e1426911\",\n                                [\n                                    isVisible ? 1 : 0,\n                                    isVisible ? \"0\" : \"20px\"\n                                ]\n                            ]\n                        ]) + \" \" + \"floating-particles\",\n                        children: [\n                            ...Array(9)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                    [\n                                        \"1c903f77e1426911\",\n                                        [\n                                            isVisible ? 1 : 0,\n                                            isVisible ? \"0\" : \"20px\"\n                                        ]\n                                    ]\n                                ]) + \" \" + \"particle\"\n                            }, i, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_PageHeader__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        title: productsDict.all_products_title || (params.lang === \"zh\" ? \"产品列表\" : \"Product List\"),\n                        subtitle: productsDict.all_products_subtitle || (params.lang === \"zh\" ? \"浏览我们的产品系列\" : \"Browse our product series\"),\n                        bgImage: \"/images/products/product-banner.png\",\n                        height: \"md:h-[520px] h-[420px]\",\n                        overlayOpacity: 0.6,\n                        useImageComponent: true,\n                        animationEffect: \"up\",\n                        className: \"mb-12 mt-0 pt-0\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                            [\n                                \"1c903f77e1426911\",\n                                [\n                                    isVisible ? 1 : 0,\n                                    isVisible ? \"0\" : \"20px\"\n                                ]\n                            ]\n                        ]) + \" \" + \"w-full flex justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: separatorRef,\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"1c903f77e1426911\",\n                                    [\n                                        isVisible ? 1 : 0,\n                                        isVisible ? \"0\" : \"20px\"\n                                    ]\n                                ]\n                            ]) + \" \" + \"animate-separator w-2/3 h-1 bg-gradient-to-r from-transparent via-blue-300 to-transparent rounded-full mb-10 mt-6 pulse-glow\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: carouselRef,\n                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                            [\n                                \"1c903f77e1426911\",\n                                [\n                                    isVisible ? 1 : 0,\n                                    isVisible ? \"0\" : \"20px\"\n                                ]\n                            ]\n                        ]) + \" \" + \"animate-carousel\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_ProductSectionCarousel__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                            [\n                                \"1c903f77e1426911\",\n                                [\n                                    isVisible ? 1 : 0,\n                                    isVisible ? \"0\" : \"20px\"\n                                ]\n                            ]\n                        ]) + \" \" + \"w-full py-12 px-6 bg-gradient-to-br from-slate-50 via-blue-50/40 to-indigo-50/40 relative border-t border-b border-gray-100/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                    [\n                                        \"1c903f77e1426911\",\n                                        [\n                                            isVisible ? 1 : 0,\n                                            isVisible ? \"0\" : \"20px\"\n                                        ]\n                                    ]\n                                ]) + \" \" + \"absolute inset-0 bg-gradient-to-r from-blue-100/30 via-transparent to-indigo-100/30\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                    [\n                                        \"1c903f77e1426911\",\n                                        [\n                                            isVisible ? 1 : 0,\n                                            isVisible ? \"0\" : \"20px\"\n                                        ]\n                                    ]\n                                ]) + \" \" + \"absolute top-0 left-1/3 w-72 h-72 bg-blue-200/20 rounded-full blur-3xl\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                    [\n                                        \"1c903f77e1426911\",\n                                        [\n                                            isVisible ? 1 : 0,\n                                            isVisible ? \"0\" : \"20px\"\n                                        ]\n                                    ]\n                                ]) + \" \" + \"absolute bottom-0 right-1/3 w-72 h-72 bg-indigo-200/20 rounded-full blur-3xl\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                    [\n                                        \"1c903f77e1426911\",\n                                        [\n                                            isVisible ? 1 : 0,\n                                            isVisible ? \"0\" : \"20px\"\n                                        ]\n                                    ]\n                                ]) + \" \" + \"relative z-10 max-w-6xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                            [\n                                                \"1c903f77e1426911\",\n                                                [\n                                                    isVisible ? 1 : 0,\n                                                    isVisible ? \"0\" : \"20px\"\n                                                ]\n                                            ]\n                                        ]) + \" \" + \"text-center mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                                    [\n                                                        \"1c903f77e1426911\",\n                                                        [\n                                                            isVisible ? 1 : 0,\n                                                            isVisible ? \"0\" : \"20px\"\n                                                        ]\n                                                    ]\n                                                ]) + \" \" + \"text-2xl md:text-3xl font-bold text-gray-800 mb-3\",\n                                                children: ((_dictionary_products = dictionary.products) === null || _dictionary_products === void 0 ? void 0 : _dictionary_products.search_title) || (params.lang === \"zh\" ? \"找到您需要的产品\" : \"Find the Product You Need\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                                    [\n                                                        \"1c903f77e1426911\",\n                                                        [\n                                                            isVisible ? 1 : 0,\n                                                            isVisible ? \"0\" : \"20px\"\n                                                        ]\n                                                    ]\n                                                ]) + \" \" + \"text-gray-600 text-base md:text-lg max-w-2xl mx-auto\",\n                                                children: ((_dictionary_products1 = dictionary.products) === null || _dictionary_products1 === void 0 ? void 0 : _dictionary_products1.search_subtitle) || (params.lang === \"zh\" ? \"搜索我们的产品库，发现最适合您需求的解决方案\" : \"Search our product catalog to discover the perfect solution for your needs\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_ProductSearch__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        placeholder: ((_dictionary_products2 = dictionary.products) === null || _dictionary_products2 === void 0 ? void 0 : _dictionary_products2.search_placeholder) || (params.lang === \"zh\" ? \"搜索产品名称、型号或关键词...\" : \"Search product name, model or keywords...\"),\n                                        onSearch: (query)=>{\n                                            console.log(params.lang === \"zh\" ? \"搜索:\" : \"Search:\", query);\n                                            setSearchQuery(query);\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: gridRef,\n                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                            [\n                                \"1c903f77e1426911\",\n                                [\n                                    isVisible ? 1 : 0,\n                                    isVisible ? \"0\" : \"20px\"\n                                ]\n                            ]\n                        ]) + \" \" + \"animate-grid max-w-7xl mx-auto px-6 py-12 relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_ProductGrid__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            searchQuery: searchQuery\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                            [\n                                \"1c903f77e1426911\",\n                                [\n                                    isVisible ? 1 : 0,\n                                    isVisible ? \"0\" : \"20px\"\n                                ]\n                            ]\n                        ]) + \" \" + \"w-full px-4 pb-16 mt-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: quoteRef,\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"1c903f77e1426911\",\n                                    [\n                                        isVisible ? 1 : 0,\n                                        isVisible ? \"0\" : \"20px\"\n                                    ]\n                                ]\n                            ]) + \" \" + \"animate-quote container mx-auto max-w-4xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_QuoteForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"products-page-quote-form\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ProductsPage, \"wkv92G0NyVOnGahiN8goq22KAy4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname\n    ];\n});\n_c = ProductsPage;\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[lang]/products/page.tsx\n"));

/***/ })

});