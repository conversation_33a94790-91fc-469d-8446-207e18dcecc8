import { getDictionary } from '../../../utils/i18n';
import Link from 'next/link';
import { solutionPlaceholders } from '../../../utils/imagePlaceholder';
import ModernProductGrid from '../../../components/ModernProductGrid';

export default async function SmallSizePage({ params }: { params: { lang: string } }) {
  const { lang } = params;
  const dict = await getDictionary(lang);

  // 100-500 sqm 产品数据
  const products = [
    {
      url: `/${lang}/products/240-sqm-customizable-morandi-style-indoor-playground-solution`,
      image: solutionPlaceholders[1],
      title:
        dict.solutions?.product2 || '240 SQM Customizable Morandi Style Indoor Playground Solution',
    },
    {
      url: `/${lang}/products/400-sqm-customizable-interstellar-style-indoor-playground-solution`,
      image: solutionPlaceholders[4],
      title:
        dict.solutions?.product5 ||
        '400 SQM Customizable Interstellar Style Indoor Playground Solution',
    },
    {
      url: `/${lang}/products/375-sqm-customizable-pastel-style-indoor-playground-solution`,
      image: solutionPlaceholders[7],
      title:
        dict.solutions?.product8 || '375 SQM Customizable Pastel Style Indoor Playground Solution',
    },
  ];

  return (
    <>
      <section className="page-banner">
        <div className="container">
          <h1 className="page-title">
            {dict.products?.categories?.small || '100-500 SQM Playgrounds'}
          </h1>
          <div className="breadcrumb">
            <Link href={`/${lang}`}>{dict.common?.home || 'Home'}</Link> /
            <Link href={`/${lang}/products`}>{dict.common?.products || 'Products'}</Link> /
            <span>{dict.products?.filter?.small || '100-500 sqm'}</span>
          </div>
        </div>
      </section>

      <section className="products-page">
        <div className="container">
          <div className="page-header">
            <p className="page-description">
              {dict.products?.small?.description ||
                'Our 100-500 sqm playground solutions are perfect for smaller venues looking to maximize play value in limited space. These compact yet feature-rich designs offer excellent return on investment for shopping malls, family entertainment centers, and retail locations.'}
            </p>
          </div>

          <div className="product-filters">
            <Link href={`/${lang}/products`} className="filter-item">
              {dict.products?.filter?.all || 'All Products'}
            </Link>
            <Link href={`/${lang}/products/indoor-playground`} className="filter-item">
              {dict.products?.filter?.indoor || 'Indoor Playgrounds'}
            </Link>
            <Link href={`/${lang}/products/trampoline-park`} className="filter-item">
              {dict.products?.filter?.trampoline || 'Trampoline Parks'}
            </Link>
          </div>

          <ModernProductGrid products={products} />

          <div className="size-benefits">
            <h2>
              {dict.products?.small?.benefits?.title || 'Benefits of 100-500 SQM Playgrounds'}
            </h2>
            <div className="benefits-grid">
              <div className="benefit-item">
                <div className="benefit-icon">
                  <i className="fas fa-dollar-sign"></i>
                </div>
                <h3>{dict.products?.small?.benefits?.cost?.title || 'Lower Initial Investment'}</h3>
                <p>
                  {dict.products?.small?.benefits?.cost?.description ||
                    'Smaller footprint means reduced startup costs while still providing a complete play experience.'}
                </p>
              </div>

              <div className="benefit-item">
                <div className="benefit-icon">
                  <i className="fas fa-map-marker-alt"></i>
                </div>
                <h3>
                  {dict.products?.small?.benefits?.location?.title || 'More Location Options'}
                </h3>
                <p>
                  {dict.products?.small?.benefits?.location?.description ||
                    'Fits into a wider variety of commercial spaces, opening up more potential venue options.'}
                </p>
              </div>

              <div className="benefit-item">
                <div className="benefit-icon">
                  <i className="fas fa-chart-line"></i>
                </div>
                <h3>{dict.products?.small?.benefits?.roi?.title || 'Faster ROI'}</h3>
                <p>
                  {dict.products?.small?.benefits?.roi?.description ||
                    'Lower operating costs and overhead typically result in quicker return on investment.'}
                </p>
              </div>

              <div className="benefit-item">
                <div className="benefit-icon">
                  <i className="fas fa-expand-arrows-alt"></i>
                </div>
                <h3>{dict.products?.small?.benefits?.efficient?.title || 'Space Efficiency'}</h3>
                <p>
                  {dict.products?.small?.benefits?.efficient?.description ||
                    'Our designs maximize play value per square meter through clever multi-level structures.'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="cta-section cta-particles">
        <div className="container">
          <div className="cta-content">
            <h2>准备好讨论您的全息定制解决方案？</h2>
            <p>今天就联系我们的团队，探索我们如何为您的需求创造完美的全息解决方案。</p>
            <Link
              href={`/${lang}/pages/contact-us`}
              className="btn-primary"
              data-text="立即联系我们"
            >
              {'立即联系我们'.split('').map((char, index) => (
                <i key={index}>{char === ' ' ? '\u00A0' : char}</i>
              ))}
            </Link>
          </div>
        </div>
      </section>
    </>
  );
}
