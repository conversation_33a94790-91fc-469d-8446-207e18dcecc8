'use client';

import { useLanguage } from '../../../components/LanguageProvider';
import Image from 'next/image';
import Link from 'next/link';
import { generatePlaceholderSVG } from '../../../utils/imagePlaceholder';

export default function MarketingSupportPage() {
  const { t, locale } = useLanguage();

  return (
    <>
      <section className="page-header">
        <div className="container">
          <h1 className="page-title">{t('dropdown.marketing_support')}</h1>
          <div className="breadcrumbs">
            <Link href={`/${locale}`}>{t('common.home')}</Link> &gt;
            <Link href={`/${locale}/pages/service`}>{t('common.services')}</Link> &gt;
            <span>{t('dropdown.marketing_support')}</span>
          </div>
        </div>
      </section>

      <section className="marketing-intro">
        <div className="container">
          <div className="content-grid">
            <div className="content-image">
              <Image
                src={generatePlaceholderSVG(600, 400, 'Marketing Support')}
                alt={t('marketing_support.intro.image_alt', {
                  fallback: 'Marketing Support for Indoor Playgrounds',
                })}
                width={600}
                height={400}
              />
            </div>
            <div className="content-text">
              <h2>
                {t('marketing_support.intro.title', {
                  fallback: 'Beyond Equipment: Your Success Is Our Priority',
                })}
              </h2>
              <p>
                {t('marketing_support.intro.paragraph1', {
                  fallback:
                    "At Infinity Playground Park, we understand that creating a successful indoor playground business involves more than just installing quality equipment. Marketing and promotion are crucial aspects that can determine your venture's success.",
                })}
              </p>
              <p>
                {t('marketing_support.intro.paragraph2', {
                  fallback:
                    "That's why we offer comprehensive marketing support to help you attract customers, build your brand, and maximize your business potential. Our team of marketing experts will work with you from pre-opening through ongoing operations.",
                })}
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="marketing-services">
        <div className="container">
          <h2 className="section-title">
            {t('marketing_support.services.title', { fallback: 'Our Marketing Support Services' })}
          </h2>

          <div className="services-grid">
            <div className="service-item">
              <div className="service-icon">
                <i className="fas fa-paint-brush"></i>
              </div>
              <h3>
                {t('marketing_support.services.brand.title', { fallback: 'Brand Development' })}
              </h3>
              <ul>
                <li>
                  {t('marketing_support.services.brand.item1', {
                    fallback: 'Logo design assistance',
                  })}
                </li>
                <li>
                  {t('marketing_support.services.brand.item2', {
                    fallback: 'Brand identity development',
                  })}
                </li>
                <li>
                  {t('marketing_support.services.brand.item3', {
                    fallback: 'Color scheme and theme recommendations',
                  })}
                </li>
                <li>
                  {t('marketing_support.services.brand.item4', {
                    fallback: 'Signage design support',
                  })}
                </li>
              </ul>
            </div>

            <div className="service-item">
              <div className="service-icon">
                <i className="fas fa-globe"></i>
              </div>
              <h3>
                {t('marketing_support.services.digital.title', { fallback: 'Digital Marketing' })}
              </h3>
              <ul>
                <li>
                  {t('marketing_support.services.digital.item1', {
                    fallback: 'Website design recommendations',
                  })}
                </li>
                <li>
                  {t('marketing_support.services.digital.item2', {
                    fallback: 'Social media strategy guidance',
                  })}
                </li>
                <li>
                  {t('marketing_support.services.digital.item3', {
                    fallback: 'Search engine optimization (SEO) tips',
                  })}
                </li>
                <li>
                  {t('marketing_support.services.digital.item4', {
                    fallback: 'Online reputation management advice',
                  })}
                </li>
              </ul>
            </div>

            <div className="service-item">
              <div className="service-icon">
                <i className="fas fa-bullhorn"></i>
              </div>
              <h3>
                {t('marketing_support.services.promotional.title', {
                  fallback: 'Promotional Materials',
                })}
              </h3>
              <ul>
                <li>
                  {t('marketing_support.services.promotional.item1', {
                    fallback: 'Customizable flyer and brochure templates',
                  })}
                </li>
                <li>
                  {t('marketing_support.services.promotional.item2', {
                    fallback: 'Digital advertisement templates',
                  })}
                </li>
                <li>
                  {t('marketing_support.services.promotional.item3', {
                    fallback: 'Email marketing templates',
                  })}
                </li>
                <li>
                  {t('marketing_support.services.promotional.item4', {
                    fallback: 'Promotional video assistance',
                  })}
                </li>
              </ul>
            </div>

            <div className="service-item">
              <div className="service-icon">
                <i className="fas fa-calendar-alt"></i>
              </div>
              <h3>
                {t('marketing_support.services.opening.title', {
                  fallback: 'Grand Opening Support',
                })}
              </h3>
              <ul>
                <li>
                  {t('marketing_support.services.opening.item1', {
                    fallback: 'Grand opening planning guidance',
                  })}
                </li>
                <li>
                  {t('marketing_support.services.opening.item2', {
                    fallback: 'Media relations assistance',
                  })}
                </li>
                <li>
                  {t('marketing_support.services.opening.item3', {
                    fallback: 'Community outreach strategies',
                  })}
                </li>
                <li>
                  {t('marketing_support.services.opening.item4', {
                    fallback: 'Event marketing tips',
                  })}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <section className="marketing-process">
        <div className="container">
          <h2 className="section-title">
            {t('marketing_support.process.title', { fallback: 'Our Marketing Support Process' })}
          </h2>

          <div className="process-timeline">
            <div className="timeline-item">
              <div className="timeline-number">1</div>
              <div className="timeline-content">
                <h3>
                  {t('marketing_support.process.step1.title', { fallback: 'Initial Consultation' })}
                </h3>
                <p>
                  {t('marketing_support.process.step1.description', {
                    fallback:
                      'We begin with a detailed consultation to understand your business goals, target market, and unique selling points.',
                  })}
                </p>
              </div>
            </div>

            <div className="timeline-item">
              <div className="timeline-number">2</div>
              <div className="timeline-content">
                <h3>
                  {t('marketing_support.process.step2.title', { fallback: 'Strategy Development' })}
                </h3>
                <p>
                  {t('marketing_support.process.step2.description', {
                    fallback:
                      'Our marketing team develops a customized marketing strategy tailored to your specific needs and market.',
                  })}
                </p>
              </div>
            </div>

            <div className="timeline-item">
              <div className="timeline-number">3</div>
              <div className="timeline-content">
                <h3>
                  {t('marketing_support.process.step3.title', {
                    fallback: 'Pre-Opening Marketing',
                  })}
                </h3>
                <p>
                  {t('marketing_support.process.step3.description', {
                    fallback:
                      'We provide support for building anticipation and generating buzz before your playground opens.',
                  })}
                </p>
              </div>
            </div>

            <div className="timeline-item">
              <div className="timeline-number">4</div>
              <div className="timeline-content">
                <h3>{t('marketing_support.process.step4.title', { fallback: 'Grand Opening' })}</h3>
                <p>
                  {t('marketing_support.process.step4.description', {
                    fallback:
                      'Our team helps plan and execute a successful grand opening event to maximize initial impact.',
                  })}
                </p>
              </div>
            </div>

            <div className="timeline-item">
              <div className="timeline-number">5</div>
              <div className="timeline-content">
                <h3>
                  {t('marketing_support.process.step5.title', { fallback: 'Ongoing Support' })}
                </h3>
                <p>
                  {t('marketing_support.process.step5.description', {
                    fallback:
                      'We continue to provide marketing materials, seasonal promotion ideas, and business growth strategies.',
                  })}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="cta-section cta-particles">
        <div className="container">
          <div className="cta-content">
            <h2>准备好讨论您的全息定制解决方案？</h2>
            <p>今天就联系我们的团队，探索我们如何为您的需求创造完美的全息解决方案。</p>
            <Link
              href={`/${locale}/pages/contact-us`}
              className="btn-primary"
              data-text="立即联系我们"
            >
              {'立即联系我们'.split('').map((char, index) => (
                <i key={index}>{char === ' ' ? '\u00A0' : char}</i>
              ))}
            </Link>
          </div>
        </div>
      </section>
    </>
  );
}
