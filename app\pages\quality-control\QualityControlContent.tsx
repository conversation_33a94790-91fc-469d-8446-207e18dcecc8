'use client';

import Image from 'next/image';
import Link from 'next/link';
import { generatePlaceholderSVG } from '../../utils/imagePlaceholder';

export default function QualityControlContent() {
  return (
    <main className="main-content">
      <section className="page-header">
        <div className="container">
          <h1 className="page-title">Quality Control</h1>
          <div className="breadcrumbs">
            <Link href="/">Home</Link> &gt; <Link href="/service">Service</Link> &gt;{' '}
            <span>Quality Control</span>
          </div>
        </div>
      </section>

      <section className="quality-intro">
        <div className="container">
          <div className="content-grid">
            <div className="content-image">
              <Image
                src={generatePlaceholderSVG(600, 400, 'Quality Control')}
                alt="Quality Control at Infinity Playground"
                width={600}
                height={400}
              />
            </div>
            <div className="content-text">
              <h2>Our Commitment to Quality</h2>
              <p>
                At Infinity Playground Park, quality is at the heart of everything we do. We
                understand that playground equipment must be manufactured to the highest standards
                to ensure safety, durability, and enjoyment.
              </p>
              <p>
                Our comprehensive quality control system covers every stage of production, from raw
                material selection to final product inspection.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="quality-process">
        <div className="container">
          <h2 className="section-title">Our Quality Control Process</h2>

          <div className="process-grid">
            <div className="process-item">
              <div className="process-icon">
                <i className="fas fa-microscope"></i>
              </div>
              <h3>Raw Material Inspection</h3>
              <p>
                Every material that enters our factory undergoes rigorous testing to ensure it meets
                our quality specifications. We test for composition, strength, and durability.
              </p>
            </div>

            <div className="process-item">
              <div className="process-icon">
                <i className="fas fa-cogs"></i>
              </div>
              <h3>Production Monitoring</h3>
              <p>
                Our quality control team monitors the production process at every stage, conducting
                regular checks and ensuring compliance with design specifications.
              </p>
            </div>

            <div className="process-item">
              <div className="process-icon">
                <i className="fas fa-clipboard-check"></i>
              </div>
              <h3>Pre-Shipment Inspection</h3>
              <p>
                Before any product leaves our facility, it undergoes comprehensive testing and
                inspection to ensure it meets all safety and quality standards.
              </p>
            </div>

            <div className="process-item">
              <div className="process-icon">
                <i className="fas fa-certificate"></i>
              </div>
              <h3>Certification</h3>
              <p>
                Our products are certified by international safety standards, including ASTM, EN,
                CE, TÜV, and more.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="quality-standards">
        <div className="container">
          <h2 className="section-title">International Quality Standards</h2>
          <div className="standards-content">
            <p>
              Infinity Playground Park adheres to the most stringent international quality and
              safety standards. Our products are regularly tested by independent laboratories to
              ensure compliance with:
            </p>

            <ul className="standards-list">
              <li>
                <strong>ASTM F1487:</strong> Standard Consumer Safety Performance Specification for
                Playground Equipment
              </li>
              <li>
                <strong>EN 1176:</strong> European Safety Standard for Playground Equipment
              </li>
              <li>
                <strong>ISO 9001:</strong> Quality Management System
              </li>
              <li>
                <strong>CE:</strong> European Conformity Certification
              </li>
              <li>
                <strong>TÜV:</strong> German Technical Inspection Association Certification
              </li>
            </ul>

            <div className="standards-image">
              <Image
                src={generatePlaceholderSVG(800, 300, 'Quality Certifications')}
                alt="Quality Certification Logos"
                width={800}
                height={300}
              />
            </div>
          </div>
        </div>
      </section>

      <section className="quality-team">
        <div className="container">
          <h2 className="section-title">Our Quality Control Team</h2>
          <p className="team-description">
            Our dedicated quality control team consists of experienced engineers and inspectors who
            are trained to detect even the smallest imperfections. They ensure that every product
            leaving our factory meets the highest standards of quality and safety.
          </p>

          <div className="team-stats">
            <div className="stat-item">
              <div className="stat-number">20+</div>
              <div className="stat-label">Quality Control Specialists</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">100%</div>
              <div className="stat-label">Product Inspection Rate</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">99.8%</div>
              <div className="stat-label">Customer Satisfaction</div>
            </div>
          </div>
        </div>
      </section>

      <section className="cta-section cta-particles">
        <div className="container">
          <div className="cta-content">
            <h2>准备好讨论您的全息定制解决方案？</h2>
            <p>今天就联系我们的团队，探索我们如何为您的需求创造完美的全息解决方案。</p>
            <Link
              href="/contact-us"
              className="btn-primary"
              data-text="立即联系我们"
            >
              {'立即联系我们'.split('').map((char, index) => (
                <i key={index}>{char === ' ' ? '\u00A0' : char}</i>
              ))}
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
