/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/tailwind.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
.container {
  width: 100%;
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.visible {
  visibility: visible;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.inset-0 {
  inset: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.-bottom-1 {
  bottom: -0.25rem;
}
.-bottom-4 {
  bottom: -1rem;
}
.-left-4 {
  left: -1rem;
}
.-right-2 {
  right: -0.5rem;
}
.-right-4 {
  right: -1rem;
}
.-top-2 {
  top: -0.5rem;
}
.-top-4 {
  top: -1rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-1 {
  bottom: 0.25rem;
}
.bottom-3 {
  bottom: 0.75rem;
}
.bottom-4 {
  bottom: 1rem;
}
.bottom-8 {
  bottom: 2rem;
}
.left-0 {
  left: 0px;
}
.left-1\/2 {
  left: 50%;
}
.left-1\/3 {
  left: 33.333333%;
}
.left-2 {
  left: 0.5rem;
}
.left-3 {
  left: 0.75rem;
}
.left-4 {
  left: 1rem;
}
.right-0 {
  right: 0px;
}
.right-1 {
  right: 0.25rem;
}
.right-1\/3 {
  right: 33.333333%;
}
.right-16 {
  right: 4rem;
}
.right-2 {
  right: 0.5rem;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.right-8 {
  right: 2rem;
}
.top-0 {
  top: 0px;
}
.top-1 {
  top: 0.25rem;
}
.top-1\/2 {
  top: 50%;
}
.top-20 {
  top: 5rem;
}
.top-3 {
  top: 0.75rem;
}
.top-4 {
  top: 1rem;
}
.top-8 {
  top: 2rem;
}
.top-full {
  top: 100%;
}
.isolate {
  isolation: isolate;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[100\] {
  z-index: 100;
}
.order-1 {
  order: 1;
}
.order-2 {
  order: 2;
}
.col-span-full {
  grid-column: 1 / -1;
}
.-mx-2 {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.-mb-16 {
  margin-bottom: -4rem;
}
.-ml-1 {
  margin-left: -0.25rem;
}
.-mr-16 {
  margin-right: -4rem;
}
.mb-0 {
  margin-bottom: 0px;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-4 {
  margin-left: 1rem;
}
.ml-5 {
  margin-left: 1.25rem;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mt-0 {
  margin-top: 0px;
}
.mt-0\.5 {
  margin-top: 0.125rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-12 {
  margin-top: 3rem;
}
.mt-16 {
  margin-top: 4rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-20 {
  margin-top: 5rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.contents {
  display: contents;
}
.hidden {
  display: none;
}
.aspect-\[16\/9\] {
  aspect-ratio: 16/9;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.aspect-video {
  aspect-ratio: 16 / 9;
}
.h-0\.5 {
  height: 0.125rem;
}
.h-1 {
  height: 0.25rem;
}
.h-10 {
  height: 2.5rem;
}
.h-11 {
  height: 2.75rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-20 {
  height: 5rem;
}
.h-24 {
  height: 6rem;
}
.h-3 {
  height: 0.75rem;
}
.h-32 {
  height: 8rem;
}
.h-4 {
  height: 1rem;
}
.h-40 {
  height: 10rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-64 {
  height: 16rem;
}
.h-72 {
  height: 18rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-9 {
  height: 2.25rem;
}
.h-96 {
  height: 24rem;
}
.h-\[240px\] {
  height: 240px;
}
.h-\[300px\] {
  height: 300px;
}
.h-\[400px\] {
  height: 400px;
}
.h-\[420px\] {
  height: 420px;
}
.h-\[500px\] {
  height: 500px;
}
.h-\[600px\] {
  height: 600px;
}
.h-\[80vh\] {
  height: 80vh;
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.max-h-40 {
  max-height: 10rem;
}
.max-h-\[80vh\] {
  max-height: 80vh;
}
.min-h-\[240px\] {
  min-height: 240px;
}
.min-h-screen {
  min-height: 100vh;
}
.w-0 {
  width: 0px;
}
.w-1\/3 {
  width: 33.333333%;
}
.w-1\/4 {
  width: 25%;
}
.w-1\/5 {
  width: 20%;
}
.w-10 {
  width: 2.5rem;
}
.w-11\/12 {
  width: 91.666667%;
}
.w-12 {
  width: 3rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\/3 {
  width: 66.666667%;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-28 {
  width: 7rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\/4 {
  width: 75%;
}
.w-32 {
  width: 8rem;
}
.w-4 {
  width: 1rem;
}
.w-4\/5 {
  width: 80%;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-64 {
  width: 16rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-full {
  width: 100%;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-\[200px\] {
  min-width: 200px;
}
.min-w-full {
  min-width: 100%;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-5xl {
  max-width: 64rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-\[600px\] {
  max-width: 600px;
}
.max-w-\[90\%\] {
  max-width: 90%;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-none {
  max-width: none;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-shrink {
  flex-shrink: 1;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.origin-top {
  transform-origin: top;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-12 {
  --tw-translate-x: -3rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-16 {
  --tw-translate-y: -4rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-16 {
  --tw-translate-x: 4rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-1 {
  --tw-translate-y: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-12 {
  --tw-translate-y: 3rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-3 {
  --tw-rotate: 3deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-90 {
  --tw-scale-x: .9;
  --tw-scale-y: .9;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-\[1\.03\] {
  --tw-scale-x: 1.03;
  --tw-scale-y: 1.03;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-x-0 {
  --tw-scale-x: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes fadeIn {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}
@keyframes ping {

  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-grab {
  cursor: grab;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize {
  resize: both;
}
.list-inside {
  list-style-position: inside;
}
.list-disc {
  list-style-type: disc;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-0 {
  gap: 0px;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-10 {
  gap: 2.5rem;
}
.gap-12 {
  gap: 3rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-5 {
  gap: 1.25rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.-space-x-px > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-1px * var(--tw-space-x-reverse));
  margin-left: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
}
.-space-y-px > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(-1px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(-1px * var(--tw-space-y-reverse));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0px * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}
.self-center {
  align-self: center;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-normal {
  white-space: normal;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.break-words {
  overflow-wrap: break-word;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-3xl {
  border-radius: 1.5rem;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-none {
  border-radius: 0px;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-b-2xl {
  border-bottom-right-radius: 1rem;
  border-bottom-left-radius: 1rem;
}
.rounded-b-md {
  border-bottom-right-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}
.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}
.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}
.rounded-t-md {
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-2 {
  border-width: 2px;
}
.border-\[2px\] {
  border-width: 2px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-t {
  border-top-width: 1px;
}
.border-t-2 {
  border-top-width: 2px;
}
.border-dashed {
  border-style: dashed;
}
.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));
}
.border-blue-100 {
  --tw-border-opacity: 1;
  border-color: rgb(219 234 254 / var(--tw-border-opacity, 1));
}
.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-100\/50 {
  border-color: rgb(243 244 246 / 0.5);
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-700 {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.border-gray-900 {
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}
.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}
.border-green-400 {
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}
.border-orange-500 {
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.border-red-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-300 {
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}
.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-transparent {
  border-color: transparent;
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}
.border-white\/30 {
  border-color: rgb(255 255 255 / 0.3);
}
.border-white\/80 {
  border-color: rgb(255 255 255 / 0.8);
}
.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/20 {
  background-color: rgb(0 0 0 / 0.2);
}
.bg-black\/30 {
  background-color: rgb(0 0 0 / 0.3);
}
.bg-black\/40 {
  background-color: rgb(0 0 0 / 0.4);
}
.bg-black\/90 {
  background-color: rgb(0 0 0 / 0.9);
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-200\/20 {
  background-color: rgb(191 219 254 / 0.2);
}
.bg-blue-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));
}
.bg-blue-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.bg-indigo-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}
.bg-indigo-200\/20 {
  background-color: rgb(199 210 254 / 0.2);
}
.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}
.bg-neutral-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}
.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}
.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}
.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-sky-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity, 1));
}
.bg-transparent {
  background-color: transparent;
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/15 {
  background-color: rgb(255 255 255 / 0.15);
}
.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}
.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}
.bg-white\/90 {
  background-color: rgb(255 255 255 / 0.9);
}
.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-yellow-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}
.bg-opacity-0 {
  --tw-bg-opacity: 0;
}
.bg-opacity-10 {
  --tw-bg-opacity: 0.1;
}
.bg-opacity-20 {
  --tw-bg-opacity: 0.2;
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.bg-gradient-to-tl {
  background-image: linear-gradient(to top left, var(--tw-gradient-stops));
}
.bg-gradient-to-tr {
  background-image: linear-gradient(to top right, var(--tw-gradient-stops));
}
.from-black\/50 {
  --tw-gradient-from: rgb(0 0 0 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/70 {
  --tw-gradient-from: rgb(0 0 0 / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/80 {
  --tw-gradient-from: rgb(0 0 0 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-100 {
  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-100\/30 {
  --tw-gradient-from: rgb(219 234 254 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-400 {
  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-50 {
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500\/20 {
  --tw-gradient-from: rgb(59 130 246 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-600 {
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-500 {
  --tw-gradient-from: #06b6d4 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-50 {
  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500 {
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-500 {
  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-100 {
  --tw-gradient-from: #fce7f3 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(252 231 243 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500 {
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-50 {
  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white {
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-black\/40 {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-black\/50 {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-blue-300 {
  --tw-gradient-to: rgb(147 197 253 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #93c5fd var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-blue-50\/40 {
  --tw-gradient-to: rgb(239 246 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(239 246 255 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-gray-300 {
  --tw-gradient-to: rgb(209 213 219 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #d1d5db var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-transparent {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-blue-100 {
  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);
}
.to-blue-50 {
  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);
}
.to-blue-500 {
  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);
}
.to-blue-600 {
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}
.to-blue-700 {
  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);
}
.to-gray-100 {
  --tw-gradient-to: #f3f4f6 var(--tw-gradient-to-position);
}
.to-gray-50 {
  --tw-gradient-to: #f9fafb var(--tw-gradient-to-position);
}
.to-indigo-100 {
  --tw-gradient-to: #e0e7ff var(--tw-gradient-to-position);
}
.to-indigo-100\/30 {
  --tw-gradient-to: rgb(224 231 255 / 0.3) var(--tw-gradient-to-position);
}
.to-indigo-50 {
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}
.to-indigo-50\/40 {
  --tw-gradient-to: rgb(238 242 255 / 0.4) var(--tw-gradient-to-position);
}
.to-pink-500 {
  --tw-gradient-to: #ec4899 var(--tw-gradient-to-position);
}
.to-purple-500 {
  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);
}
.to-purple-500\/20 {
  --tw-gradient-to: rgb(168 85 247 / 0.2) var(--tw-gradient-to-position);
}
.to-purple-600 {
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.to-red-500 {
  --tw-gradient-to: #ef4444 var(--tw-gradient-to-position);
}
.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.bg-cover {
  background-size: cover;
}
.bg-center {
  background-position: center;
}
.fill-current {
  fill: currentColor;
}
.fill-gray-900 {
  fill: #111827;
}
.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.object-center {
  -o-object-position: center;
     object-position: center;
}
.p-1 {
  padding: 0.25rem;
}
.p-12 {
  padding: 3rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-16 {
  padding-bottom: 4rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pl-6 {
  padding-left: 1.5rem;
}
.pr-12 {
  padding-right: 3rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-1 {
  padding-top: 0.25rem;
}
.pt-4 {
  padding-top: 1rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.font-sans {
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-light {
  font-weight: 300;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.italic {
  font-style: italic;
}
.leading-5 {
  line-height: 1.25rem;
}
.leading-6 {
  line-height: 1.5rem;
}
.leading-none {
  line-height: 1;
}
.leading-relaxed {
  line-height: 1.625;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-wide {
  letter-spacing: 0.025em;
}
.tracking-wider {
  letter-spacing: 0.05em;
}
.text-blue-100 {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.text-blue-200 {
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}
.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-green-900 {
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}
.text-indigo-600 {
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}
.text-indigo-800 {
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}
.text-neutral-400 {
  --tw-text-opacity: 1;
  color: rgb(163 163 163 / var(--tw-text-opacity, 1));
}
.text-neutral-500 {
  --tw-text-opacity: 1;
  color: rgb(115 115 115 / var(--tw-text-opacity, 1));
}
.text-neutral-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}
.text-neutral-900 {
  --tw-text-opacity: 1;
  color: rgb(23 23 23 / var(--tw-text-opacity, 1));
}
.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}
.text-purple-700 {
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.text-purple-900 {
  --tw-text-opacity: 1;
  color: rgb(88 28 135 / var(--tw-text-opacity, 1));
}
.text-red-400 {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}
.text-white\/90 {
  color: rgb(255 255 255 / 0.9);
}
.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.text-yellow-900 {
  --tw-text-opacity: 1;
  color: rgb(113 63 18 / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.underline-offset-4 {
  text-underline-offset: 4px;
}
.placeholder-gray-400::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-500::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-500::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.opacity-0 {
  opacity: 0;
}
.opacity-10 {
  opacity: 0.1;
}
.opacity-100 {
  opacity: 1;
}
.opacity-20 {
  opacity: 0.2;
}
.opacity-25 {
  opacity: 0.25;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-80 {
  opacity: 0.8;
}
.opacity-90 {
  opacity: 0.9;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-inset {
  --tw-ring-inset: inset;
}
.ring-blue-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(191 219 254 / var(--tw-ring-opacity, 1));
}
.ring-blue-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity, 1));
}
.ring-blue-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}
.ring-gray-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity, 1));
}
.ring-gray-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity, 1));
}
.ring-orange-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 215 170 / var(--tw-ring-opacity, 1));
}
.ring-white {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-xl {
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-filter {
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.delay-100 {
  transition-delay: 100ms;
}
.delay-200 {
  transition-delay: 200ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.duration-700 {
  transition-duration: 700ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.\[contain\:layout_paint_size\] {
  contain: layout paint size;
}
.hover\:-translate-y-0\.5:hover {
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:-translate-y-1:hover {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:-translate-y-2:hover {
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:border-blue-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.hover\:border-gray-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.hover\:border-gray-400:hover {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.hover\:border-gray-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}
.hover\:border-white\/30:hover {
  border-color: rgb(255 255 255 / 0.3);
}
.hover\:bg-black\/50:hover {
  background-color: rgb(0 0 0 / 0.5);
}
.hover\:bg-black\/60:hover {
  background-color: rgb(0 0 0 / 0.6);
}
.hover\:bg-blue-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-400:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}
.hover\:bg-indigo-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));
}
.hover\:bg-orange-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}
.hover\:bg-purple-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.hover\:bg-purple-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}
.hover\:bg-red-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}
.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}
.hover\:bg-transparent:hover {
  background-color: transparent;
}
.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-white\/30:hover {
  background-color: rgb(255 255 255 / 0.3);
}
.hover\:bg-yellow-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(254 240 138 / var(--tw-bg-opacity, 1));
}
.hover\:bg-yellow-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));
}
.hover\:bg-opacity-10:hover {
  --tw-bg-opacity: 0.1;
}
.hover\:from-blue-700:hover {
  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:to-purple-700:hover {
  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);
}
.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-900:hover {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-200:hover {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.hover\:text-green-900:hover {
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}
.hover\:text-indigo-500:hover {
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}
.hover\:text-indigo-900:hover {
  --tw-text-opacity: 1;
  color: rgb(49 46 129 / var(--tw-text-opacity, 1));
}
.hover\:text-red-800:hover {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.hover\:text-red-900:hover {
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}
.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.hover\:underline:hover {
  text-decoration-line: underline;
}
.hover\:opacity-100:hover {
  opacity: 1;
}
.hover\:shadow-2xl:hover {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:z-10:focus {
  z-index: 10;
}
.focus\:z-20:focus {
  z-index: 20;
}
.focus\:border-indigo-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:outline-offset-0:focus {
  outline-offset: 0px;
}
.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-blue-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}
.focus\:ring-indigo-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}
.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}
.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus-visible\:outline:focus-visible {
  outline-style: solid;
}
.focus-visible\:outline-2:focus-visible {
  outline-width: 2px;
}
.focus-visible\:outline-offset-2:focus-visible {
  outline-offset: 2px;
}
.focus-visible\:outline-blue-600:focus-visible {
  outline-color: #2563eb;
}
.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}
.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}
.disabled\:bg-blue-300:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));
}
.disabled\:bg-blue-400:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}
.disabled\:opacity-30:disabled {
  opacity: 0.3;
}
.disabled\:opacity-50:disabled {
  opacity: 0.5;
}
.group:hover .group-hover\:-translate-y-1 {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:-translate-y-2 {
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:translate-x-2 {
  --tw-translate-x: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:rotate-6 {
  --tw-rotate: 6deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-150 {
  --tw-scale-x: 1.5;
  --tw-scale-y: 1.5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-x-100 {
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:border-blue-200\/30 {
  border-color: rgb(191 219 254 / 0.3);
}
.group:hover .group-hover\:border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.group:hover .group-hover\:bg-blue-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.group:hover .group-hover\:bg-opacity-80 {
  --tw-bg-opacity: 0.8;
}
.group:hover .group-hover\:text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-neutral-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 82 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-white\/90 {
  color: rgb(255 255 255 / 0.9);
}
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}
.group:hover .group-hover\:opacity-20 {
  opacity: 0.2;
}
.group:hover .group-hover\:opacity-30 {
  opacity: 0.3;
}
.group:hover .group-hover\:opacity-40 {
  opacity: 0.4;
}
.group:hover .group-hover\:shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:hover .group-hover\:shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
@media (min-width: 640px) {

  .sm\:ml-6 {
    margin-left: 1.5rem;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:flex-1 {
    flex: 1 1 0%;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }

  .sm\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}
@media (min-width: 768px) {

  .md\:order-1 {
    order: 1;
  }

  .md\:order-2 {
    order: 2;
  }

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:aspect-\[4\/3\] {
    aspect-ratio: 4/3;
  }

  .md\:aspect-square {
    aspect-ratio: 1 / 1;
  }

  .md\:h-\[320px\] {
    height: 320px;
  }

  .md\:h-\[400px\] {
    height: 400px;
  }

  .md\:h-\[500px\] {
    height: 500px;
  }

  .md\:h-\[520px\] {
    height: 520px;
  }

  .md\:h-auto {
    height: auto;
  }

  .md\:w-20 {
    width: 5rem;
  }

  .md\:w-3\/4 {
    width: 75%;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:flex-1 {
    flex: 1 1 0%;
  }

  .md\:flex-shrink-0 {
    flex-shrink: 0;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-col {
    flex-direction: column;
  }

  .md\:items-end {
    align-items: flex-end;
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .md\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .md\:pb-0 {
    padding-bottom: 0px;
  }

  .md\:pr-2 {
    padding-right: 0.5rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}
@media (min-width: 1024px) {

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:gap-12 {
    gap: 3rem;
  }

  .lg\:gap-16 {
    gap: 4rem;
  }

  .lg\:p-8 {
    padding: 2rem;
  }

  .lg\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .lg\:pl-8 {
    padding-left: 2rem;
  }

  .lg\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .lg\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }
}
@media (min-width: 1280px) {

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
@media (prefers-color-scheme: dark) {

  .dark\:bg-gray-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
  }

  .dark\:text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity, 1));
  }
} 
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/about-us.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/* Core Technologies Section Styles */
.core-technology {
  padding: 5rem 0;
  background-color: #f8f9fc;
  position: relative;
  overflow: hidden;
}

.core-technology::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 5% 15%, rgba(26, 26, 46, 0.03) 0%, transparent 30%),
    radial-gradient(circle at 95% 85%, rgba(26, 26, 46, 0.03) 0%, transparent 30%);
  z-index: 1;
}

.core-technology .container {
  position: relative;
  z-index: 2;
}

.technology-highlights {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2.5rem;
  margin-bottom: 4rem;
}

.tech-item {
  background: white;
  border-radius: 16px;
  padding: 2.5rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  box-shadow: 0 10px 30px rgba(26, 26, 46, 0.05);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  overflow: hidden;
}

.tech-item::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary-color), rgba(26, 26, 46, 0.5));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.4s ease;
}

.tech-item:hover::after {
  transform: scaleX(1);
}

.tech-icon-wrapper {
  position: relative;
  margin-bottom: 2rem;
}

.tech-icon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(145deg, rgba(26, 26, 46, 0.9), rgba(26, 26, 46, 1));
  color: white;
  font-size: 2.5rem;
  border-radius: 16px;
  position: relative;
  z-index: 2;
  box-shadow: 0 8px 20px rgba(26, 26, 46, 0.2);
}

.tech-icon-bg {
  position: absolute;
  width: 80px;
  height: 80px;
  top: 12px;
  left: 12px;
  background-color: rgba(26, 26, 46, 0.1);
  border-radius: 16px;
  z-index: 1;
}

.tech-number {
  font-size: 1rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.8rem;
  background-color: rgba(26, 26, 46, 0.05);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  display: inline-block;
}

.tech-content h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.6rem;
  font-weight: 600;
  color: var(--text-dark);
}

.tech-divider {
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), rgba(26, 26, 46, 0.4));
  margin-bottom: 1.5rem;
  border-radius: 3px;
}

.tech-content p {
  margin: 0;
  color: var(--text-medium);
  line-height: 1.7;
  font-size: 1.1rem;
}

/* 响应式样式 */
@media (max-width: 992px) {
  .technology-highlights {
    grid-template-columns: 1fr;
  }
  
  .tech-item {
    padding: 2rem;
  }
  
  .tech-icon {
    width: 70px;
    height: 70px;
    font-size: 2rem;
  }
  
  .tech-icon-bg {
    width: 70px;
    height: 70px;
    top: 10px;
    left: 10px;
  }
} 
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/banner-fix.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/* 覆盖产品页banner样式，移除所有渐变效果，实现透明导航 */

.page-banner {
  background-size: cover !important; /* 使用cover而不是100% 100%，避免图片变形 */
  background-position: center top !important; /* 改为顶部对齐 */
  background-repeat: no-repeat !important;
  padding: 180px 0 120px !important; /* 增加上下内边距，创造更多空间 */
  color: white !important;
  position: relative !important;
  overflow: hidden !important;
  margin-bottom: 0 !important;
  margin-top: -150px !important; /* 进一步增加负边距，让横幅向上移动更多 */
  width: 100vw !important; /* 占据整个视口宽度 */
  max-width: 100vw !important;
  margin-left: calc(50% - 50vw) !important; /* 向左延伸到屏幕边缘 */
  margin-right: calc(50% - 50vw) !important; /* 向右延伸到屏幕边缘 */
  left: 0 !important;
  right: 0 !important;
  top: 0 !important; /* 确保从顶部开始 */
}

/* 移除所有渐变覆盖层 */
.page-banner:before {
  display: none !important;
}

.page-banner:after {
  display: none !important;
}

/* 确保文字容器位于正确位置 */
.page-banner .container {
  position: relative;
  z-index: 2;
  background-color: transparent !important; /* 确保容器背景透明 */
}

/* 页面标题样式 - 增强可见性 */
.page-title {
  font-size: 3rem !important;
  font-weight: 700 !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 0 0 10px rgba(0, 0, 0, 0.5) !important;
  color: white !important;
  letter-spacing: 1px !important;
  margin-bottom: 1rem !important;
}

/* 面包屑导航样式 - 透明背景 */
.breadcrumb {
  display: flex !important;
  align-items: center !important;
  background-color: transparent !important; /* 移除白色背景 */
  padding: 0.5rem 0 !important;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8), 0 0 5px rgba(0, 0, 0, 0.5) !important;
  font-weight: 500 !important;
  font-size: 1.1rem !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 面包屑导航链接样式 */
.breadcrumb a {
  color: white !important;
  text-decoration: none !important;
  transition: opacity 0.3s ease !important;
  position: relative !important;
}

.breadcrumb a:hover {
  opacity: 0.8 !important;
}

/* 面包屑分隔符样式 */
.breadcrumb .separator {
  margin: 0 0.5rem !important;
  color: rgba(255, 255, 255, 0.7) !important;
}

/* 修复页面间隙问题 */
body {
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden !important;
}

section {
  margin: 0 !important;
}

.products-page {
  margin-top: 40px !important; /* 增加顶部外边距，与导航栏分离 */
  padding-top: 60px !important; /* 增加顶部内边距 */
}
/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/modern-contrast-fix.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/**
 * 现代高对比度修复
 * 使用最新的forced-colors标准替代已弃用的-ms-high-contrast
 */

/* 高对比度模式激活时的通用样式 */
@media (forced-colors: active) {
  /* 确保按钮边框可见 */
  button, 
  [role="button"],
  .button,
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    border: 1px solid ButtonText;
    color: ButtonText;
    background-color: ButtonFace;
  }
  
  /* 确保链接可见 */
  a {
    color: LinkText;
    text-decoration: underline;
  }
  
  /* 确保图标可见 */
  svg, 
  img[role="presentation"] {
    forced-color-adjust: none;
  }
  
  /* 修复焦点轮廓 */
  :focus {
    outline: 2px solid CanvasText;
  }
  
  /* 修复表单元素 */
  input, textarea, select {
    border: 1px solid CanvasText;
    background-color: Canvas;
    color: CanvasText;
  }
  
  /* 修复复选框和单选按钮 */
  input[type="checkbox"],
  input[type="radio"] {
    border: 1px solid CanvasText;
    background-color: Canvas;
  }
  
  /* 修复选择下拉菜单 */
  select {
    background-color: Canvas;
    color: CanvasText;
    border: 1px solid CanvasText;
  }
}

/* 常见的-ms-high-contrast替代方案 */
@media (forced-colors: active) {
  /* 替代 forced-colors: active */
  .ms-high-contrast-active {
    /* 这里放置您的高对比度模式样式 */
  }
}

@media (forced-colors: none) {
  /* 替代 forced-colors: none */
  .ms-high-contrast-none {
    /* 这里放置您的非高对比度模式样式 */
  }
}

/* 阻止微软自动翻译器添加的高对比度样式 */
.ms-translator-high-contrast-styles {
  display: none !important;
}

/* 空规则覆盖旧的-ms-high-contrast媒体查询，不影响正常样式 */
@media (forced-colors: active),
       (forced-colors: black-on-white),
       (forced-colors: white-on-black),
       (forced-colors: none) {
  /* 空规则覆盖所有旧的-ms-high-contrast样式 */
} 
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/ms-fix.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/* 使用现代的Forced Colors Mode标准 */

/*
 * 完全移除-ms-high-contrast媒体查询
 * 使用现代的forced-colors媒体查询替代
 */

/* 使用现代的forced-colors媒体查询 */
@media (forced-colors: active) {
  /* 基本覆盖 */
  a:focus {
    outline: 2px solid ButtonText !important;
    outline-offset: 2px !important;
  }

  /* 按钮和交互元素 */
  button:focus,
  input:focus,
  textarea:focus,
  select:focus {
    outline: 2px solid ButtonText !important;
    outline-offset: 2px !important;
  }

  /* 确保链接在高对比度模式下可见 */
  a {
    color: LinkText !important;
  }

  /* 确保按钮在高对比度模式下可见 */
  button,
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    color: ButtonText !important;
    background-color: ButtonFace !important;
    border: 1px solid ButtonText !important;
  }

  /* 确保图标在高对比度模式下可见 */
  svg, img {
    forced-color-adjust: auto !important;
  }

  /* 确保表单元素在高对比度模式下可见 */
  input, textarea, select {
    border-color: ButtonText !important;
    background-color: Field !important;
    color: FieldText !important;
  }

  /* 确保复选框和单选按钮在高对比度模式下可见 */
  input[type="checkbox"],
  input[type="radio"] {
    color: ButtonText !important;
    border-color: ButtonText !important;
  }
}
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/high-contrast-override.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/**
 * 全局覆盖所有-ms-high-contrast媒体查询
 * 这个文件用于解决第三方库中使用-ms-high-contrast的问题
 */

/* 
 * 使用!important覆盖所有可能的-ms-high-contrast媒体查询
 * 这是一个更强力的解决方案，确保所有第三方库的-ms-high-contrast媒体查询都被覆盖
 */

/* 覆盖所有forced-colors: active媒体查询 */
@media (forced-colors: active) {
  * {
    /* 重置所有可能的样式 */
    background-color: initial !important;
    color: initial !important;
    border-color: initial !important;
    outline-color: initial !important;
    text-shadow: initial !important;
    box-shadow: initial !important;
  }
}

/* 覆盖所有forced-colors: black-on-white媒体查询 */
@media (forced-colors: black-on-white) {
  * {
    /* 重置所有可能的样式 */
    background-color: initial !important;
    color: initial !important;
    border-color: initial !important;
    outline-color: initial !important;
    text-shadow: initial !important;
    box-shadow: initial !important;
  }
}

/* 覆盖所有forced-colors: white-on-black媒体查询 */
@media (forced-colors: white-on-black) {
  * {
    /* 重置所有可能的样式 */
    background-color: initial !important;
    color: initial !important;
    border-color: initial !important;
    outline-color: initial !important;
    text-shadow: initial !important;
    box-shadow: initial !important;
  }
}

/* 使用现代的forced-colors媒体查询提供正确的高对比度样式 */
@media (forced-colors: active) {
  /* 基本元素样式 */
  body {
    color: CanvasText;
    background-color: Canvas;
  }
  
  /* 链接样式 */
  a {
    color: LinkText;
  }
  
  a:visited {
    color: VisitedText;
  }
  
  /* 按钮样式 */
  button, 
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    color: ButtonText;
    background-color: ButtonFace;
    border: 1px solid ButtonText;
  }
  
  /* 输入框样式 */
  input, 
  textarea, 
  select {
    color: FieldText;
    background-color: Field;
    border: 1px solid FieldText;
  }
  
  /* 图标和图片 */
  svg, 
  img {
    forced-color-adjust: auto;
  }
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/ms-high-contrast-blocker.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/**
 * Complete blocker for -ms-high-contrast media queries
 * This file uses a CSS hack to completely prevent any -ms-high-contrast media queries from being processed
 */

/* 
 * This technique uses a CSS hack to completely disable -ms-high-contrast media queries
 * by creating a higher-specificity rule that overrides all possible -ms-high-contrast styles
 */

/* First, create a dummy element with extremely high specificity */
html body div#ms-high-contrast-blocker {
  display: none !important;
}

/* Then use attribute selectors to create even higher specificity */
html[lang] body[class] div[id="ms-high-contrast-blocker"] {
  display: none !important;
}

/* Now override all -ms-high-contrast media queries with empty rules */
@media (forced-colors: active), 
       (forced-colors: black-on-white), 
       (forced-colors: white-on-black),
       (forced-colors: none) {
  
  /* Create an extremely high-specificity selector that will override all other rules */
  html[lang] body[class] *,
  html[lang] body[class] *::before,
  html[lang] body[class] *::after {
    /* 不再重置所有属性，允许正常深色模式工作 */
    /* 仅处理高对比度相关属性 */
    forced-color-adjust: none !important;
  }
}

/* Add the modern forced-colors media query for proper high contrast support */
@media (forced-colors: active) {
  /* Basic elements */
  body {
    color: CanvasText;
    background-color: Canvas;
  }
  
  /* Links */
  a {
    color: LinkText;
  }
  
  a:visited {
    color: VisitedText;
  }
  
  /* Buttons */
  button, 
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    color: ButtonText;
    background-color: ButtonFace;
    border: 1px solid ButtonText;
  }
  
  /* Form elements */
  input, 
  textarea, 
  select {
    color: FieldText;
    background-color: Field;
    border: 1px solid FieldText;
  }
  
  /* Images and SVGs */
  img, 
  svg {
    forced-color-adjust: auto;
  }
}

/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/animations.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/* 页面元素渐入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDelay {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  50% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加动画类 */
.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-fade-in-delay {
  animation: fadeInDelay 1.2s ease-out forwards;
}

/* 背景图缩放动画 */
@keyframes scaleBackground {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.05);
  }
}

.bg-scale {
  animation: scaleBackground 10s ease-in-out infinite alternate;
}

/* 产品页面专用动画 */
@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 产品页面动画类 */
.animate-fade-in-right {
  animation: fadeInRight 1s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 1s ease-out forwards;
}

.animate-fade-in-up {
  animation: fadeInUp 1s ease-out forwards;
}

/* 背景放大效果 */
.bg-zoom {
  transition: transform 8s ease;
}

/* 移除背景悬停放大效果 */

/* 渐变覆盖层动画 */
@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-overlay {
  background: linear-gradient(270deg, rgba(0,0,0,0.7), rgba(0,0,0,0.4), rgba(0,0,0,0.7));
  background-size: 200% 200%;
  animation: gradientMove 10s ease infinite;
}

/* 粒子动画 */
@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-40px) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-20px) rotate(270deg);
    opacity: 0.8;
  }
}

/* 脉冲发光动画 */
@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

/* 悬停提升动画 */
@keyframes hoverLift {
  from {
    transform: translateY(0) scale(1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }
  to {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }
}

/* 缩放进入动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 弹跳动画 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0,-4px,0);
  }
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 摇摆动画 */
@keyframes swing {
  20% {
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    transform: rotate3d(0, 0, 1, -5deg);
  }
  100% {
    transform: rotate3d(0, 0, 1, 0deg);
  }
}

/* 动画工具类 */
.animate-scale-in {
  animation: scaleIn 0.6s ease-out;
}

.animate-bounce-custom {
  animation: bounce 1s ease-in-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-swing {
  animation: swing 1s ease-in-out;
}

.animate-pulse-glow {
  animation: pulseGlow 3s ease-in-out infinite;
}

/* 悬停效果类 */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

/* 延迟动画 */
.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

.animate-delay-500 {
  animation-delay: 0.5s;
}

/* 滚动触发动画 */
.scroll-animate {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-animate.in-view {
  opacity: 1;
  transform: translateY(0);
}

/* 交错动画 */
.stagger-children > * {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.stagger-children.animate > *:nth-child(1) { transition-delay: 0.1s; }
.stagger-children.animate > *:nth-child(2) { transition-delay: 0.2s; }
.stagger-children.animate > *:nth-child(3) { transition-delay: 0.3s; }
.stagger-children.animate > *:nth-child(4) { transition-delay: 0.4s; }
.stagger-children.animate > *:nth-child(5) { transition-delay: 0.5s; }
.stagger-children.animate > *:nth-child(6) { transition-delay: 0.6s; }

.stagger-children.animate > * {
  opacity: 1;
  transform: translateY(0);
}

/* 性能优化 */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 响应式动画 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/unified-cta.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/* 统一CTA区域样式 */

/* 基础CTA区域样式 - 统一所有CTA区域 */
.cta-section,
.contact-cta,
.premium-cta {
  background-color: var(--primary-color, #0a59f7) !important;
  color: white !important;
  padding: 5rem 0 !important;
  text-align: center !important;
  position: relative !important;
  overflow: hidden !important;
}

/* CTA粒子背景效果 - 统一应用到所有CTA区域 */
.cta-section::before,
.contact-cta::before,
.premium-cta::before,
.cta-particles {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.12) 0%, transparent 25%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.12) 0%, transparent 25%) !important;
  z-index: 0 !important;
  pointer-events: none !important;
}

/* 确保内容在粒子效果之上 */
.cta-section .container,
.contact-cta .container,
.premium-cta .container {
  position: relative !important;
  z-index: 1 !important;
}

/* CTA内容区域 */
.cta-section .cta-content,
.contact-cta .cta-content,
.premium-cta .cta-content {
  max-width: 700px !important;
  margin: 0 auto !important;
  text-align: center !important;
  position: relative !important;
  z-index: 1 !important;
}

/* CTA标题样式 */
.cta-section h2,
.contact-cta h2,
.premium-cta h2,
.cta-section .cta-content h2,
.contact-cta .cta-content h2,
.premium-cta .cta-content h2 {
  font-size: 2.5rem !important;
  font-weight: 600 !important;
  color: white !important;
  margin-bottom: 1.2rem !important;
  position: relative !important;
  z-index: 1 !important;
  text-shadow: none !important;
}

/* CTA描述文字样式 */
.cta-section p,
.contact-cta p,
.premium-cta p,
.cta-section .cta-content p,
.contact-cta .cta-content p,
.premium-cta .cta-content p {
  font-size: 1.2rem !important;
  line-height: 1.6 !important;
  color: rgba(255, 255, 255, 0.9) !important;
  margin-bottom: 2.5rem !important;
  max-width: 700px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  position: relative !important;
  z-index: 1 !important;
}

/* CTA按钮样式 - 动画按钮效果 - 最高优先级 */
.btn-primary[data-text],
.cta-section .btn-primary,
.contact-cta .btn-primary,
.premium-cta .btn-primary,
.cta-section .btn-glow,
.contact-cta .btn-glow,
.premium-cta .btn-glow,
a.btn-primary[data-text] {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 50px !important;
  position: relative !important;
  padding: 0 20px !important;
  font-size: 18px !important;
  text-transform: uppercase !important;
  border: 0 !important;
  box-shadow: hsl(210deg 87% 36%) 0px 7px 0px 0px !important;
  background-color: hsl(210deg 100% 44%) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  transition: 31ms cubic-bezier(.5, .7, .4, 1) !important;
  text-decoration: none !important;
  color: white !important;
  font-weight: bold !important;
  letter-spacing: 4px !important;
  z-index: 1 !important;
  cursor: pointer !important;
  margin: 0 auto !important;
  width: auto !important;
  min-width: 200px !important;
}

/* 按钮默认文字 - 最高优先级 */
.btn-primary[data-text]:before,
.cta-section .btn-primary:before,
.contact-cta .btn-primary:before,
.premium-cta .btn-primary:before,
.cta-section .btn-glow:before,
.contact-cta .btn-glow:before,
.premium-cta .btn-glow:before,
a.btn-primary[data-text]:before {
  content: attr(data-text) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: absolute !important;
  inset: 0 !important;
  font-size: 15px !important;
  font-weight: bold !important;
  color: white !important;
  letter-spacing: 2px !important;
  opacity: 1 !important;
  transition: all 0.3s ease !important;
  transform: translateY(0) !important; /* 默认位置 */
  z-index: 2 !important; /* 确保在字母之上 */
}

/* 按钮点击效果 - 最高优先级 */
.btn-primary[data-text]:active,
.cta-section .btn-primary:active,
.contact-cta .btn-primary:active,
.premium-cta .btn-primary:active,
.cta-section .btn-glow:active,
.contact-cta .btn-glow:active,
.premium-cta .btn-glow:active,
a.btn-primary[data-text]:active {
  box-shadow: none !important;
  transform: translateY(7px) !important;
  transition: 35ms cubic-bezier(.5, .7, .4, 1) !important;
}

/* 悬停时隐藏默认文字 - 最高优先级 */
.btn-primary[data-text]:hover:before,
.cta-section .btn-primary:hover:before,
.contact-cta .btn-primary:hover:before,
.premium-cta .btn-primary:hover:before,
.cta-section .btn-glow:hover:before,
.contact-cta .btn-glow:hover:before,
.premium-cta .btn-glow:hover:before,
a.btn-primary[data-text]:hover:before {
  transition: all 0.2s ease !important;
  transform: translateY(-100%) !important; /* 向上滑出 */
  opacity: 0 !important;
}

/* 动画字母样式 - 默认隐藏 - 最高优先级 */
.btn-primary[data-text] i,
.cta-section .btn-primary i,
.contact-cta .btn-primary i,
.premium-cta .btn-primary i,
.cta-section .btn-glow i,
.contact-cta .btn-glow i,
.premium-cta .btn-glow i,
a.btn-primary[data-text] i {
  color: white !important;
  font-size: 15px !important;
  font-weight: bold !important;
  letter-spacing: 0px !important; /* 默认无字母间距 */
  font-style: normal !important;
  transition: all 0.3s ease !important;
  transform: translateY(20px) !important; /* 从下方出现 */
  opacity: 0 !important; /* 默认完全隐藏 */
  display: inline-block !important;
  position: relative !important;
  z-index: 3 !important; /* 确保在默认文字之上 */
}

/* 悬停时显示动画字母 - 最高优先级 */
.btn-primary[data-text]:hover i,
.cta-section .btn-primary:hover i,
.contact-cta .btn-primary:hover i,
.premium-cta .btn-primary:hover i,
.cta-section .btn-glow:hover i,
.contact-cta .btn-glow:hover i,
.premium-cta .btn-glow:hover i,
a.btn-primary[data-text]:hover i {
  transition: all 0.3s ease !important;
  transform: translateY(0px) !important;
  opacity: 1 !important;
  letter-spacing: 2px !important; /* 悬停时增加字母间距 */
}

/* 字母动画延迟效果 - 最高优先级 */
.btn-primary[data-text]:hover i:nth-child(1),
.cta-section .btn-primary:hover i:nth-child(1),
.contact-cta .btn-primary:hover i:nth-child(1),
.premium-cta .btn-primary:hover i:nth-child(1),
.cta-section .btn-glow:hover i:nth-child(1),
.contact-cta .btn-glow:hover i:nth-child(1),
.premium-cta .btn-glow:hover i:nth-child(1),
a.btn-primary[data-text]:hover i:nth-child(1) {
  transition-delay: 0.045s !important;
}

.btn-primary[data-text]:hover i:nth-child(2),
.cta-section .btn-primary:hover i:nth-child(2),
.contact-cta .btn-primary:hover i:nth-child(2),
.premium-cta .btn-primary:hover i:nth-child(2),
.cta-section .btn-glow:hover i:nth-child(2),
.contact-cta .btn-glow:hover i:nth-child(2),
.premium-cta .btn-glow:hover i:nth-child(2),
a.btn-primary[data-text]:hover i:nth-child(2) {
  transition-delay: calc(0.045s * 2) !important;
}

.btn-primary[data-text]:hover i:nth-child(3),
.cta-section .btn-primary:hover i:nth-child(3),
.contact-cta .btn-primary:hover i:nth-child(3),
.premium-cta .btn-primary:hover i:nth-child(3),
.cta-section .btn-glow:hover i:nth-child(3),
.contact-cta .btn-glow:hover i:nth-child(3),
.premium-cta .btn-glow:hover i:nth-child(3),
a.btn-primary[data-text]:hover i:nth-child(3) {
  transition-delay: calc(0.045s * 3) !important;
}

.btn-primary[data-text]:hover i:nth-child(4),
.cta-section .btn-primary:hover i:nth-child(4),
.contact-cta .btn-primary:hover i:nth-child(4),
.premium-cta .btn-primary:hover i:nth-child(4),
.cta-section .btn-glow:hover i:nth-child(4),
.contact-cta .btn-glow:hover i:nth-child(4),
.premium-cta .btn-glow:hover i:nth-child(4),
a.btn-primary[data-text]:hover i:nth-child(4) {
  transition-delay: calc(0.045s * 4) !important;
}

.btn-primary[data-text]:hover i:nth-child(5),
.cta-section .btn-primary:hover i:nth-child(5),
.contact-cta .btn-primary:hover i:nth-child(5),
.premium-cta .btn-primary:hover i:nth-child(5),
.cta-section .btn-glow:hover i:nth-child(5),
.contact-cta .btn-glow:hover i:nth-child(5),
.premium-cta .btn-glow:hover i:nth-child(5),
a.btn-primary[data-text]:hover i:nth-child(5) {
  transition-delay: calc(0.045s * 5) !important;
}

.btn-primary[data-text]:hover i:nth-child(6),
.cta-section .btn-primary:hover i:nth-child(6),
.contact-cta .btn-primary:hover i:nth-child(6),
.premium-cta .btn-primary:hover i:nth-child(6),
.cta-section .btn-glow:hover i:nth-child(6),
.contact-cta .btn-glow:hover i:nth-child(6),
.premium-cta .btn-glow:hover i:nth-child(6),
a.btn-primary[data-text]:hover i:nth-child(6) {
  transition-delay: calc(0.045s * 6) !important;
}

.cta-section .btn-primary:hover i:nth-child(7),
.contact-cta .btn-primary:hover i:nth-child(7),
.premium-cta .btn-primary:hover i:nth-child(7),
.cta-section .btn-glow:hover i:nth-child(7),
.contact-cta .btn-glow:hover i:nth-child(7),
.premium-cta .btn-glow:hover i:nth-child(7) {
  transition-delay: calc(0.045s * 7) !important;
}

.cta-section .btn-primary:hover i:nth-child(8),
.contact-cta .btn-primary:hover i:nth-child(8),
.premium-cta .btn-primary:hover i:nth-child(8),
.cta-section .btn-glow:hover i:nth-child(8),
.contact-cta .btn-glow:hover i:nth-child(8),
.premium-cta .btn-glow:hover i:nth-child(8) {
  transition-delay: calc(0.045s * 8) !important;
}

.cta-section .btn-primary:hover i:nth-child(9),
.contact-cta .btn-primary:hover i:nth-child(9),
.premium-cta .btn-primary:hover i:nth-child(9),
.cta-section .btn-glow:hover i:nth-child(9),
.contact-cta .btn-glow:hover i:nth-child(9),
.premium-cta .btn-glow:hover i:nth-child(9) {
  transition-delay: calc(0.045s * 9) !important;
}

.cta-section .btn-primary:hover i:nth-child(10),
.contact-cta .btn-primary:hover i:nth-child(10),
.premium-cta .btn-primary:hover i:nth-child(10),
.cta-section .btn-glow:hover i:nth-child(10),
.contact-cta .btn-glow:hover i:nth-child(10),
.premium-cta .btn-glow:hover i:nth-child(10) {
  transition-delay: calc(0.045s * 10) !important;
}

.cta-section .btn-primary:hover i:nth-child(11),
.contact-cta .btn-primary:hover i:nth-child(11),
.premium-cta .btn-primary:hover i:nth-child(11),
.cta-section .btn-glow:hover i:nth-child(11),
.contact-cta .btn-glow:hover i:nth-child(11),
.premium-cta .btn-glow:hover i:nth-child(11) {
  transition-delay: calc(0.045s * 11) !important;
}

.cta-section .btn-primary:hover i:nth-child(12),
.contact-cta .btn-primary:hover i:nth-child(12),
.premium-cta .btn-primary:hover i:nth-child(12),
.cta-section .btn-glow:hover i:nth-child(12),
.contact-cta .btn-glow:hover i:nth-child(12),
.premium-cta .btn-glow:hover i:nth-child(12) {
  transition-delay: calc(0.045s * 12) !important;
}

.cta-section .btn-primary:hover i:nth-child(13),
.contact-cta .btn-primary:hover i:nth-child(13),
.premium-cta .btn-primary:hover i:nth-child(13),
.cta-section .btn-glow:hover i:nth-child(13),
.contact-cta .btn-glow:hover i:nth-child(13),
.premium-cta .btn-glow:hover i:nth-child(13) {
  transition-delay: calc(0.045s * 13) !important;
}

.cta-section .btn-primary:hover i:nth-child(14),
.contact-cta .btn-primary:hover i:nth-child(14),
.premium-cta .btn-primary:hover i:nth-child(14),
.cta-section .btn-glow:hover i:nth-child(14),
.contact-cta .btn-glow:hover i:nth-child(14),
.premium-cta .btn-glow:hover i:nth-child(14) {
  transition-delay: calc(0.045s * 14) !important;
}

.cta-section .btn-primary:hover i:nth-child(15),
.contact-cta .btn-primary:hover i:nth-child(15),
.premium-cta .btn-primary:hover i:nth-child(15),
.cta-section .btn-glow:hover i:nth-child(15),
.contact-cta .btn-glow:hover i:nth-child(15),
.premium-cta .btn-glow:hover i:nth-child(15) {
  transition-delay: calc(0.045s * 15) !important;
}

/* 移除所有可能冲突的背景样式 */
.cta-section {
  background: var(--primary-color, #0a59f7) !important;
  background-color: var(--primary-color, #0a59f7) !important;
  background-image: none !important;
  background-gradient: none !important;
}

.contact-cta {
  background: var(--primary-color, #0a59f7) !important;
  background-color: var(--primary-color, #0a59f7) !important;
  background-image: none !important;
  background-gradient: none !important;
}

.premium-cta {
  background: var(--primary-color, #0a59f7) !important;
  background-color: var(--primary-color, #0a59f7) !important;
  background-image: none !important;
  background-gradient: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cta-section,
  .contact-cta,
  .premium-cta {
    padding: 3rem 0 !important;
  }

  .cta-section h2,
  .contact-cta h2,
  .premium-cta h2,
  .cta-section .cta-content h2,
  .contact-cta .cta-content h2,
  .premium-cta .cta-content h2 {
    font-size: 2rem !important;
  }

  .cta-section p,
  .contact-cta p,
  .premium-cta p,
  .cta-section .cta-content p,
  .contact-cta .cta-content p,
  .premium-cta .cta-content p {
    font-size: 1.1rem !important;
    margin-bottom: 2rem !important;
  }

  .cta-section .btn-primary,
  .contact-cta .btn-primary,
  .premium-cta .btn-primary,
  .cta-section .btn-glow,
  .contact-cta .btn-glow,
  .premium-cta .btn-glow {
    padding: 12px 24px !important;
    font-size: 0.9rem !important;
  }
}

/* 确保所有CTA区域都有粒子效果 */
.cta-section:not(.cta-particles):not([class*="particles"])::before,
.contact-cta:not(.cta-particles):not([class*="particles"])::before,
.premium-cta:not(.cta-particles):not([class*="particles"])::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.12) 0%, transparent 25%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.12) 0%, transparent 25%) !important;
  z-index: 0 !important;
  pointer-events: none !important;
}

/* 强制覆盖 - 确保动画按钮样式绝对优先 */
a[data-text].btn-primary,
a.btn-primary[data-text] {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 50px !important;
  position: relative !important;
  padding: 0 20px !important;
  font-size: 18px !important;
  text-transform: uppercase !important;
  border: 0 !important;
  box-shadow: hsl(210deg 87% 36%) 0px 7px 0px 0px !important;
  background-color: hsl(210deg 100% 44%) !important;
  border-radius: 12px !important;
  overflow: visible !important; /* 改为visible以防止内容被裁剪 */
  transition: 31ms cubic-bezier(.5, .7, .4, 1) !important;
  text-decoration: none !important;
  color: white !important;
  font-weight: bold !important;
  letter-spacing: 4px !important;
  z-index: 1 !important;
  cursor: pointer !important;
  min-width: -moz-fit-content !important;
  min-width: fit-content !important; /* 确保按钮宽度适应内容 */
  white-space: nowrap !important; /* 防止文字换行 */
  flex-wrap: nowrap !important; /* 防止flex项目换行 */
}

/* 覆盖Next.js生成的内联样式 - 针对关于我们页面 */
.contact-cta a[data-text].btn-primary,
.contact-cta a.btn-primary[data-text],
.about-us-page .contact-cta .btn-primary {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 50px !important;
  position: relative !important;
  padding: 0 20px !important;
  font-size: 18px !important;
  text-transform: uppercase !important;
  border: 0 !important;
  box-shadow: hsl(210deg 87% 36%) 0px 7px 0px 0px !important;
  background-color: hsl(210deg 100% 44%) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  transition: 31ms cubic-bezier(.5, .7, .4, 1) !important;
  text-decoration: none !important;
  color: white !important;
  font-weight: bold !important;
  letter-spacing: 4px !important;
  z-index: 1 !important;
  cursor: pointer !important;
  gap: 0 !important; /* 移除gap */
  margin: 0 auto !important; /* 居中显示 */
  width: auto !important;
  min-width: 200px !important;
}

/* 针对Next.js生成的JSX类名的特殊覆盖 */
.contact-cta .btn-primary i[class*="jsx-"],
.contact-cta .btn-primary i[class^="jsx-"],
.about-us-page .contact-cta .btn-primary i[class*="jsx-"],
.about-us-page .contact-cta .btn-primary i[class^="jsx-"] {
  display: inline !important;
  font-style: normal !important;
  font-weight: inherit !important;
  color: inherit !important;
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
}

/* 确保按钮内的字符正确显示 */
.contact-cta .btn-primary i,
.about-us-page .contact-cta .btn-primary i {
  display: inline !important;
  font-style: normal !important;
  font-weight: inherit !important;
  color: inherit !important;
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
}

/* 强制覆盖 - 默认文字显示 */
a[data-text].btn-primary:before,
a.btn-primary[data-text]:before {
  content: attr(data-text) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: absolute !important;
  inset: 0 !important;
  font-size: 15px !important;
  font-weight: bold !important;
  color: white !important;
  letter-spacing: 2px !important;
  opacity: 1 !important;
  transition: all 0.3s ease !important;
  transform: translateY(0) !important;
  z-index: 2 !important;
}

/* 强制覆盖 - 字母默认隐藏 */
a[data-text].btn-primary i,
a.btn-primary[data-text] i {
  color: white !important;
  font-size: 15px !important;
  font-weight: bold !important;
  letter-spacing: 0px !important;
  font-style: normal !important;
  transition: all 0.3s ease !important;
  transform: translateY(20px) !important;
  opacity: 0 !important;
  display: inline-block !important;
  position: relative !important;
  z-index: 3 !important;
}

/* 特定放大：首页CustomSolutions组件中的"获取定制服务方案"按钮 */
/* 使用data-text属性来精确定位这个特定按钮 */
a[data-text*="获取"][data-text*="定制"][data-text*="方案"].btn-primary,
a.btn-primary[data-text*="获取"][data-text*="定制"][data-text*="方案"],
a[data-text*="获取全息投影定制方案"].btn-primary,
a.btn-primary[data-text*="获取全息投影定制方案"] {
  transform: scale(1.4) !important; /* 进一步增加放大比例 */
  min-width: 360px !important; /* 增加中文按钮最小宽度 */
  width: auto !important; /* 允许按钮自动调整宽度 */
  height: 80px !important; /* 进一步增加高度 */
  font-size: 20px !important; /* 进一步增大字体 */
  padding: 0 40px !important; /* 进一步增加内边距 */
  margin: 25px auto !important; /* 增加外边距 */
  white-space: nowrap !important; /* 防止文字换行 */
  flex-wrap: nowrap !important; /* 防止flex项目换行 */
  max-width: 95% !important; /* 稍微增加最大宽度 */
}

/* 英文按钮样式 - 需要更大的宽度 */
a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary,
a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"],
a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary,
a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"],
a[data-text="Get Your Custom Service Plan"].btn-primary,
a.btn-primary[data-text="Get Your Custom Service Plan"] {
  transform: scale(1.2) !important; /* 适中的放大比例 */
  min-width: 420px !important; /* 适中的最小宽度 */
  width: auto !important; /* 允许按钮自动调整宽度 */
  height: 65px !important; /* 适中的高度 */
  font-size: 18px !important; /* 适中的字体大小 */
  padding: 0 35px !important; /* 适中的内边距 */
  margin: 20px auto !important; /* 适中的外边距 */
  white-space: nowrap !important; /* 防止文字换行 */
  flex-wrap: nowrap !important; /* 防止flex项目换行 */
  max-width: 95% !important; /* 适中的最大宽度 */
  overflow: visible !important; /* 确保内容不被裁剪 */
}

/* 特定放大：首页CustomSolutions组件中按钮的默认文字 */
a[data-text*="获取"][data-text*="定制"][data-text*="方案"].btn-primary:before,
a.btn-primary[data-text*="获取"][data-text*="定制"][data-text*="方案"]:before,
a[data-text*="获取全息投影定制方案"].btn-primary:before,
a.btn-primary[data-text*="获取全息投影定制方案"]:before,
/* 英文按钮默认文字样式 */
a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary:before,
a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"]:before,
a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary:before,
a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"]:before,
a[data-text="Get Your Custom Service Plan"].btn-primary:before,
a.btn-primary[data-text="Get Your Custom Service Plan"]:before {
  font-size: 18px !important; /* 适中的默认文字字体 */
}

/* 特定放大：首页CustomSolutions组件中按钮的动画字母 */
a[data-text*="获取"][data-text*="定制"][data-text*="方案"].btn-primary i,
a.btn-primary[data-text*="获取"][data-text*="定制"][data-text*="方案"] i,
a[data-text*="获取全息投影定制方案"].btn-primary i,
a.btn-primary[data-text*="获取全息投影定制方案"] i {
  font-size: 20px !important; /* 进一步增大动画字母字体 */
  white-space: nowrap !important; /* 防止字母换行 */
  flex-shrink: 0 !important; /* 防止字母收缩 */
  display: inline-block !important; /* 确保字母正确显示 */
}

/* 英文按钮动画字母样式 - 特殊处理 */
a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary i,
a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"] i,
a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary i,
a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] i,
a[data-text="Get Your Custom Service Plan"].btn-primary i,
a.btn-primary[data-text="Get Your Custom Service Plan"] i {
  font-size: 18px !important; /* 适中的动画字母字体 */
  white-space: nowrap !important; /* 防止字母换行 */
  flex-shrink: 0 !important; /* 防止字母收缩 */
  display: inline-block !important; /* 确保字母正确显示 */
  min-width: auto !important; /* 允许字母自适应宽度 */
  letter-spacing: 0 !important; /* 默认无额外字母间距 */
}

/* 特定放大：首页CustomSolutions组件中按钮悬停时的动画字母 */
a[data-text*="获取"][data-text*="定制"][data-text*="方案"].btn-primary:hover i,
a.btn-primary[data-text*="获取"][data-text*="定制"][data-text*="方案"]:hover i,
a[data-text*="获取全息投影定制方案"].btn-primary:hover i,
a.btn-primary[data-text*="获取全息投影定制方案"]:hover i {
  font-size: 20px !important; /* 进一步增大悬停时的字体大小 */
  letter-spacing: 2px !important; /* 悬停时增加字母间距 */
}

/* 英文按钮悬停时动画字母样式 - 减少字母间距以防止溢出 */
a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary:hover i,
a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"]:hover i,
a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary:hover i,
a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"]:hover i {
  font-size: 20px !important; /* 进一步增大悬停时的字体大小 */
  letter-spacing: 1px !important; /* 英文按钮悬停时使用较小的字母间距 */
}

/* 响应式设计 - 平板设备 */
@media (max-width: 768px) {
  /* 英文按钮在平板设备上的样式调整 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"],
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] {
    min-width: 400px !important; /* 在平板上减少最小宽度 */
    font-size: 16px !important; /* 减小字体 */
    padding: 0 25px !important; /* 减少内边距 */
    transform: scale(1.15) !important; /* 减少缩放比例 */
  }

  /* 平板设备上的字母样式 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary i,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"] i,
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary i,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] i {
    font-size: 16px !important;
  }
}

/* 响应式设计 - 手机设备 */
@media (max-width: 576px) {
  /* 英文按钮在手机设备上的样式调整 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"],
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] {
    min-width: 300px !important; /* 在手机上进一步减少最小宽度 */
    font-size: 14px !important; /* 进一步减小字体 */
    padding: 0 20px !important; /* 进一步减少内边距 */
    transform: scale(1.05) !important; /* 进一步减少缩放比例 */
    height: 60px !important; /* 减少高度 */
  }

  /* 手机设备上的字母样式 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary i,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"] i,
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary i,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] i {
    font-size: 14px !important;
  }

  /* 手机设备上悬停时减少字母间距 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary:hover i,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"]:hover i,
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary:hover i,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"]:hover i {
    letter-spacing: 0.5px !important; /* 在手机上使用更小的字母间距 */
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/step-image-unified.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/* 步骤图片统一尺寸样式 - 最高优先级 */

/* 基础图片容器统一 */
.step-image,
.premium-card,
.process-steps .step-item .step-image,
.process-steps .step-item .premium-card {
  width: 100% !important;
  height: 800px !important;
  display: block !important;
  margin: 0 auto !important;
  position: relative !important;
  overflow: hidden !important;
  border-radius: 8px !important;
  max-width: 1400px !important;
}

/* 图片本身统一 */
.step-image img,
.step-image .step-img,
.premium-card img,
.premium-card .step-img,
.process-steps .step-item .step-image img,
.process-steps .step-item .step-image .step-img,
.process-steps .step-item .premium-card img,
.process-steps .step-item .premium-card .step-img {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  -o-object-fit: cover !important;
     object-fit: cover !important;
  display: block !important;
  transition: transform 0.3s ease !important;
}

/* 移除悬停放大效果 */
.step-image:hover img,
.step-image:hover .step-img,
.premium-card:hover img,
.premium-card:hover .step-img {
  transform: none !important;
}

/* 响应式设计 - 平板设备 */
@media (max-width: 768px) {
  .step-image,
  .premium-card,
  .process-steps .step-item .step-image,
  .process-steps .step-item .premium-card {
    height: 300px !important;
  }

  .step-image img,
  .step-image .step-img,
  .premium-card img,
  .premium-card .step-img,
  .process-steps .step-item .step-image img,
  .process-steps .step-item .step-image .step-img,
  .process-steps .step-item .premium-card img,
  .process-steps .step-item .premium-card .step-img {
    width: 100% !important;
    height: 100% !important;
    -o-object-fit: cover !important;
       object-fit: cover !important;
  }
}

/* 响应式设计 - 平板设备 */
@media (max-width: 768px) {
  .step-image,
  .premium-card,
  .process-steps .step-item .step-image,
  .process-steps .step-item .premium-card {
    height: 750px !important;
    max-width: 1300px !important;
  }
}

/* 响应式设计 - 手机设备 */
@media (max-width: 576px) {
  .step-image,
  .premium-card,
  .process-steps .step-item .step-image,
  .process-steps .step-item .premium-card {
    height: 650px !important;
  }

  .step-image img,
  .step-image .step-img,
  .premium-card img,
  .premium-card .step-img,
  .process-steps .step-item .step-image img,
  .process-steps .step-item .step-image .step-img,
  .process-steps .step-item .premium-card img,
  .process-steps .step-item .premium-card .step-img {
    width: 100% !important;
    height: 100% !important;
    -o-object-fit: cover !important;
       object-fit: cover !important;
  }
}

/* 确保内容覆盖层正确显示 */
.step-content-overlay {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  padding: 30px !important;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent) !important;
  color: white !important;
  z-index: 2 !important;
}

/* 步骤编号样式 */
.step-number {
  font-size: 3rem !important;
  font-weight: 800 !important;
  color: rgba(255, 255, 255, 0.2) !important;
  line-height: 1 !important;
  position: absolute !important;
  top: 20px !important;
  right: 30px !important;
  z-index: 3 !important;
}

/* 步骤标题和描述 */
.step-content-overlay h3 {
  font-size: 1.75rem !important;
  font-weight: 700 !important;
  margin-bottom: 10px !important;
  color: white !important;
}

.step-content-overlay p {
  font-size: 1rem !important;
  line-height: 1.6 !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 移动设备上的内容覆盖层调整 */
@media (max-width: 768px) {
  .step-content-overlay {
    padding: 20px !important;
  }

  .step-number {
    font-size: 2.5rem !important;
    top: 15px !important;
    right: 20px !important;
  }

  .step-content-overlay h3 {
    font-size: 1.5rem !important;
  }

  .step-content-overlay p {
    font-size: 0.9rem !important;
  }
}

@media (max-width: 576px) {
  .step-content-overlay {
    padding: 15px !important;
  }

  .step-number {
    font-size: 2rem !important;
    top: 10px !important;
    right: 15px !important;
  }

  .step-content-overlay h3 {
    font-size: 1.3rem !important;
  }

  .step-content-overlay p {
    font-size: 0.85rem !important;
  }
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/force-modern-product-style.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/* 强制现代化产品样式覆盖 - 最高优先级 */

/* 强制覆盖所有产品网格样式 */
.grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3.gap-8,
.products-grid,
.modern-product-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
  gap: 2rem !important;
  padding: 2rem 0 !important;
  margin: 0 !important;
}

/* 强制覆盖所有产品卡片样式 */
article.group.cursor-pointer,
.product-card,
.modern-product-card {
  background: white !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05) !important;
  height: auto !important;
  display: block !important;
  max-width: none !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 悬停效果 */
article.group.cursor-pointer:hover,
.product-card:hover,
.modern-product-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
}

/* 产品图片容器 */
.relative.overflow-hidden.bg-neutral-50.rounded-lg,
.product-image,
.modern-product-image {
  position: relative !important;
  width: 100% !important;
  height: 240px !important;
  overflow: hidden !important;
  background: #f8f9fa !important;
  border-radius: 8px !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 产品图片 */
.relative.overflow-hidden.bg-neutral-50.rounded-lg img,
.product-image img,
.modern-product-image img,
.product-img {
  width: 100% !important;
  height: 100% !important;
  -o-object-fit: cover !important;
     object-fit: cover !important;
  transition: transform 0.7s ease !important;
  border-radius: 0 !important;
}

/* 图片悬停效果 */
article.group.cursor-pointer:hover .relative.overflow-hidden.bg-neutral-50.rounded-lg img,
.product-card:hover .product-image img,
.modern-product-card:hover .modern-product-image img,
.product-card:hover .product-img {
  transform: scale(1.05) !important;
}

/* ID 标签 */
.absolute.top-4.left-4 span,
.product-id-badge {
  position: absolute !important;
  top: 1rem !important;
  left: 1rem !important;
  background: rgba(255, 255, 255, 0.9) !important;
  -webkit-backdrop-filter: blur(10px) !important;
          backdrop-filter: blur(10px) !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 6px !important;
  font-family: 'Courier New', monospace !important;
  font-size: 0.75rem !important;
  color: #6b7280 !important;
  font-weight: 500 !important;
  z-index: 2 !important;
}

/* 产品内容区域 */
.space-y-3,
.product-content,
.modern-product-content {
  padding: 1.5rem !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 0.75rem !important;
}

/* 标题容器 */
.flex.items-start.justify-between.gap-4 {
  display: flex !important;
  align-items: flex-start !important;
  justify-content: space-between !important;
  gap: 1rem !important;
}

/* 产品标题 */
.text-lg.font-medium.text-neutral-900,
.product-title {
  font-size: 1.125rem !important;
  font-weight: 500 !important;
  color: #111827 !important;
  line-height: 1.4 !important;
  margin: 0 !important;
  transition: color 0.2s ease !important;
  text-align: left !important;
  border: none !important;
  min-height: auto !important;
  padding: 0 !important;
}

/* 标题悬停效果 */
article.group.cursor-pointer:hover .text-lg.font-medium.text-neutral-900,
.product-card:hover .product-title {
  color: #6b7280 !important;
}

/* 箭头图标 */
.w-4.h-4.text-neutral-400,
.product-arrow {
  width: 1rem !important;
  height: 1rem !important;
  color: #9ca3af !important;
  flex-shrink: 0 !important;
  transition: transform 0.2s ease !important;
}

/* 箭头悬停效果 */
article.group.cursor-pointer:hover .w-4.h-4.text-neutral-400,
.product-card:hover .product-arrow {
  transform: translate(0.25rem, -0.25rem) !important;
}

/* 产品描述 */
.text-sm.text-neutral-600.leading-relaxed.line-clamp-2,
.product-description {
  font-size: 0.875rem !important;
  color: #6b7280 !important;
  line-height: 1.6 !important;
  margin: 0 !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
}

/* 产品分类 */
.text-xs.text-neutral-400.font-mono,
.product-category {
  font-size: 0.75rem !important;
  color: #9ca3af !important;
  font-family: 'Courier New', monospace !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  margin: 0 !important;
}

/* 移除旧的产品覆盖层 */
.product-overlay {
  display: none !important;
}

/* 移除旧的查看详情按钮 */
.view-details-btn {
  display: none !important;
}

/* 强制隐藏所有旧的产品卡片样式 */
.bg-white.rounded-lg.overflow-hidden.shadow-md.hover\:shadow-lg.transition-shadow.duration-300 {
  display: none !important;
}

/* 强制隐藏包含"查看详情"的旧产品卡片 */
a.group.block:has(.text-blue-600.text-sm.font-medium.group-hover\:underline) {
  display: none !important;
}

/* 强制隐藏所有包含旧样式的产品卡片 */
.group.block:has(.relative.h-48.overflow-hidden) {
  display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3.gap-8,
  .products-grid,
  .modern-product-grid {
    grid-template-columns: 1fr !important;
    gap: 1.5rem !important;
    padding: 1rem 0 !important;
  }

  .relative.overflow-hidden.bg-neutral-50.rounded-lg,
  .product-image,
  .modern-product-image {
    height: 200px !important;
  }

  .space-y-3,
  .product-content,
  .modern-product-content {
    padding: 1.25rem !important;
  }

  .text-lg.font-medium.text-neutral-900,
  .product-title {
    font-size: 1rem !important;
  }
}

@media (max-width: 480px) {
  .grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3.gap-8,
  .products-grid,
  .modern-product-grid {
    gap: 1rem !important;
  }

  .relative.overflow-hidden.bg-neutral-50.rounded-lg,
  .product-image,
  .modern-product-image {
    height: 180px !important;
  }

  .space-y-3,
  .product-content,
  .modern-product-content {
    padding: 1rem !important;
  }
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/custom-playground-design.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/* Custom Playground Design Page Styles */

.main-content {
  position: relative;
  overflow-x: hidden;
}

.page-header {
  position: relative;
  overflow: hidden;
}

.bg-gradient {
  background: linear-gradient(135deg, #1a1a2e, #16213e) !important;
  color: white !important;
  padding: 100px 0 !important;
  position: relative;
  overflow: hidden;
}

.bg-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.2;
  z-index: 0;
}

.page-title {
  font-size: 3.5rem !important;
  font-weight: 800 !important;
  letter-spacing: 2px !important;
  margin-bottom: 20px !important;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
  opacity: 1;
  transform: translateY(0);
  position: relative;
  z-index: 1;
}

.animate-title {
  animation: fadeInUp 1s forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.breadcrumbs {
  font-size: 1rem !important;
  letter-spacing: 1px !important;
  position: relative;
  z-index: 1;
}

.breadcrumbs a {
  color: rgba(255, 255, 255, 0.8) !important;
  transition: color 0.3s;
}

.breadcrumbs a:hover {
  color: white !important;
}

.section-padding {
  padding: 100px 0 !important;
}

.bg-light {
  background-color: transparent !important;
}

.enhanced {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 60px !important;
  align-items: center !important;
}

.image-decoration {
  position: relative;
  padding: 20px;
}

.rounded-image {
  border-radius: 12px !important;
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15) !important;
  width: 100% !important;
  height: auto !important;
}

.decoration-element {
  position: absolute;
  width: 120px;
  height: 120px;
  border: 4px solid rgba(26, 26, 46, 0.1);
  z-index: -1;
  border-radius: 50%;
}

.top-right {
  top: -20px;
  right: -20px;
}

.bottom-left {
  bottom: -20px;
  left: -20px;
}

.section-title {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 25px !important;
  line-height: 1.2 !important;
}

.section-title.center {
  text-align: center !important;
}

.accent-border {
  position: relative;
  padding-bottom: 15px;
}

.accent-border:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #1a1a2e, #4e54c8);
}

.accent-line {
  position: relative;
  padding-bottom: 15px;
}

.accent-line:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #1a1a2e, #4e54c8);
}

.section-description {
  font-size: 1.2rem !important;
  color: #666 !important;
  margin-bottom: 50px !important;
  max-width: 800px !important;
  line-height: 1.6 !important;
}

.section-description.center {
  text-align: center !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.lead-text {
  font-size: 1.25rem !important;
  line-height: 1.7 !important;
  margin-bottom: 20px !important;
  font-weight: 300 !important;
}

.supporting-text {
  font-size: 1.1rem !important;
  line-height: 1.7 !important;
  color: #555 !important;
  margin-bottom: 30px !important;
}

.intro-stats {
  display: flex !important;
  margin-top: 40px !important;
  gap: 40px !important;
}

.stat-item {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
}

.stat-number {
  font-size: 2.5rem !important;
  font-weight: 800 !important;
  color: #1a1a2e !important;
  margin-bottom: 5px !important;
}

.stat-label {
  font-size: 0.9rem !important;
  color: #666 !important;
  font-weight: 500 !important;
}

.process-steps {
  display: grid !important;
  gap: 40px !important;
}

.step-item {
  position: relative;
}

.premium-card {
  position: relative;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

.premium-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15) !important;
}

.step-img {
  width: 100% !important;
  height: 400px !important;
  -o-object-fit: cover !important;
     object-fit: cover !important;
}

.step-content-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)) !important;
  color: white !important;
  padding: 40px 30px 30px !important;
}

.step-number {
  font-size: 3rem !important;
  font-weight: 800 !important;
  color: rgba(255, 255, 255, 0.3) !important;
  margin-bottom: 10px !important;
}

.step-content-overlay h3 {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin-bottom: 15px !important;
  color: white !important;
}

.step-content-overlay p {
  font-size: 1rem !important;
  line-height: 1.6 !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

.features-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
  gap: 30px !important;
  margin-top: 50px !important;
}

.premium-feature {
  background: white !important;
  padding: 40px 30px !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  text-align: center !important;
}

.premium-feature:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
}

.feature-icon {
  width: 80px !important;
  height: 80px !important;
  background: linear-gradient(135deg, #1a1a2e, #4e54c8) !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 auto 25px !important;
}

.feature-icon i {
  font-size: 2rem !important;
  color: white !important;
}

.premium-feature h3 {
  font-size: 1.3rem !important;
  font-weight: 600 !important;
  margin-bottom: 15px !important;
  color: #1a1a2e !important;
}

.premium-feature p {
  font-size: 1rem !important;
  line-height: 1.6 !important;
  color: #666 !important;
}

.cta-section {
  background: linear-gradient(135deg, #1a1a2e, #16213e) !important;
  color: white !important;
  padding: 100px 0 !important;
  position: relative;
  overflow: hidden;
}

.cta-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
}

.cta-content {
  text-align: center !important;
  position: relative;
  z-index: 1;
}

.cta-content h2 {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 20px !important;
  color: white !important;
}

.cta-content p {
  font-size: 1.2rem !important;
  margin-bottom: 30px !important;
  color: rgba(255, 255, 255, 0.9) !important;
  max-width: 600px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* 只在项目定制指南页面的CTA区域应用这些按钮样式，且不能有data-text属性 */
.main-content .cta-section .btn-primary:not([data-text]) {
  display: inline-block !important;
  padding: 15px 40px !important;
  background: linear-gradient(135deg, #ff6b6b, #ff5252) !important;
  color: white !important;
  border-radius: 50px !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  text-decoration: none !important;
  border: none !important;
  cursor: pointer !important;
}

.main-content .cta-section .btn-primary:not([data-text]):hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3) !important;
  color: white !important;
}

.main-content .cta-section .btn-glow:not([data-text]) {
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.3) !important;
}

/* Animation Classes */
.reveal-animation {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.reveal-animation.is-revealed {
  opacity: 1;
  transform: translateY(0);
}

.delay-100 { transition-delay: 0.1s; }
.delay-200 { transition-delay: 0.2s; }
.delay-300 { transition-delay: 0.3s; }
.delay-400 { transition-delay: 0.4s; }

/* Responsive Design */
@media (max-width: 768px) {
  .page-title {
    font-size: 2.5rem !important;
  }

  .section-title {
    font-size: 2rem !important;
  }

  .enhanced {
    grid-template-columns: 1fr !important;
    gap: 40px !important;
  }

  .intro-stats {
    flex-wrap: wrap !important;
    gap: 20px !important;
  }

  .stat-number {
    font-size: 2rem !important;
  }

  .cta-content h2 {
    font-size: 2.2rem !important;
  }
}

/* Case Studies Styles */
.case-studies {
  background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 100%) !important;
}

.case-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
  gap: 30px !important;
  margin-top: 50px !important;
}

.case-item {
  background: white !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

.case-item:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
}

.case-image {
  position: relative !important;
  overflow: hidden !important;
  height: 250px !important;
}

.case-image img {
  width: 100% !important;
  height: 100% !important;
  -o-object-fit: cover !important;
     object-fit: cover !important;
  transition: transform 0.3s ease !important;
}

.case-item:hover .case-image img {
  transform: scale(1.05) !important;
}

.case-content {
  padding: 25px !important;
}

.case-content h3 {
  font-size: 1.4rem !important;
  font-weight: 700 !important;
  color: #1a1a2e !important;
  margin-bottom: 15px !important;
}

.case-content p {
  color: #666 !important;
  line-height: 1.6 !important;
  margin-bottom: 20px !important;
}

.case-stats {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 8px !important;
}

.stat-badge {
  background: linear-gradient(135deg, #264eca 0%, #2239b4 100%) !important;
  color: white !important;
  padding: 4px 12px !important;
  border-radius: 20px !important;
  font-size: 0.8rem !important;
  font-weight: 500 !important;
}

/* Testimonials Styles */
.design-testimonials {
  background: #f8fafc !important;
}

.testimonial-slider {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
  gap: 30px !important;
  margin-top: 50px !important;
}

.testimonial-item {
  background: white !important;
  border-radius: 12px !important;
  padding: 30px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
}

.quote-icon {
  position: absolute !important;
  top: -15px !important;
  left: 30px !important;
  background: linear-gradient(135deg, #264eca 0%, #2239b4 100%) !important;
  color: white !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.2rem !important;
}

.testimonial-quote p {
  font-size: 1.1rem !important;
  line-height: 1.7 !important;
  color: #333 !important;
  margin-bottom: 25px !important;
  font-style: italic !important;
}

.testimonial-author {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  border-top: 1px solid #eee !important;
  padding-top: 20px !important;
}

.author-info h4 {
  font-size: 1.1rem !important;
  font-weight: 700 !important;
  color: #1a1a2e !important;
  margin-bottom: 5px !important;
}

.client-title {
  color: #666 !important;
  font-size: 0.9rem !important;
}

.rating {
  display: flex !important;
  gap: 3px !important;
}

.rating i {
  color: #ffd700 !important;
  font-size: 1rem !important;
}

@media (max-width: 768px) {
  .case-grid {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }

  .testimonial-slider {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }

  .testimonial-author {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 15px !important;
  }
}

@media (max-width: 576px) {
  .premium-grid {
    grid-template-columns: 1fr !important;
  }

  .page-title {
    font-size: 2rem !important;
  }

  .section-padding {
    padding: 50px 0 !important;
  }

  .cta-content h2 {
    font-size: 1.8rem !important;
  }

  .main-content .cta-section .btn-primary:not([data-text]) {
    width: 100% !important;
    max-width: 300px !important;
  }

  .case-grid {
    grid-template-columns: 1fr !important;
  }

  .testimonial-item {
    padding: 20px !important;
  }

  .testimonial-quote p {
    font-size: 1rem !important;
  }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/holographic-guide.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/* Holographic Guide Page Styles */

.holographic-guide-page {
  min-height: 100vh !important;
  background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 100%) !important;
  position: relative !important;
  z-index: 1 !important;
  width: 100% !important;
  overflow-x: hidden !important;
}

.holographic-guide-breadcrumb {
  background: white !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 1rem 0 !important;
}

.holographic-guide-breadcrumb nav {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  font-size: 0.875rem !important;
  color: #6b7280 !important;
}

.holographic-guide-breadcrumb a {
  color: #6b7280 !important;
  transition: color 0.3s ease !important;
}

.holographic-guide-breadcrumb a:hover {
  color: #2563eb !important;
}

.holographic-guide-container {
  max-width: 1280px !important;
  margin: 0 auto !important;
  padding: 0 1rem !important;
}

.holographic-guide-header {
  text-align: center !important;
  margin-bottom: 3rem !important;
  padding: 4rem 0 !important;
}

.holographic-guide-badge {
  display: inline-flex !important;
  align-items: center !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 9999px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  background-color: #dbeafe !important;
  color: #2563eb !important;
  margin-bottom: 1rem !important;
}

.holographic-guide-title {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  color: #111827 !important;
  line-height: 1.2 !important;
  margin-bottom: 1.5rem !important;
}

.holographic-guide-title-accent {
  color: #2563eb !important;
}

.holographic-guide-description {
  font-size: 1.125rem !important;
  color: #6b7280 !important;
  line-height: 1.7 !important;
  max-width: 48rem !important;
  margin: 0 auto !important;
}

.holographic-guide-stats {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 4rem !important;
}

.holographic-guide-stat {
  text-align: center !important;
}

.holographic-guide-stat-number {
  font-size: 1.875rem !important;
  font-weight: 700 !important;
  color: #111827 !important;
  margin-bottom: 0.5rem !important;
}

.holographic-guide-stat-label {
  font-size: 0.875rem !important;
  color: #6b7280 !important;
}

.holographic-guide-steps {
  display: flex !important;
  flex-direction: column !important;
  gap: 2rem !important;
}

.holographic-guide-step {
  background: white !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  padding: 2rem !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

.holographic-guide-step:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

.holographic-guide-step-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  color: #111827 !important;
  margin-bottom: 1rem !important;
}

.holographic-guide-step-list {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.holographic-guide-step-item {
  color: #374151 !important;
  margin-bottom: 0.5rem !important;
  padding-left: 1rem !important;
  position: relative !important;
}

.holographic-guide-step-item:before {
  content: "•" !important;
  color: #2563eb !important;
  font-weight: bold !important;
  position: absolute !important;
  left: 0 !important;
}

.holographic-guide-cta {
  margin-top: 4rem !important;
  text-align: center !important;
}

.holographic-guide-cta-box {
  background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%) !important;
  color: white !important;
  border-radius: 0.5rem !important;
  padding: 2rem !important;
}

.holographic-guide-cta-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 1rem !important;
  color: white !important;
}

.holographic-guide-cta-description {
  color: #bfdbfe !important;
  margin-bottom: 1.5rem !important;
  max-width: 32rem !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.holographic-guide-cta-button {
  display: inline-flex !important;
  align-items: center !important;
  padding: 0.75rem 1.5rem !important;
  background: white !important;
  color: #2563eb !important;
  border-radius: 0.375rem !important;
  font-weight: 500 !important;
  text-decoration: none !important;
  transition: background-color 0.3s ease !important;
}

.holographic-guide-cta-button:hover {
  background-color: #f3f4f6 !important;
  color: #2563eb !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .holographic-guide-title {
    font-size: 2.25rem !important;
  }

  .holographic-guide-container {
    padding: 0 1.5rem !important;
  }
}

@media (max-width: 768px) {
  .holographic-guide-title {
    font-size: 2rem !important;
  }

  .holographic-guide-stats {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .holographic-guide-step {
    padding: 1.5rem !important;
  }

  .holographic-guide-step-title {
    font-size: 1.25rem !important;
  }

  .holographic-guide-header {
    padding: 2rem 0 !important;
  }

  .holographic-guide-cta-box {
    padding: 1.5rem !important;
  }

  .holographic-guide-cta-title {
    font-size: 1.25rem !important;
  }
}

@media (max-width: 640px) {
  .holographic-guide-title {
    font-size: 1.75rem !important;
  }

  .holographic-guide-description {
    font-size: 1rem !important;
  }

  .holographic-guide-stat-number {
    font-size: 1.5rem !important;
  }

  .holographic-guide-cta-button {
    width: 100% !important;
    justify-content: center !important;
    max-width: 300px !important;
  }
}

/* Force override any conflicting styles */
.holographic-guide-page * {
  box-sizing: border-box !important;
}

.holographic-guide-page .container {
  max-width: 1280px !important;
  margin: 0 auto !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

/* Ensure proper spacing */
.holographic-guide-page .space-y-8 > * + * {
  margin-top: 2rem !important;
}

.holographic-guide-page .space-y-2 > * + * {
  margin-top: 0.5rem !important;
}

.holographic-guide-page .mb-16 {
  margin-bottom: 4rem !important;
}

.holographic-guide-page .mb-12 {
  margin-bottom: 3rem !important;
}

.holographic-guide-page .mb-6 {
  margin-bottom: 1.5rem !important;
}

.holographic-guide-page .mb-4 {
  margin-bottom: 1rem !important;
}

/* 确保页面内容不被其他元素覆盖 */
.holographic-guide-page {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.holographic-guide-breadcrumb {
  position: relative !important;
  z-index: 10 !important;
}

.holographic-guide-container {
  position: relative !important;
  z-index: 5 !important;
  display: block !important;
  visibility: visible !important;
}

.holographic-guide-header {
  position: relative !important;
  z-index: 5 !important;
  display: block !important;
  visibility: visible !important;
}

.holographic-guide-stats {
  position: relative !important;
  z-index: 5 !important;
  display: grid !important;
  visibility: visible !important;
}

.holographic-guide-steps {
  position: relative !important;
  z-index: 5 !important;
  display: flex !important;
  visibility: visible !important;
}

.holographic-guide-cta {
  position: relative !important;
  z-index: 5 !important;
  display: block !important;
  visibility: visible !important;
}

/* 防止被弹窗或其他元素覆盖 */
body.holographic-guide-active {
  overflow-x: hidden !important;
}

/* 强制显示页面内容 */
.holographic-guide-page > * {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/*!********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/modern-product-grid.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************************/
/* Modern Product Grid Styles - 高优先级覆盖 */

/* 强制覆盖旧的产品网格样式 */
.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.gap-8,
.modern-product-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
  gap: 2rem !important;
  padding: 2rem 0 !important;
}

/* 强制覆盖所有旧的产品卡片样式 */
article.group.cursor-pointer,
.modern-product-card,
.product-card {
  background: white !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05) !important;
  height: auto !important;
  display: block !important;
}

article.group.cursor-pointer:hover,
.modern-product-card:hover,
.product-card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
}

/* 强制覆盖产品图片容器样式 */
.relative.overflow-hidden.bg-neutral-50.rounded-lg,
.modern-product-image,
.product-image {
  position: relative !important;
  width: 100% !important;
  height: 240px !important;
  overflow: hidden !important;
  background: #f8f9fa !important;
  border-radius: 8px !important;
}

.relative.overflow-hidden.bg-neutral-50.rounded-lg img,
.modern-product-image img,
.product-image img {
  width: 100% !important;
  height: 100% !important;
  -o-object-fit: cover !important;
     object-fit: cover !important;
  transition: transform 0.7s ease !important;
}

article.group.cursor-pointer:hover .relative.overflow-hidden.bg-neutral-50.rounded-lg img,
.modern-product-card:hover .modern-product-image img,
.product-card:hover .product-image img {
  transform: scale(1.05) !important;
}

/* ID 标签 */
.product-id-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
  z-index: 2;
}

/* 产品内容区域 */
.modern-product-content {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* 标题和箭头容器 */
.product-title-container {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 1rem;
}

.product-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
  line-height: 1.4;
  margin: 0;
  transition: color 0.2s ease;
}

.modern-product-card:hover .product-title {
  color: #6b7280;
}

/* 箭头图标 */
.product-arrow {
  width: 1rem;
  height: 1rem;
  color: #9ca3af;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.modern-product-card:hover .product-arrow {
  transform: translate(0.25rem, -0.25rem);
}

/* 产品描述 */
.product-description {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.6;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Tailwind line-clamp 支持 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 产品分类 */
.product-category {
  font-size: 0.75rem;
  color: #9ca3af;
  font-family: 'Courier New', monospace;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-product-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 1rem 0;
  }

  .modern-product-image {
    height: 200px;
  }

  .modern-product-content {
    padding: 1.25rem;
  }

  .product-title {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .modern-product-grid {
    gap: 1rem;
  }

  .modern-product-image {
    height: 180px;
  }

  .modern-product-content {
    padding: 1rem;
  }
}

/* 加载状态 */
.product-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #6b7280;
}

/* 空状态 */
.product-empty {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.product-empty h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: #374151;
}

.product-empty p {
  font-size: 0.875rem;
  margin: 0;
}

/* 过滤器样式 */
.product-filters {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-button {
  padding: 0.5rem 1rem;
  border: 1px solid #e5e7eb;
  background: white;
  color: #6b7280;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-button:hover,
.filter-button.active {
  background: #111827;
  color: white;
  border-color: #111827;
}

/* 网格动画 */
.modern-product-card {
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.modern-product-card:nth-child(1) { animation-delay: 0.1s; }
.modern-product-card:nth-child(2) { animation-delay: 0.2s; }
.modern-product-card:nth-child(3) { animation-delay: 0.3s; }
.modern-product-card:nth-child(4) { animation-delay: 0.4s; }
.modern-product-card:nth-child(5) { animation-delay: 0.5s; }
.modern-product-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/globals.css ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/* Reset and Base Styles */ /* 核心 Tailwind CSS 样式 - 必须在最前面 */ /* 确保新样式覆盖产品页banner */ /* 使用现代forced-colors替代-ms-high-contrast */ /* 修复-ms-high-contrast弃用警告 */ /* 强力覆盖所有第三方库的-ms-high-contrast媒体查询 */ /* 完全阻止所有-ms-high-contrast媒体查询 */ /* 添加动画效果 */ /* 统一CTA样式 */ /* 统一步骤图片尺寸样式 */ /* 强制现代化产品样式 - 最高优先级 */ /* 定制页面样式 */ /* 全息指南页面样式 */
/* 移除旧的产品页面修复样式，使用最新的product-detail-fix.css */

/* 产品图片1920x1080比例样式 */
.product-image-1920x1080 {
    aspect-ratio: 16/9 !important;
    width: 100% !important;
    min-height: 400px !important;
    max-height: 600px !important;
}

.product-image-1920x1080 img {
    width: 100% !important;
    height: 100% !important;
    -o-object-fit: cover !important;
       object-fit: cover !important;
    border-radius: 12px !important;
}

/* 产品页面优化样式 */
.product-detail-page {
    position: relative;
    overflow-x: hidden;
}

/* 缩略图hover效果 - 移除放大效果 */
.hover\:scale-102:hover {
    /* transform: scale(1.02); */
}

/* 平滑过渡动画 */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 产品特点卡片动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-card {
    animation: fadeInUp 0.6s ease-out forwards;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }

/* 骨架屏动画优化 */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.animate-pulse {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 2s infinite linear;
}

/* 响应式优化 */
@media (max-width: 640px) {
    .product-image-1920x1080 {
        aspect-ratio: 16/9 !important;
    }
}

/* 下拉菜单动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 应用动画到下拉菜单 */
body > div[style*="position: fixed"][style*="z-index: 100000"] {
    animation: fadeIn 0.2s ease forwards;
}

:root {
    /* Primary Colors */
    --primary-color: #1a1a2e; /* Main brand color - Deep Navy */
    --primary-hover: #13132a; /* Darker shade for hover states */
    --secondary-color: #ff6b6b; /* Secondary accent color */
    --secondary-hover: #ff5252; /* Darker shade for hover states */

    /* Text Colors */
    --text-dark: #333333; /* Main text color */
    --text-medium: #666666; /* Secondary text color */
    --text-light: #999999; /* Light text color */

    /* Background Colors */
    --bg-white: #ffffff; /* White background */
    --bg-light: #f8f8f8; /* Light background */
    --bg-gray: #eeeeee; /* Gray background */
    --bg-primary: #1a1a2e; /* Primary background color */

    /* Border Colors */
    --border-light: #eeeeee; /* Light border color */
    --border-medium: #dddddd; /* Medium border color */

    /* Shadow */
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.05); /* Light shadow */
    --shadow-medium: 0 5px 15px rgba(0, 0, 0, 0.1); /* Medium shadow */

    /* New Colors */
    --primary-text: #333333;
    --secondary-text: #666666;
    --light-text: #999999;
    --accent-color: #333333;
    --light-bg: #f9f9f9;
    --border-color: #eeeeee;
    --transition-fast: 0.3s ease;
    --transition-medium: 0.5s ease;
    --box-shadow-subtle: 0 10px 30px rgba(0, 0, 0, 0.05);
    --box-shadow-medium: 0 15px 40px rgba(0, 0, 0, 0.1);

    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 255, 255, 255;
    --background-end-rgb: 255, 255, 255;
}

/*
@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}
*/

/* CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    min-height: 100vh;
    overflow-x: hidden;
    font-family: 'Inter', 'Helvetica Neue', Arial, sans-serif;
    color: var(--primary-text);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-poppins, sans-serif);
    font-weight: 600;
    line-height: 1.2;
}

strong, b {
    font-weight: 600;
}

a {
    color: inherit;
    text-decoration: none;
}

ul {
    list-style: none;
}

.container {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 640px) {
    .container {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (min-width: 1024px) {
    .container {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* Header Styles */
.header {
    position: relative;
    z-index: 9999; /* 提高头部的z-index到最高 */
}

/* Header Main */
.header-main {
    padding: 15px 0;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
    /* 移除可能限制下拉菜单显示的overflow属性 */
}

.header-main.transparent {
    background-color: transparent;
    background-image: linear-gradient(to bottom, rgba(0,0,0,0.3), rgba(0,0,0,0));
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9999; /* 提高透明导航的层级 */
}

.header-main.sticky {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9999; /* 提高固定导航的层级 */
    padding: 10px 0;
    box-shadow: var(--shadow-medium);
    animation: slideDown 0.3s ease-out;
    background-color: var(--bg-primary);
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

.header-main .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* Logo */
.logo a {
    display: flex;
    align-items: center;
}

.logo img {
    height: 35px;
    margin-top: 3px;
}

/* Nav Container */
.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    margin-left: 30px;
    position: static; /* 确保容器不会意外创建新的定位上下文 */
}

/* Main Navigation */
.main-nav {
    display: flex;
    position: static; /* 确保不创建新的定位上下文 */
}

.nav-list {
    display: flex;
    align-items: center;
    gap: 5px;
    position: static; /* 确保不创建新的定位上下文 */
}

.nav-item {
    position: relative;
    margin: 0 5px;
}

.nav-link {
    display: block;
    padding: 10px 10px;
    font-weight: 300;
    color: white !important;
    position: relative;
    transition: color 0.3s ease;
    font-size: 15px;
    white-space: nowrap;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 1.5px;
    text-transform: uppercase;
}

.nav-link:hover {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* 添加首页链接的特殊样式 */
.nav-item:first-child .nav-link {
    color: white !important;
    font-weight: 600;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
    position: relative;
}

.nav-item:first-child .nav-link::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 10px;
    right: 10px;
    height: 2px;
    background-color: white;
    transform: scaleX(0.7);
    transition: transform 0.3s ease;
}

.nav-item:first-child .nav-link:hover::after {
    transform: scaleX(1);
}

/* 移动菜单中的首页链接样式 */
.mobile-nav-item:first-child .mobile-nav-link {
    color: white;
    font-weight: 600;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
}

/* Dropdown Menus */
.has-dropdown {
    position: relative;
}

.has-dropdown > .nav-link i {
    font-size: 9px;
    margin-left: 2px;
    transition: transform 0.3s ease;
    position: relative;
    top: -1px;
    opacity: 0.8;
}

.has-dropdown:hover > .nav-link i {
    transform: rotate(180deg);
}

/* 保留下拉菜单项目的样式 */
.dropdown-item {
    position: relative;
}

.dropdown-link {
    display: block;
    padding: 8px 20px;
    color: var(--text-dark);
    font-size: 14px;
    transition: color 0.3s ease, background-color 0.3s ease;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.dropdown-link:hover {
    color: var(--primary-color);
    background-color: rgba(10, 89, 247, 0.05);
}

.has-submenu {
    position: relative;
}

.has-submenu > .dropdown-link::after {
    content: '\f054';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 8px;
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s ease;
    color: var(--text-light);
}

.has-submenu:hover > .dropdown-link::after {
    transform: translateY(-50%) rotate(90deg);
}

.submenu {
    position: absolute;
    top: 0;
    left: 100%;
    min-width: 200px;
    background-color: var(--bg-white);
    box-shadow: var(--shadow-medium);
    border-radius: 4px;
    padding: 10px 0;
    opacity: 0;
    visibility: hidden;
    transform: translateX(10px);
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s;
    z-index: 101;
}

.submenu-link {
    display: block;
    padding: 8px 20px;
    color: var(--text-dark);
    font-size: 14px;
    transition: color 0.3s ease, background-color 0.3s ease;
}

.submenu-link:hover {
    color: var(--primary-color);
    background-color: rgba(10, 89, 247, 0.05);
}

.header-actions {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.search-icon {
    color: var(--bg-white);
    font-size: 16px;
    margin-right: 15px;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.account-icon {
    color: var(--bg-white);
    font-size: 16px;
    margin-right: 20px;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.search-icon:hover, .account-icon:hover {
    opacity: 0.8;
}

.language-selector {
    position: relative;
    margin-right: 20px;
}

.current-language {
    display: flex;
    align-items: center;
    color: var(--bg-white);
    cursor: pointer;
}

.current-language .fa-globe {
    margin-right: 4px;
    font-size: 14px;
}

.current-language .fa-chevron-down {
    font-size: 8px;
    margin-left: 4px;
    transition: transform 0.3s ease;
    position: relative;
    top: -1px;
    opacity: 0.8;
}

.language-selector:hover .current-language .fa-chevron-down {
    transform: rotate(180deg);
}

.language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 150px;
    background-color: var(--bg-white);
    box-shadow: var(--shadow-medium);
    border-radius: 4px;
    padding: 10px 0;
    margin-top: 10px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s;
    z-index: 100;
}

.language-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.language-selector:hover .language-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.rotate-180 {
    transform: rotate(180deg);
}

.language-link {
    display: block;
    padding: 8px 20px;
    color: var(--text-dark);
    font-size: 14px;
    transition: color 0.3s ease, background-color 0.3s ease;
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-family: inherit;
}

.language-link:hover {
    color: var(--primary-color);
    background-color: rgba(10, 89, 247, 0.05);
}

.language-item.active .language-link {
    color: var(--primary-color);
    font-weight: 600;
    background-color: rgba(10, 89, 247, 0.05);
}

.btn-quote {
    display: inline-block;
    padding: 12px 24px;
    background-color: white;
    color: #0a59f7;
    font-weight: 600;
    border-radius: 4px;
    font-size: 15px;
    transition: all 0.3s ease;
    border: 2px solid white;
    white-space: nowrap;
}

.btn-quote:hover {
    background-color: transparent;
    color: white;
    box-shadow: 0 4px 10px rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    width: 30px;
    height: 20px;
    justify-content: space-between;
    background: transparent;
    border: none;
    cursor: pointer;
    z-index: 1001;
}

.mobile-menu-toggle span {
    width: 100%;
    height: 2px;
    background-color: var(--bg-white);
    transition: all 0.3s ease;
    position: relative;
}

.mobile-menu-toggle span.active:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle span.active:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle span.active:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.mobile-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-white);
    padding: 80px 20px 40px;
    z-index: 2000; /* 提高层级，确保在其他元素之上 */
    overflow-y: auto;
    transform: translateY(-100%);
    transition: transform 0.5s ease;
    visibility: hidden;
}

.mobile-menu.active {
    transform: translateY(0);
    visibility: visible;
}

.mobile-nav-item {
    margin-bottom: 10px;
    border-bottom: 1px solid var(--border-light);
}

.mobile-nav-link {
    display: block;
    padding: 12px 0;
    color: var(--text-dark);
    font-weight: 300;
    position: relative;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    transition: all 0.3s ease;
}

.mobile-nav-link:hover {
    color: var(--primary-color);
}

.mobile-dropdown-toggle {
    position: absolute;
    right: 0;
    top: 12px;
    font-size: 12px;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.mobile-dropdown-toggle.active {
    transform: rotate(180deg);
    color: var(--primary-color);
}

.mobile-submenu, .mobile-submenu-level2 {
    display: block;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background-color: var(--bg-light);
}

.mobile-submenu.active, .mobile-submenu-level2.active {
    max-height: 1000px;
    padding: 5px 0;
    display: block;
}

.mobile-submenu-item {
    margin-bottom: 0;
}

.mobile-submenu-link {
    display: block;
    padding: 10px 20px;
    color: var(--text-medium);
    font-size: 14px;
}

.mobile-submenu-link:hover {
    color: var(--primary-color);
}

body.menu-open {
    overflow: hidden;
}

/* Main Content */
.main-content {
    position: relative;
    z-index: 1; /* 确保主内容区域层级低于导航 */
}

/* Hero Slider */
.hero-slider-wrapper {
    width: 100%;
    overflow: hidden;
    margin-bottom: 0;
    position: relative;
    z-index: 1; /* 确保slider层级低于导航 */
}

.hero-slider {
    position: relative;
    width: 100%;
    height: 100vh; /* 使用100vh使其全屏 */
    overflow: hidden;
    z-index: 1;
}

.slider-container {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;
}

.slider-wrapper {
    display: flex;
    transition: transform 0.5s ease;
    height: 100%; /* 调整为100%高度 */
}

.slide {
    flex: 0 0 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 1s ease, visibility 1s;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
}

.slide.active {
    opacity: 1;
    visibility: visible;
}

.slide-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: #000; /* 黑色背景，更适合全屏显示 */
}

.slide-image {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover; /* 使用cover确保整个区域都被填满 */
    -o-object-position: center;
       object-position: center;
}

.slide-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 5;
    width: 80%;
    max-width: 1200px;
}

.slide-content h2 {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

@media (max-width: 992px) {
    .slide-content h2 {
        font-size: 3rem;
    }
}

@media (max-width: 768px) {
    .slide-content h2 {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    .slide-content h2 {
        font-size: 2rem;
    }
}

.slide a {
    display: block;
    width: 100%;
    height: 100%;
    position: relative;
}

.slider-dots {
    position: absolute;
    bottom: 30px; /* 增大底部距离 */
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 12px; /* 增大间距 */
    z-index: 10;
}

.dot {
    width: 14px; /* 增大点的尺寸 */
    height: 14px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
}

.dot:hover {
    background-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.2);
}

.dot.active {
    background-color: var(--primary-color);
    transform: scale(1.3);
}

.slider-arrows {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 30px; /* 增大左右间距 */
    z-index: 10;
}

.arrow {
    width: 50px; /* 增大箭头尺寸 */
    height: 50px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
    color: var(--bg-white);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
    border: none;
    font-size: 18px;
}

.arrow:hover {
    background-color: rgba(0, 0, 0, 0.5);
    transform: scale(1.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .hero-slider {
        height: 100vh; /* 保持全屏 */
    }

    .arrow {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
}

@media (max-width: 576px) {
    .hero-slider {
        height: 100vh; /* 保持全屏 */
    }

    .slider-arrows {
        padding: 0 15px;
    }
}

/* Services Features Section */
.services-features {
    padding: 80px 0;
    overflow: hidden;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 20px;
    padding: 20px 0;
}

.feature-card {
    background-color: var(--bg-white);
    padding: 30px 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    width: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.feature-icon {
    font-size: 30px;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.feature-card h3 {
    font-size: 18px;
    margin-bottom: 10px;
    font-weight: 600;
}

.feature-card p {
    font-size: 14px;
    color: var(--text-medium);
    line-height: 1.5;
}

/* Custom Solutions Section */
.custom-solutions {
    padding: 60px 0;
    background-color: var(--bg-light);
}

.section-title {
    font-size: 32px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    color: var(--text-dark);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: 10px;
}

.solutions-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.solution-card {
    background-color: var(--bg-white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.solution-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.solution-image {
    height: 200px;
    overflow: hidden;
}

.solution-image img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
    transition: transform 0.5s ease;
}

/* 移除解决方案卡片图片悬停放大效果 */

.solution-card h3 {
    padding: 15px;
    font-size: 16px;
    font-weight: 600;
    transition: color 0.3s ease;
}

.solution-card:hover h3 {
    color: var(--primary-color);
}

.cta-button {
    text-align: center;
    margin-top: 40px;
}

.btn-primary {
    display: inline-block;
    padding: 12px 30px;
    background-color: var(--primary-color);
    color: var(--bg-white);
    font-weight: 600;
    border-radius: 5px;
    font-size: 16px;
    transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(10, 89, 247, 0.2);
}

/* Media Queries */
@media (max-width: 1200px) {
    .container {
        max-width: 960px;
    }
}

@media (max-width: 992px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 20px;
    }

    .container {
        max-width: 720px;
    }

    .footer-columns {
        grid-template-columns: repeat(3, 1fr);
    }

    .main-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .header-actions {
        margin-left: auto;
    }

    .search-icon, .account-icon {
        margin-right: 15px;
    }

    .language-selector {
        margin-right: 15px;
    }

    .mobile-menu {
        display: block;
    }

    .header-main.sticky {
        padding: 8px 0;
    }

    .logo img {
        height: 30px;
    }
}

@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .solutions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .search-icon, .account-icon {
        margin-right: 10px;
    }

    .btn-quote {
        padding: 8px 15px;
        font-size: 13px;
    }
}

@media (max-width: 576px) {
    .header-actions {
        margin-left: 10px;
    }

    .search-icon, .language-selector, .account-icon {
        margin-right: 8px;
    }

    .btn-quote {
        display: none;
    }

    .features-grid {
        grid-template-columns: 1fr !important;
    }

    .solutions-grid {
        grid-template-columns: 1fr;
    }
}

/* Factory Showcase Section */
.factory-section {
    padding: 80px 0;
    background-color: var(--bg-light);
}

.section-description {
    text-align: center;
    max-width: 1200px;
    margin: 0 auto 30px;
    color: var(--text-medium);
    line-height: 1.6;
}

.factory-sliders-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: center;
    margin: 40px auto;
    width: 96%; /* 占据屏幕96%的宽度 */
    max-width: 100%; /* 使用全屏宽度 */
    padding: 0;
}

.factory-slider {
    position: relative;
    width: calc(50% - 15px);
    min-width: 500px; /* 增加最小宽度 */
    max-width: 800px; /* 限制最大宽度 */
    overflow: hidden;
}

.factory-slider-container {
    position: relative;
    width: 100%;
    border-radius: 12px;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
}

.factory-slides {
    position: relative;
    height: 450px;
    background-color: var(--bg-gray);
}

.factory-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.8s ease, visibility 0.8s;
}

.factory-slide.active {
    opacity: 1;
    visibility: visible;
}

.factory-slide img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
}

/* 确保只有活动幻灯片的文字标题显示 */
.factory-slide .slide-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    color: white;
    padding: 25px 80px 25px 25px;
    font-size: 1.2rem;
    font-weight: 300;
    z-index: 2;
    transform: translateY(20px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.8s ease 0.3s;
}

.factory-slide.active .slide-caption {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.factory-slider-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 12px;
    z-index: 10;
}

.factory-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
}

.factory-dot:hover {
    background-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.2);
}

.factory-dot.active {
    background-color: var(--primary-color);
    transform: scale(1.2);
}

.factory-slider-arrows {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 10;
}

.factory-arrow {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.3s ease;
    border: none;
    font-size: 16px;
}

.factory-arrow:hover {
    background-color: rgba(0, 0, 0, 0.5);
    transform: scale(1.1);
}

/* 响应式设计调整 */
@media (max-width: 1800px) {
    .factory-sliders-container {
        width: 98%;
    }

    .factory-slider {
        min-width: 450px;
    }
}

@media (max-width: 1200px) {
    .factory-slider {
        min-width: 400px;
    }
}

@media (max-width: 992px) {
    .factory-slider {
        width: 94%;
        min-width: 300px;
        margin: 0 auto 30px;
    }
}

@media (max-width: 768px) {
    .factory-slides {
        height: 380px;
    }

    .factory-arrow {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .factory-slides {
        height: 300px;
    }

    .factory-slider-arrows {
        padding: 0 10px;
    }

    .factory-arrow {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }
}

/* Global Case Showcase */
.global-showcase {
    padding: 60px 0;
}

.country-tabs {
    margin: 30px 0;
}

.country-list {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
}

.country-item {
    padding: 8px 20px;
    background-color: var(--bg-light);
    border-radius: 30px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.country-item:hover,
.country-item.active {
    background-color: var(--primary-color);
    color: white;
}

.case-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.case-item {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background-color: var(--bg-white);
}

.case-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.case-image {
    height: 250px;
    overflow: hidden;
}

.case-info {
    padding: 20px;
}

.case-info h3 {
    font-size: 18px;
    margin-bottom: 5px;
}

.case-info p {
    color: var(--text-medium);
    font-size: 14px;
}

/* Responsive styles for factory and case sections */
@media (max-width: 992px) {
    .case-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .case-grid {
        grid-template-columns: 1fr;
    }

    .country-list {
        gap: 10px;
    }

    .country-item {
        padding: 6px 15px;
        font-size: 14px;
    }
}

/* About Section Styles */
.about-section {
    padding: 60px 0;
    background-color: var(--bg-light);
}

.about-content {
    margin-top: 40px;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.about-text-container {
    flex: 1;
}

.about-text p {
    margin-bottom: 20px;
    line-height: 1.7;
}

.about-text .btn-primary {
    margin-top: 10px;
}

/* Stats Container */
.stats-container {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 50px 0;
    gap: 20px;
}

.stat-box {
    flex: 1;
    min-width: 200px;
    background-color: var(--bg-white);
    padding: 25px 20px;
    border-radius: 8px;
    box-shadow: var(--shadow-light);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-box:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stat-description {
    font-size: 0.9rem;
    color: var(--text-medium);
    line-height: 1.4;
}

/* Team Section */
.team-section {
    margin: 50px 0;
}

.section-subtitle {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin-bottom: 20px;
    text-align: center;
}

.team-placeholder {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
    height: 400px;
}

/* Certificates Section */
.certificates-section {
    margin: 50px 0;
}

.certificate-description {
    text-align: center;
    max-width: 700px;
    margin: 0 auto 30px;
    color: var(--text-medium);
}

.certificate-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.certificate-item {
    width: 100%;
    height: 200px;
    padding: 10px;
    background-color: var(--bg-white);
    border-radius: 8px;
    box-shadow: var(--shadow-light);
    transition: transform 0.3s ease;
}

.certificate-item:hover {
    transform: scale(1.05);
}

/* CTA Section */
.about-cta {
    margin: 50px 0 20px;
    text-align: center;
    padding: 40px;
    background-color: var(--bg-white);
    border-radius: 8px;
    box-shadow: var(--shadow-light);
}

.cta-title {
    font-size: 1.5rem;
    margin-bottom: 20px;
    color: var(--text-dark);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .certificate-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .about-content {
        flex-direction: column;
    }

    .about-text-container {
        width: 100%;
    }

    .stats-container {
        flex-direction: column;
    }

    .stat-box {
        min-width: 100%;
    }

    .team-placeholder {
        height: 300px;
    }

    .certificate-grid {
        grid-template-columns: 1fr;
    }

    .about-cta {
        padding: 30px 20px;
    }
}

/* Contact Form Styles */
.contact-form-container {
    background-color: var(--bg-white);
    border-radius: 8px;
    box-shadow: var(--shadow-light);
    padding: 40px;
    margin: 50px 0;
}

.contact-form-container h2 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.contact-form-container > p {
    color: var(--text-medium);
    margin-bottom: 30px;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.form-group {
    flex: 1;
    min-width: 250px;
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-size: 0.9rem;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 15px;
    border: 1px solid var(--border-medium);
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

.form-group textarea {
    resize: vertical;
}

.error-message {
    color: #e74c3c;
    font-size: 0.9rem;
    margin-top: 5px;
}

.success-message {
    text-align: center;
    padding: 40px 20px;
}

.success-message i {
    font-size: 3rem;
    color: #2ecc71;
    margin-bottom: 20px;
}

.success-message h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.success-message p {
    color: var(--text-medium);
}

@media (max-width: 768px) {
    .contact-form-container {
        padding: 30px 20px;
    }

    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .form-group {
        width: 100%;
    }
}

/* Contact Page Styles */
.contact-page {
    padding: 60px 0;
}

.page-header {
    text-align: center;
    margin-bottom: 50px;
}

.page-title {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--text-dark);
}

.page-description {
    max-width: 800px;
    margin: 0 auto;
    color: var(--text-medium);
    line-height: 1.6;
}

.contact-container {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
}

.contact-info {
    flex: 1;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.info-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-top: 5px;
}

.info-content h3 {
    font-size: 1.2rem;
    margin-bottom: 8px;
    color: var(--text-dark);
}

.info-content p {
    color: var(--text-medium);
    line-height: 1.5;
}

@media (max-width: 992px) {
    .contact-container {
        flex-direction: column;
    }

    .contact-info {
        width: 100%;
    }
}

/* 移除旧的产品详情页样式，使用最新的product-detail-fix.css */

/* Products Page Styles */
.products-page {
    padding: 60px 0;
}

.product-filters {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    margin: 30px 0 50px;
}

.filter-item {
    padding: 10px 20px;
    background-color: var(--bg-light);
    border-radius: 30px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.filter-item:hover,
.filter-item.active {
    background-color: var(--primary-color);
    color: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.product-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    height: 280px;
    display: block;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.product-image {
    height: 100%;
    width: 100%;
    overflow: hidden;
    position: relative;
    background-color: rgba(0, 0, 0, 0.1);
}

.product-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px;
    font-size: 1rem;
    color: white;
    margin: 0;
    text-align: center;
    font-weight: 500;
    z-index: 2;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.6);
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.product-card:hover .product-title {
    opacity: 1;
    transform: translateY(0);
}

.product-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 2;
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0);
    opacity: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
    z-index: 1;
}

.product-card:hover .product-overlay {
    background: rgba(0, 0, 0, 0.5);
}

.view-details-btn {
    padding: 8px 16px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
    transform: translateY(10px);
    opacity: 0;
    transition: all 0.3s ease;
}

.product-card:hover .view-details-btn {
    transform: translateY(0);
    opacity: 1;
}

/* Global Defaults for Product Cards */
.product-card:not(:has(.product-overlay)) .product-image {
    position: relative;
}

.product-card:not(:has(.product-overlay)) .product-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0);
    transition: background-color 0.3s ease;
    z-index: 1;
}

.product-card:not(:has(.product-overlay)):hover .product-image::after {
    background: rgba(0, 0, 0, 0.5);
}

.product-card:not(:has(.product-content)) .product-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px;
    font-size: 1rem;
    color: white;
    margin: 0;
    text-align: center;
    font-weight: 500;
    z-index: 2;
    text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.6);
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    }

.product-card:not(:has(.product-content)):hover .product-title {
    opacity: 1;
    transform: translateY(0);
}

.product-description {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
    margin: 0 0 10px 0;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 15px 15px;
}

/* Footer Styles */
.footer {
    background-color: var(--bg-light);
    color: var(--text-medium);
    border-top: 1px solid var(--border-light);
}

.footer-top {
    padding: 60px 0 40px;
}

.footer-columns {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 30px;
}

.footer-column h4 {
    color: var(--text-dark);
    font-size: 18px;
    margin-bottom: 20px;
    font-weight: 600;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: var(--text-medium);
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-contact li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    font-size: 14px;
}

.footer-contact i {
    margin-right: 10px;
    color: var(--primary-color);
    font-size: 16px;
    margin-top: 2px;
}

.footer-contact a {
    color: var(--text-medium);
    transition: color 0.3s ease;
}

.footer-contact a:hover {
    color: var(--primary-color);
}

.footer-logo {
    margin-bottom: 15px;
}

.footer-description {
    font-size: 14px;
    line-height: 1.6;
}

.footer-bottom {
    padding: 15px 0;
    border-top: 1px solid var(--border-light);
    text-align: center;
}

.footer-bottom .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.copyright {
    font-size: 14px;
    color: var(--text-medium);
}

.footer-social {
    display: flex;
    gap: 10px;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #f0f0f0;
    color: #666;
    transition: all 0.3s ease;
    font-size: 16px;
}

.social-icon:hover {
    background-color: #0a59f7;
    color: white;
    transform: translateY(-3px);
}

@media (max-width: 992px) {
    .footer-columns {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .footer-columns {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-bottom .container {
        flex-direction: column;
        gap: 15px;
    }
}

@media (max-width: 576px) {
    .footer-columns {
        grid-template-columns: 1fr;
    }
}

.nav-item:hover > .nav-link {
    color: rgba(255, 255, 255, 0.9);
}

.has-submenu:hover .submenu {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

/* About Us Page Styles - 移除重叠的样式，使用PageHeader组件 */

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
}

.about-content h2 {
    font-size: 36px;
    color: #0a59f7;
    margin-bottom: 20px;
}

.about-content p {
    margin-bottom: 20px;
    line-height: 1.7;
}

.our-mission {
    background-color: #f5f8fd;
    padding: 60px 0;
}

.section-title {
    font-size: 36px;
    color: #0a59f7;
    margin-bottom: 30px;
    text-align: center;
}

.mission-content {
    max-width: 800px;
    margin: 0 auto 40px;
    text-align: center;
    line-height: 1.7;
}

.mission-content p {
    margin-bottom: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    max-width: 1000px;
    margin: 0 auto;
}

.stat-item {
    text-align: center;
    padding: 30px 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.stat-number {
    font-size: 42px;
    font-weight: 700;
    color: #0a59f7;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 16px;
    color: #333;
}

.team-section {
    padding: 60px 0;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.team-member {
    text-align: center;
}

.member-image img {
    border-radius: 50%;
    margin-bottom: 15px;
}

.member-name {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #0a59f7;
}

.member-title {
    font-size: 16px;
    color: #666;
    margin-bottom: 10px;
}

.member-bio {
    font-size: 14px;
    line-height: 1.6;
    color: #555;
}

.contact-cta {
    background-color: #0a59f7;
    padding: 60px 0;
    text-align: center;
    color: white;
}

.contact-cta h2 {
    font-size: 36px;
    margin-bottom: 15px;
}

.contact-cta p {
    font-size: 18px;
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.btn-primary {
    display: inline-block;
    background-color: white;
    color: #0a59f7;
    font-weight: 600;
    padding: 15px 30px;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #f5f8fd;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Responsive About Page */
@media (max-width: 992px) {
    .about-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .team-grid {
        grid-template-columns: 1fr;
    }

    .page-title {
        font-size: 32px;
    }

    .section-title {
        font-size: 28px;
    }
}

/* Service Pages Shared Styles */
.page-header {
    background-color: #f5f8fd;
    padding: 60px 0 30px;
    text-align: center;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
    margin: 40px 0;
}

.content-text h2 {
    font-size: 32px;
    color: #0a59f7;
    margin-bottom: 20px;
}

.content-text p {
    margin-bottom: 20px;
    line-height: 1.7;
}

/* Services List Grid */
.services-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

@media (min-width: 768px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .services-grid {
        gap: 3rem;
    }
}

.service-card {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.service-content {
    padding: 25px;
}

.service-content h3 {
    font-size: 22px;
    margin-bottom: 15px;
    color: #0a59f7;
}

.service-features {
    margin: 20px 0;
    padding-left: 20px;
}

.service-features li {
    margin-bottom: 8px;
    position: relative;
}

.service-features li:before {
    content: "•";
    color: #0a59f7;
    position: absolute;
    left: -15px;
}

.btn-secondary {
    display: inline-block;
    padding: 10px 20px;
    background-color: transparent;
    color: #0a59f7;
    border: 2px solid #0a59f7;
    border-radius: 4px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    margin-top: 10px;
}

.btn-secondary:hover {
    background-color: #0a59f7;
    color: white;
    transform: translateY(-2px);
}

/* Process Timeline */
.process-timeline {
    margin-top: 40px;
}

.timeline-item {
    display: flex;
    margin-bottom: 30px;
    position: relative;
}

.timeline-number {
    background-color: #0a59f7;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 700;
    margin-right: 20px;
    flex-shrink: 0;
}

.timeline-content h3 {
    font-size: 22px;
    margin-bottom: 10px;
    color: #333;
}

/* Process Steps */
.process-steps {
    margin-top: 40px;
}

.step-item {
    margin-bottom: 60px;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.step-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.step-number {
    background-color: #0a59f7;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 700;
    margin-right: 20px;
}

.step-header h3 {
    font-size: 24px;
    color: #333;
}

.step-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 30px;
    align-items: center;
}

.step-list {
    margin-bottom: 20px;
    padding-left: 20px;
}

.step-list li {
    margin-bottom: 10px;
    position: relative;
}

.step-list li:before {
    content: "•";
    color: #0a59f7;
    position: absolute;
    left: -15px;
}

/* Feature Grids */
.features-grid, .standards-grid, .benefits-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.considerations-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 40px;
}

.feature-item, .standard-item, .benefit-item {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.consideration-item {
    background-color: #fff;
    padding: 25px 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.feature-item:hover, .standard-item:hover, .consideration-item:hover, .benefit-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.feature-icon, .standard-icon, .consideration-icon, .benefit-icon {
    font-size: 36px;
    color: #0a59f7;
    margin-bottom: 20px;
}

.feature-item h3, .standard-item h3, .consideration-item h3, .benefit-item h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #333;
}

/* Case Studies */
.case-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.case-item {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.case-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.case-content {
    padding: 20px;
}

.case-content h3 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #0a59f7;
}

/* Testimonials */
.testimonial-slider {
    margin-top: 40px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.testimonial-item {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    text-align: center;
}

.testimonial-quote p {
    font-size: 18px;
    font-style: italic;
    color: #555;
    margin-bottom: 20px;
    line-height: 1.6;
}

.testimonial-quote h4 {
    font-size: 20px;
    margin-bottom: 5px;
    color: #333;
}

.client-title {
    color: #777;
    font-size: 16px;
}

/* CTA Section */
.cta-section {
    background-color: #0a59f7;
    padding: 60px 0;
    margin: 60px 0 0;
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 32px;
    margin-bottom: 15px;
}

.cta-content p {
    font-size: 18px;
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Quality Standards Features */
.features-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-top: 40px;
    align-items: center;
}

.features-list {
    margin-bottom: 20px;
}

.features-list li {
    margin-bottom: 15px;
    position: relative;
    padding-left: 20px;
}

.features-list li:before {
    content: "•";
    color: #0a59f7;
    position: absolute;
    left: 0;
}

.standards-list {
    padding-left: 20px;
    margin-bottom: 30px;
}

.standards-list li {
    margin-bottom: 15px;
    position: relative;
}

.standards-image {
    margin-top: 30px;
    text-align: center;
}

/* Testing Grid */
.testing-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.testing-item {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.testing-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.testing-number {
    font-size: 24px;
    font-weight: 700;
    color: #0a59f7;
    margin-bottom: 20px;
}

.testing-item h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #333;
}

/* Team Stats */
.team-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 40px;
}

.team-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    line-height: 1.7;
}

/* Resources Section */
.resources-intro {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    line-height: 1.7;
}

.resources-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
}

.resource-item {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.resource-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.resource-icon {
    font-size: 36px;
    color: #0a59f7;
    margin-bottom: 20px;
}

.resource-item h3 {
    font-size: 20px;
    margin-bottom: 15px;
    color: #333;
}

.story-item {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    align-items: center;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .content-grid {
        grid-template-columns: 1fr;
    }

    .services-grid, .features-grid, .standards-grid, .benefits-grid, .testing-grid, .resources-grid, .case-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .considerations-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .team-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .step-content {
        grid-template-columns: 1fr;
    }

    .story-item {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .services-grid, .features-grid, .standards-grid, .benefits-grid, .testing-grid {
        grid-template-columns: 1fr;
    }

    .considerations-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .resources-grid, .case-grid {
        grid-template-columns: 1fr;
    }

    .team-stats {
        grid-template-columns: 1fr;
    }

    .service-card, .feature-item, .standard-item, .consideration-item, .benefit-item, .testing-item, .resource-item {
        max-width: 100%;
    }
}

/* Playground Features Section */
.playground-features {
    padding: 60px 0;
    background-color: var(--bg-white);
    position: relative;
}

.playground-features .section-title {
    color: var(--primary-color);
    margin-bottom: 20px;
}

.playground-features .section-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
    color: var(--text-medium);
    font-size: 16px;
    line-height: 1.7;
}

.features-tabs {
    margin-bottom: 30px;
}

.tab-content {
    position: relative;
}

.tab-item {
    display: flex;
    background-color: var(--bg-light);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.tab-image {
    flex: 1;
    height: 400px;
    position: relative;
    overflow: hidden;
}

.tab-image img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
    transition: transform 0.6s ease;
}

.tab-item:hover .tab-image img {
    transform: scale(1.05);
}

.tab-info {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: white;
}

.tab-info h3 {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-dark);
}

.tab-info h4 {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--primary-color);
    font-weight: 500;
}

.tab-info p {
    color: var(--text-medium);
    font-size: 16px;
    line-height: 1.7;
    margin-bottom: 30px;
}

.features-nav {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 25px;
}

.feature-nav-btn {
    padding: 9px 18px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 30px;
    color: var(--text-medium);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    outline: none;
}

.feature-nav-btn:hover,
.feature-nav-btn.active {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

@media (max-width: 992px) {
    .tab-item {
        flex-direction: column;
    }

    .tab-image {
        height: 300px;
        width: 100%;
    }

    .tab-info {
        padding: 30px;
    }
}

@media (max-width: 768px) {
    .features-nav {
        gap: 8px;
    }

    .feature-nav-btn {
        padding: 8px 14px;
        font-size: 13px;
    }

    .tab-info h3 {
        font-size: 20px;
    }

    .tab-info h4 {
        font-size: 16px;
    }
}

@media (max-width: 576px) {
    .feature-nav-btn {
        padding: 6px 12px;
        font-size: 12px;
    }

    .considerations-grid {
        grid-template-columns: 1fr;
    }
}

/* 设计特性部分改进 */
.design-features {
    padding: 60px 0;
    background-color: #f8f9fd;
}

.design-features .section-title {
    margin-bottom: 40px;
    text-align: center;
}

.design-features .features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.design-features .feature-item {
    background-color: #fff;
    padding: 25px 20px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.design-features .feature-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: rgba(10, 89, 247, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.design-features .feature-icon i {
    font-size: 28px;
    color: #0a59f7;
}

.design-features .feature-item h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    position: relative;
}

.design-features .feature-item h3:after {
    content: '';
    width: 35px;
    height: 3px;
    background-color: #0a59f7;
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 2px;
}

.design-features .feature-item p {
    color: #666;
    line-height: 1.5;
    font-size: 14px;
}

/* 设计特性部分响应式样式 */
@media (max-width: 1200px) {
    .design-features .features-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
    }

    .design-features .feature-item {
        padding: 20px 15px;
    }
}

@media (max-width: 992px) {
    .design-features .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .design-features .feature-item {
        padding: 25px 20px;
    }
}

@media (max-width: 576px) {
    .design-features .features-grid {
        grid-template-columns: 1fr;
    }

    .design-features .feature-item {
        padding: 25px 20px;
    }
}

/* 浮动按钮样式 */
.floating-buttons {
    position: fixed;
    right: 20px;
    bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    z-index: 999;
}

.floating-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    color: white;
    font-size: 24px;
    border: none;
}

.floating-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
}

.whatsapp-btn {
    background-color: #25D366;
}

.email-btn {
    background-color: #FF9F40;
}

.top-btn {
    background-color: #9E9E9E;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s, transform 0.3s ease;
}

.top-btn.visible {
    opacity: 1;
    visibility: visible;
}

@media (max-width: 768px) {
    .floating-btn {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .floating-buttons {
        right: 15px;
        bottom: 15px;
        gap: 12px;
    }
}

/* 添加container-fluid样式 */
.container-fluid {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

/* 修改section-title样式使其在全宽容器下居中 */
.factory-section .section-title {
    text-align: center;
    margin-bottom: 15px;
}

/* 调整轮播图容器布局 */
.factory-sliders-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: center;
    margin: 40px auto;
    width: 96%; /* 占据屏幕96%的宽度 */
    max-width: 100%; /* 使用全屏宽度 */
    padding: 0;
}

.factory-slider {
    position: relative;
    width: calc(50% - 15px);
    min-width: 500px; /* 增加最小宽度 */
    max-width: 800px; /* 限制最大宽度 */
    overflow: hidden;
}

/* 响应式调整 */
@media (max-width: 1800px) {
    .factory-sliders-container {
        width: 98%;
    }

    .factory-slider {
        min-width: 450px;
    }
}

@media (max-width: 1200px) {
    .factory-slider {
        min-width: 400px;
    }
}

@media (max-width: 992px) {
    .factory-slider {
        width: 94%;
        min-width: 300px;
        margin: 0 auto 30px;
    }
}

@media (max-width: 768px) {
    .factory-slides {
        height: 380px;
    }

    .factory-arrow {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .factory-slides {
        height: 300px;
    }

    .factory-slider-arrows {
        padding: 0 10px;
    }

    .factory-arrow {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }
}

@media (max-width: 1200px) {
    .slide-image {
        -o-object-position: center top;
           object-position: center top; /* Adjust position for smaller screens */
    }
}

/* 现代化产品网格样式 - 最高优先级 */ /* 现代化产品网格样式 - 最后导入确保最高优先级 */
/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/global-quote-form.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/* 全局引用表单样式 */

/* 表单容器基础样式 */
.quote-form-container {
  position: relative;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin: 10rem auto 2rem auto !important;
  max-width: 800px;
  width: 100%;
  overflow: hidden;
}

/* 产品页面特定样式 - 使用更高优先级 */
.products-page-quote-form.quote-form-container {
  background-color: #fff !important;
  margin: 3rem auto !important;
  max-width: 800px !important;
  display: block !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

/* 表单内部容器 */
.form-inner {
  position: relative;
  z-index: 1;
  width: 100%;
}

/* 表单标题 */
.quote-form-container h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1.5rem;
  text-align: center;
}

@media (min-width: 768px) {
  .quote-form-container h3 {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

/* 表单网格布局 */
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

@media (max-width: 640px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .quote-form-container {
    max-width: 95%;
    padding: 1.5rem;
    margin: 1rem auto !important;
  }
}

/* 输入框样式 */
.quote-form-container input,
.quote-form-container textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background-color: #f9fafb;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.quote-form-container input:focus,
.quote-form-container textarea:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.quote-form-container textarea {
  resize: vertical;
  min-height: 100px;
}

/* 按钮样式 */
.quote-form-container button {
  width: 100%;
  background-color: #4f46e5;
  color: white;
  font-weight: 600;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
  margin-top: 1rem;
}

.quote-form-container button:hover {
  background-color: #4338ca;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.quote-form-container button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

/* 隐私声明样式 */
.privacy-note {
  font-size: 0.8rem;
  color: #6b7280;
  text-align: center;
  margin-top: 1rem;
}
