{"name": "infinity-playground-clone", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:network": "node start-network.js", "debug": "node scripts/start-with-debug.js", "build": "next build", "start": "next start", "lint": "next lint", "migrate-api": "node scripts/migrate-api-routes.js", "migrate-pages": "node scripts/migrate-pages-routes.js", "check-routes": "node scripts/check-route-conflicts.js", "organize-scripts": "node scripts/organize-scripts.js", "reorganize-assets": "node scripts/reorganize-product-assets.js", "normalize-public": "node scripts/normalize-public-folders.js", "setup-linting": "node scripts/setup-linting.js", "remove-duplicates": "node scripts/remove-duplicate-routes.js", "format": "prettier --write '**/*.{js,jsx,ts,tsx,json,md}'", "clean": "node scripts/clear-cache.js", "clean:all": "node scripts/clear-cache.js --all", "fix-high-contrast": "node scripts/upgrade-high-contrast.js", "init-admin": "node scripts/init-admin.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.802.0", "@heroicons/react": "^2.2.0", "@nauverse/react-aurora-background": "^1.0.12", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-slot": "^1.2.3", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.11.14", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.3", "dotenv": "^16.5.0", "form-data": "^4.0.2", "formidable": "^3.5.4", "framer-motion": "^12.12.1", "glob": "^11.0.2", "lucide-react": "^0.511.0", "next": "latest", "next-auth": "^4.24.11", "node-fetch": "^2.7.0", "nodemailer": "^7.0.3", "ogl": "^1.0.11", "pg": "^8.16.0", "react": "latest", "react-dom": "latest", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.56.1", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0"}, "devDependencies": {"@stagewise/toolbar-next": "^0.1.2", "@types/bcrypt": "^5.0.2", "@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.21", "critters": "^0.0.23", "eslint": "^9.27.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "open": "^10.1.2", "postcss": "^8.5.3", "prettier": "^2.8.8", "rimraf": "^5.0.10", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}