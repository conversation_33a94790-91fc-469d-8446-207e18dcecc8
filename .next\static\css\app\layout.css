/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[11].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Poppins","arguments":[{"subsets":["latin"],"weight":["300","400","500","600","700"],"display":"swap","variable":"--font-poppins"}],"variableName":"poppins"} ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* devanagari */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/6c177e25b87fd9cd-s.woff2) format('woff2');
  unicode-range: U+0900-097F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}
/* latin-ext */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/6c9a125e97d835e1-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(/_next/static/media/4c285fdca692ea22-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* devanagari */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/034d78ad42e9620c-s.woff2) format('woff2');
  unicode-range: U+0900-097F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}
/* latin-ext */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/fe0777f1195381cb-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(/_next/static/media/eafabf029ad39a43-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* devanagari */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/db911767852bc875-s.woff2) format('woff2');
  unicode-range: U+0900-097F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}
/* latin-ext */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/f10b8e9d91f3edcb-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url(/_next/static/media/8888a3826f4a3af4-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* devanagari */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/29e7bbdce9332268-s.woff2) format('woff2');
  unicode-range: U+0900-097F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}
/* latin-ext */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/c3bc380753a8436c-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url(/_next/static/media/0484562807a97172-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* devanagari */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/29a4aea02fdee119-s.woff2) format('woff2');
  unicode-range: U+0900-097F, U+1CD0-1CF9, U+200C-200D, U+20A8, U+20B9, U+20F0, U+25CC, U+A830-A839, U+A8E0-A8FF, U+11B00-11B09;
}
/* latin-ext */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/a1386beebedccca4-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Poppins_51684b';
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url(/_next/static/media/b957ea75a84b6ea7-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Poppins_Fallback_51684b';src: local("Arial");ascent-override: 92.33%;descent-override: 30.78%;line-gap-override: 8.79%;size-adjust: 113.73%
}.__className_51684b {font-family: '__Poppins_51684b', '__Poppins_Fallback_51684b';font-style: normal
}.__variable_51684b {--font-poppins: '__Poppins_51684b', '__Poppins_Fallback_51684b'
}

/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/header-dark.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/* 黑色导航栏样式 */

/* 基础样式，确保定位正确 */
.header-main {
  position: fixed; /* 改为固定定位，确保始终在顶部 */
  top: 0; /* 确保定位在页面顶部 */
  left: 0; /* 确保水平铺满 */
  width: 100%; /* 确保宽度100% */
  z-index: 9999 !important; /* 使用非常高的z-index确保在其他元素之上 */
  overflow: visible; /* 修改overflow为visible */
  padding: 10px 0 !important; /* 减少内边距 */
  margin: 0 !important; /* 移除所有外边距 */
  background: transparent; /* 完全透明背景 */
}

/* 文字Logo样式 */
.text-logo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 300;
  letter-spacing: 3px;
  color: #ffffff;
  background: linear-gradient(135deg, #fff 0%, #e0e0e0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  text-transform: uppercase;
}

/* 中文logo样式优化 */
:lang(zh) .logo-text {
  font-size: 1.6rem;
  letter-spacing: 2px;
  font-weight: 300;
  text-transform: none;
}

:lang(zh) .logo-tag {
  font-size: 0.8rem;
  letter-spacing: 2px;
}

.logo-tag {
  font-size: 0.7rem;
  font-weight: 300;
  letter-spacing: 3px;
  color: #3498db;
  margin-top: 2px;
  position: relative;
  padding-left: 2px;
}

/* 固定状态的导航栏背景色 - 稍微增加透明度 */
.header-main.sticky {
  background: linear-gradient(135deg, rgba(18, 18, 18, 0.8) 0%, rgba(26, 26, 26, 0.8) 50%, rgba(18, 18, 18, 0.8) 100%) !important;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);
  padding: 10px 0; /* 减少内边距 */
  margin-bottom: 0; /* 移除底部外边距 */
}

/* 透明状态的导航栏背景渐变 - 完全透明 */
.header-main.transparent {
  background: transparent; /* 完全透明 */
  padding: 15px 0; /* 减少内边距 */
  margin-bottom: 0; /* 移除底部外边距 */
}

/* 添加微妙的光泽效果 */
.header-main.sticky::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

/* 添加背景纹理效果 */
.header-main.sticky::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.05;
  background-image:
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 15%),
    radial-gradient(circle at 80% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 15%);
  pointer-events: none;
}

/* 导航链接增强 */
.nav-link {
  position: relative;
  transition: color 0.3s ease !important;
}

.nav-link::after {
  content: '';
  position: absolute;
  left: 10px;
  right: 10px;
  bottom: 6px;
  height: 1px;
  background-color: rgba(255, 255, 255, 0.7);
  transform: scaleX(0);
  transition: transform 0.4s ease;
  transform-origin: center;
}

.nav-link:hover::after {
  transform: scaleX(0.6);
}

/* 按钮样式增强 */
.btn-quote {
  background-image: linear-gradient(to right, #e9e9e9, #ffffff, #e9e9e9) !important;
  transition: all 0.3s ease, transform 0.2s ease !important;
}

.btn-quote:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* 适配移动设备 */
@media (max-width: 992px) {
  .header-main.sticky {
    background: linear-gradient(135deg, rgba(18, 18, 18, 0.9) 0%, rgba(26, 26, 26, 0.9) 50%, rgba(18, 18, 18, 0.9) 100%) !important;
  }

  .mobile-menu {
    background: linear-gradient(180deg, #121212 0%, #171717 100%);
  }

  .mobile-nav-link {
    color: #f5f5f5;
    font-weight: 300;
    letter-spacing: 1.2px;
    text-transform: uppercase;
  }

  .mobile-submenu {
    background-color: #1a1a1a;
    border-left: 1px solid rgba(255, 255, 255, 0.05);
  }

  .logo-text {
    font-size: 1.3rem;
  }

  .logo-tag {
    font-size: 0.6rem;
  }

  :lang(zh) .logo-text {
    font-size: 1.4rem;
  }

  :lang(zh) .logo-tag {
    font-size: 0.7rem;
  }
}
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/products.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/* Products Page Styles - Unified with Homepage Design */
.products-page {
  padding: 100px 0 60px;
  margin-top: 30px;
  position: relative;
  background: linear-gradient(to bottom, #f5f9ff 0%, #ffffff 300px);
}

/* 页面标题区域 */
.page-banner {
  /* background-image: url('/images/products/slide1.jpg') !important; */ /* <-- 注释掉此行 */
  background-size: cover !important;
  background-position: center top !important;
  padding: 150px 0 100px !important;
  color: white !important;
  position: relative !important;
  overflow: hidden !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
  margin-bottom: 50px !important; /* 增加与下方内容的间距 */
  margin-top: 0 !important; /* 移除顶部外边距，让banner紧贴导航栏 */
}

.page-banner:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(120deg, rgba(10, 89, 247, 0.5), rgba(26, 26, 46, 0.6)) !important;
  z-index: 1;
}

.page-banner:after {
  content: '';
  position: absolute;
  bottom: -50px;
  right: -50px;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  z-index: 1;
}

.page-banner .container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 页面内容与顶部横幅分隔线 */
.products-page:before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 6px;
  background: linear-gradient(90deg, transparent, rgba(26, 101, 227, 0.3), transparent);
  border-radius: 3px;
  z-index: 2;
}

.page-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  letter-spacing: 1px;
  color: white;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  animation: fadeInDown 0.8s ease-out;
}

.breadcrumb {
  display: flex;
  gap: 10px;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  align-items: center;
  animation: fadeIn 1s ease-out;
}

.breadcrumb a {
  color: rgba(255, 255, 255, 0.9);
  transition: color 0.3s;
}

.breadcrumb a:hover {
  color: white;
}

.breadcrumb .separator {
  color: rgba(255, 255, 255, 0.7);
  margin: 0 5px;
}

.page-header {
  text-align: center;
  margin-bottom: 50px;
  position: relative;
  padding-top: 20px;
}

.page-description {
  font-size: 1.1rem;
  max-width: 800px;
  margin: 0 auto 30px;
  color: #556;
  font-weight: 300;
  line-height: 1.8;
}

/* 产品筛选区域 */
.product-filters {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: 30px 0 50px;
  gap: 20px;
}

.filter-item {
  background-color: #f0f4f8;
  border: 1px solid #ddd;
  border-radius: 8px;
  cursor: pointer;
  padding: 12px 28px;
  font-weight: 500;
  font-size: 1.05rem;
  color: #333;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.filter-item.active {
  background-color: #1a65e3;
  color: white;
  box-shadow: 0 5px 15px rgba(26, 101, 227, 0.2);
}

/* 产品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 50px;
  margin-bottom: 60px;
  padding: 20px;
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 40px;
    padding: 15px;
  }
}

@media (max-width: 576px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 30px;
    padding: 10px;
  }
}

.product-card {
  background-color: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(26, 26, 46, 0.08);
  transition: transform 0.4s cubic-bezier(0.19, 1, 0.22, 1), box-shadow 0.4s cubic-bezier(0.19, 1, 0.22, 1);
  border: 1px solid rgba(26, 26, 46, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 380px;
  justify-self: center;
  margin: 0 auto;
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(26, 26, 46, 0.12);
}

.product-image {
  height: 280px !important;
  overflow: hidden;
  position: relative;
  width: 100%;
}

.product-image img, .product-img {
  width: 100% !important;
  height: 100% !important;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.7s cubic-bezier(0.19, 1, 0.22, 1);
}

.product-card:hover .product-image img,
.product-card:hover .product-img {
  transform: scale(1.05);
}

.card-link {
  display: flex;
  flex-direction: column;
  height: 100%;
  color: inherit;
  text-decoration: none;
}

.product-content {
  padding: 30px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.product-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
  padding: 1.2rem;
  color: var(--text-dark);
  line-height: 1.4;
  transition: color 0.3s;
  text-align: center;
  border-top: 1px solid rgba(0,0,0,0.05);
  min-height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-card:hover .product-title {
  color: #1a65e3;
}

.product-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.view-details-btn {
  background-color: #1a65e3;
  color: white;
  padding: 10px 20px;
  border-radius: 30px;
  font-weight: 500;
  transform: translateY(20px);
  transition: transform 0.3s ease;
  box-shadow: 0 5px 15px rgba(26, 101, 227, 0.3);
}

.product-card:hover .view-details-btn {
  transform: translateY(0);
}

.product-description {
  color: var(--text-medium);
  font-weight: 300;
  line-height: 1.6;
  margin-bottom: 20px;
  flex-grow: 1;
}

.product-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: auto;
}

.product-size, .product-type {
  font-size: 0.85rem;
  padding: 5px 10px;
  border-radius: 4px;
  display: inline-block;
}

.product-size {
  background-color: rgba(26, 101, 227, 0.1);
  color: #1a65e3;
}

.product-type {
  background-color: rgba(20, 184, 116, 0.1);
  color: #14b874;
}

.product-link {
  display: inline-flex;
  align-items: center;
  margin-top: 15px;
  color: #1a65e3;
  font-weight: 500;
  transition: all 0.3s;
}

.product-link i {
  margin-left: 8px;
  transition: transform 0.3s;
}

.product-link:hover {
  color: #0a4bc1;
}

.product-link:hover i {
  transform: translateX(3px);
}

/* 产品详情页 */
.product-detail {
  padding: 60px 0 80px;
}

.product-detail .container {
  max-width: 1200px;
}

.product-header {
  margin-bottom: 50px;
  text-align: center;
}

.product-title {
  font-size: 2.5rem;
  font-weight: 400;
  margin-bottom: 15px;
  color: var(--text-dark);
}

.product-description {
  font-size: 1.1rem;
  max-width: 800px;
  margin: 0 auto;
  color: var(--text-medium);
  font-weight: 300;
  line-height: 1.8;
}

.product-gallery {
  margin-bottom: 60px;
}

.gallery-main {
  height: 500px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 15px 40px rgba(26, 26, 46, 0.1);
  margin-bottom: 20px;
}

.main-image {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.gallery-thumbnails {
  display: flex;
  gap: 15px;
  padding-bottom: 10px;
}

.thumbnail {
  width: 100px;
  height: 100px;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 5px 15px rgba(26, 26, 46, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  flex-shrink: 0;
  border: 2px solid transparent;
}

.thumbnail.active {
  border-color: #1a65e3;
}

.thumbnail:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(26, 26, 46, 0.15);
}

.thumbnail img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.product-info-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 50px;
}

.product-details h2,
.product-inquiry h2 {
  font-size: 1.8rem;
  margin-bottom: 25px;
  color: var(--text-dark);
  font-weight: 300;
  position: relative;
  padding-bottom: 15px;
}

.product-details h2:after,
.product-inquiry h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, rgba(26, 26, 46, 0.5), #1a65e3, rgba(26, 26, 46, 0.1));
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.detail-item {
  background-color: #f9f9fd;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(26, 26, 46, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.detail-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(26, 26, 46, 0.08);
}

.detail-item h3 {
  font-size: 1.1rem;
  margin-bottom: 10px;
  color: #1a65e3;
  font-weight: 500;
}

.detail-item p {
  color: var(--text-medium);
  font-weight: 300;
}

.product-inquiry {
  background-color: #f9f9fd;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(26, 26, 46, 0.08);
}

.product-inquiry p {
  color: var(--text-medium);
  margin-bottom: 25px;
  font-weight: 300;
  line-height: 1.7;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .gallery-main {
    height: 450px;
  }
}

@media (max-width: 992px) {
  .product-info-container {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .gallery-main {
    height: 400px;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .page-title {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .page-banner {
    padding: 60px 0 40px;
  }

  .page-title {
    font-size: 2rem;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
  }

  .product-image {
    height: 180px;
  }

  .product-content {
    padding: 20px;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .gallery-main {
    height: 300px;
  }

  .product-title {
    font-size: 1.8rem;
  }
}

@media (max-width: 576px) {
  .products-grid {
    grid-template-columns: 1fr;
  }

  .product-image {
    height: 220px;
  }

  .page-title {
    font-size: 1.8rem;
  }

  .page-description {
    font-size: 1rem;
  }

  .product-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .filter-item {
    width: 100%;
    text-align: center;
  }

  .main-media {
    height: 280px;
  }
}

/* Collection Page Specific Styles */
.collection-page {
  padding: 60px 0;
}

.category-intro {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin: 50px 0;
}

.intro-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.intro-content h2 {
  margin-top: 0;
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 20px;
}

.intro-content p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.benefits-list {
  padding-left: 20px;
}

.benefits-list li {
  margin-bottom: 10px;
  color: #444;
}

.intro-image {
  border-radius: 8px;
  overflow: hidden;
  height: 350px;
}

.collection-filters {
  background-color: #f5f5f5;
  padding: 25px;
  border-radius: 8px;
  margin-bottom: 40px;
}

.collection-filters h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.2rem;
  color: #333;
}

.filter-options {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.filter-group {
  margin-bottom: 15px;
}

.filter-group h4 {
  margin: 0 0 10px 0;
  font-size: 1rem;
  color: #555;
}

.filter-option {
  display: inline-block;
  padding: 6px 12px;
  background-color: white;
  border-radius: 20px;
  margin-right: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  font-size: 0.9rem;
  color: #444;
  border: 1px solid #ddd;
  transition: all 0.2s ease;
}

.filter-option:hover {
  background-color: #0a59f7;
  color: white;
  border-color: #0a59f7;
}

.space-optimization, .business-benefits, .large-venue-advantages {
  background-color: #f9f9f9;
  padding: 40px;
  border-radius: 8px;
  margin: 50px 0;
}

.space-optimization h2, .business-benefits h2, .large-venue-advantages h2 {
  margin-top: 0;
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}

.strategy-content, .benefits-content, .advantages-content {
  max-width: 900px;
  margin: 0 auto;
}

.benefits-content, .advantages-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
}

.benefit-item, .advantage-item {
  background-color: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.benefit-item h3, .advantage-item h3 {
  margin-top: 0;
  font-size: 1.2rem;
  color: #0a59f7;
  margin-bottom: 15px;
}

.cta-section {
  text-align: center;
  background-color: #0a59f7;
  padding: 60px;
  border-radius: 8px;
  color: white;
}

.cta-section h2 {
  margin-top: 0;
  font-size: 2rem;
  margin-bottom: 20px;
}

.cta-section p {
  font-size: 1.1rem;
  margin-bottom: 30px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.btn-primary {
  display: inline-block;
  padding: 12px 30px;
  background-color: white;
  color: #0a59f7;
  border-radius: 30px;
  font-weight: bold;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .category-intro {
    grid-template-columns: 1fr;
  }

  .intro-image {
    order: -1;
    height: 300px;
  }

  .space-optimization, .business-benefits, .large-venue-advantages {
    padding: 30px 20px;
  }

  .cta-section {
    padding: 40px 20px;
  }
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }

  .product-image {
    height: 180px;
  }

  .benefits-content, .advantages-content {
    grid-template-columns: 1fr;
  }

  .page-title {
    font-size: 2rem;
  }
}

.product-features {
  margin-top: 30px;
}

.product-features h3 {
  font-size: 1.4rem;
  margin-bottom: 20px;
  color: var(--text-dark);
  font-weight: 300;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-top: 20px;
}

.features-list li {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-medium);
  font-weight: 300;
  line-height: 1.6;
}

.features-list li i {
  color: #1a65e3;
  font-size: 1.1rem;
}

/* 相关产品部分 */
.related-products {
  margin-top: 80px;
  padding-top: 60px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.related-products h2 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 40px;
  color: var(--text-dark);
  font-weight: 300;
  position: relative;
  padding-bottom: 15px;
}

.related-products h2:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, rgba(26, 26, 46, 0.2), #1a65e3, rgba(26, 26, 46, 0.2));
}

.related-products-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

@media (max-width: 1200px) {
  .related-products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .related-products-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .features-list {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 576px) {
  .related-products-grid {
    grid-template-columns: 1fr;
  }

  .related-products h2 {
    font-size: 1.6rem;
  }
}

/* 产品列表页样式 */
.products-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  padding: 40px 0;
}

/* 更新淘宝风格产品详情页样式 */
.product-gallery-taobao {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
}

.thumb-gallery {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 80px;
}



.thumbnail-item {
  width: 80px;
  height: 80px;
  border: 2px solid #e1e1e1;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.thumbnail-item.active {
  border-color: #1a65e3;
  box-shadow: 0 0 8px rgba(26, 101, 227, 0.4);
}

.thumbnail-item:hover {
  border-color: #1a65e3;
  transform: translateY(-2px);
}

.play-icon {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  font-size: 24px;
}

.main-media {
  flex: 1;
  height: 450px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  background-color: #fafafa;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.main-image, .main-video {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}

/* 产品详情图片样式 */
.product-details-section {
  padding: 40px 0;
  background-color: #fafafa;
}

.details-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 30px;
}

.tab {
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab:hover {
  color: #1a65e3;
}

.tab.active {
  color: #1a65e3;
  border-bottom-color: #1a65e3;
}

.details-content {
  padding: 20px 0;
}

.detail-images {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.detail-image {
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 响应式样式 */
@media (max-width: 992px) {
  .product-main-content {
    flex-direction: column;
  }

  .product-gallery-container {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .product-gallery-taobao {
    flex-direction: column-reverse;
  }

  .thumb-gallery {
    flex-direction: row;
    width: 100%;
    max-height: none;
    padding-bottom: 10px;
  }

  .main-media {
    height: 350px;
  }

  .tab {
    padding: 10px 15px;
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .main-media {
    height: 280px;
  }

  .product-title {
    font-size: 20px;
  }

  .price-value {
    font-size: 20px;
  }
}

/* 添加动画关键帧 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 产品列表页面样式 */

.product-list-page {
  position: relative;
}

/* 产品页面表单样式 - 简化版本 */
.products-page-quote-form {
  display: block !important;
}
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/quality-control.css ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
 /* Quality Control Page Styles */
.quality-control-page {
  display: flex;
  flex-direction: column;
}
.page-banner {
  background-image: url('/images/holographic-tech-bg.jpg');
  background-size: cover;
  background-position: center;
  padding: 80px 0 60px;
  text-align: center;
  position: relative;
  color: white;
}

.page-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(10, 89, 247, 0.8), rgba(26, 26, 46, 0.9));
  z-index: 1;
}

.page-banner .container {
  position: relative;
  z-index: 2;
}

.quality-intro {
  padding: 60px 0;
  background-color: #f8f9fd;
  margin-top: 0;
}

.quality-process {
  padding: 60px 0;
  background-color: #fff;
}

.quality-process .section-title {
  text-align: center;
  margin-bottom: 40px;
  color: #0a59f7;
  font-size: 32px;
}

.process-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  margin-top: 40px;
}

.process-item {
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
  border: 1px solid #eee;
}

.process-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.process-icon {
  font-size: 36px;
  color: #0a59f7;
  margin-bottom: 20px;
}

.process-item h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
  font-weight: 600;
}

.process-item p {
  color: #666;
  line-height: 1.6;
}

/* Responsive styles */
@media (max-width: 992px) {
  .process-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .process-grid {
    grid-template-columns: 1fr;
  }

  .quality-process .section-title {
    font-size: 28px;
  }
}

/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/banner-fix.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/* 覆盖产品页banner样式，移除所有渐变效果，实现透明导航 */

.page-banner {
  background-size: cover !important; /* 使用cover而不是100% 100%，避免图片变形 */
  background-position: center top !important; /* 改为顶部对齐 */
  background-repeat: no-repeat !important;
  padding: 180px 0 120px !important; /* 增加上下内边距，创造更多空间 */
  color: white !important;
  position: relative !important;
  overflow: hidden !important;
  margin-bottom: 0 !important;
  margin-top: -150px !important; /* 进一步增加负边距，让横幅向上移动更多 */
  width: 100vw !important; /* 占据整个视口宽度 */
  max-width: 100vw !important;
  margin-left: calc(50% - 50vw) !important; /* 向左延伸到屏幕边缘 */
  margin-right: calc(50% - 50vw) !important; /* 向右延伸到屏幕边缘 */
  left: 0 !important;
  right: 0 !important;
  top: 0 !important; /* 确保从顶部开始 */
}

/* 移除所有渐变覆盖层 */
.page-banner:before {
  display: none !important;
}

.page-banner:after {
  display: none !important;
}

/* 确保文字容器位于正确位置 */
.page-banner .container {
  position: relative;
  z-index: 2;
  background-color: transparent !important; /* 确保容器背景透明 */
}

/* 页面标题样式 - 增强可见性 */
.page-title {
  font-size: 3rem !important;
  font-weight: 700 !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8), 0 0 10px rgba(0, 0, 0, 0.5) !important;
  color: white !important;
  letter-spacing: 1px !important;
  margin-bottom: 1rem !important;
}

/* 面包屑导航样式 - 透明背景 */
.breadcrumb {
  display: flex !important;
  align-items: center !important;
  background-color: transparent !important; /* 移除白色背景 */
  padding: 0.5rem 0 !important;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8), 0 0 5px rgba(0, 0, 0, 0.5) !important;
  font-weight: 500 !important;
  font-size: 1.1rem !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* 面包屑导航链接样式 */
.breadcrumb a {
  color: white !important;
  text-decoration: none !important;
  transition: opacity 0.3s ease !important;
  position: relative !important;
}

.breadcrumb a:hover {
  opacity: 0.8 !important;
}

/* 面包屑分隔符样式 */
.breadcrumb .separator {
  margin: 0 0.5rem !important;
  color: rgba(255, 255, 255, 0.7) !important;
}

/* 修复页面间隙问题 */
body {
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden !important;
}

section {
  margin: 0 !important;
}

.products-page {
  margin-top: 40px !important; /* 增加顶部外边距，与导航栏分离 */
  padding-top: 60px !important; /* 增加顶部内边距 */
}
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/ms-fix.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/* 使用现代的Forced Colors Mode标准 */

/*
 * 完全移除-ms-high-contrast媒体查询
 * 使用现代的forced-colors媒体查询替代
 */

/* 使用现代的forced-colors媒体查询 */
@media (forced-colors: active) {
  /* 基本覆盖 */
  a:focus {
    outline: 2px solid ButtonText !important;
    outline-offset: 2px !important;
  }

  /* 按钮和交互元素 */
  button:focus,
  input:focus,
  textarea:focus,
  select:focus {
    outline: 2px solid ButtonText !important;
    outline-offset: 2px !important;
  }

  /* 确保链接在高对比度模式下可见 */
  a {
    color: LinkText !important;
  }

  /* 确保按钮在高对比度模式下可见 */
  button,
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    color: ButtonText !important;
    background-color: ButtonFace !important;
    border: 1px solid ButtonText !important;
  }

  /* 确保图标在高对比度模式下可见 */
  svg, img {
    forced-color-adjust: auto !important;
  }

  /* 确保表单元素在高对比度模式下可见 */
  input, textarea, select {
    border-color: ButtonText !important;
    background-color: Field !important;
    color: FieldText !important;
  }

  /* 确保复选框和单选按钮在高对比度模式下可见 */
  input[type="checkbox"],
  input[type="radio"] {
    color: ButtonText !important;
    border-color: ButtonText !important;
  }
}
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/high-contrast-override.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/**
 * 全局覆盖所有-ms-high-contrast媒体查询
 * 这个文件用于解决第三方库中使用-ms-high-contrast的问题
 */

/* 
 * 使用!important覆盖所有可能的-ms-high-contrast媒体查询
 * 这是一个更强力的解决方案，确保所有第三方库的-ms-high-contrast媒体查询都被覆盖
 */

/* 覆盖所有forced-colors: active媒体查询 */
@media (forced-colors: active) {
  * {
    /* 重置所有可能的样式 */
    background-color: initial !important;
    color: initial !important;
    border-color: initial !important;
    outline-color: initial !important;
    text-shadow: initial !important;
    box-shadow: initial !important;
  }
}

/* 覆盖所有forced-colors: black-on-white媒体查询 */
@media (forced-colors: black-on-white) {
  * {
    /* 重置所有可能的样式 */
    background-color: initial !important;
    color: initial !important;
    border-color: initial !important;
    outline-color: initial !important;
    text-shadow: initial !important;
    box-shadow: initial !important;
  }
}

/* 覆盖所有forced-colors: white-on-black媒体查询 */
@media (forced-colors: white-on-black) {
  * {
    /* 重置所有可能的样式 */
    background-color: initial !important;
    color: initial !important;
    border-color: initial !important;
    outline-color: initial !important;
    text-shadow: initial !important;
    box-shadow: initial !important;
  }
}

/* 使用现代的forced-colors媒体查询提供正确的高对比度样式 */
@media (forced-colors: active) {
  /* 基本元素样式 */
  body {
    color: CanvasText;
    background-color: Canvas;
  }
  
  /* 链接样式 */
  a {
    color: LinkText;
  }
  
  a:visited {
    color: VisitedText;
  }
  
  /* 按钮样式 */
  button, 
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    color: ButtonText;
    background-color: ButtonFace;
    border: 1px solid ButtonText;
  }
  
  /* 输入框样式 */
  input, 
  textarea, 
  select {
    color: FieldText;
    background-color: Field;
    border: 1px solid FieldText;
  }
  
  /* 图标和图片 */
  svg, 
  img {
    forced-color-adjust: auto;
  }
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/ms-high-contrast-blocker.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/**
 * Complete blocker for -ms-high-contrast media queries
 * This file uses a CSS hack to completely prevent any -ms-high-contrast media queries from being processed
 */

/* 
 * This technique uses a CSS hack to completely disable -ms-high-contrast media queries
 * by creating a higher-specificity rule that overrides all possible -ms-high-contrast styles
 */

/* First, create a dummy element with extremely high specificity */
html body div#ms-high-contrast-blocker {
  display: none !important;
}

/* Then use attribute selectors to create even higher specificity */
html[lang] body[class] div[id="ms-high-contrast-blocker"] {
  display: none !important;
}

/* Now override all -ms-high-contrast media queries with empty rules */
@media (forced-colors: active), 
       (forced-colors: black-on-white), 
       (forced-colors: white-on-black),
       (forced-colors: none) {
  
  /* Create an extremely high-specificity selector that will override all other rules */
  html[lang] body[class] *,
  html[lang] body[class] *::before,
  html[lang] body[class] *::after {
    /* 不再重置所有属性，允许正常深色模式工作 */
    /* 仅处理高对比度相关属性 */
    forced-color-adjust: none !important;
  }
}

/* Add the modern forced-colors media query for proper high contrast support */
@media (forced-colors: active) {
  /* Basic elements */
  body {
    color: CanvasText;
    background-color: Canvas;
  }
  
  /* Links */
  a {
    color: LinkText;
  }
  
  a:visited {
    color: VisitedText;
  }
  
  /* Buttons */
  button, 
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    color: ButtonText;
    background-color: ButtonFace;
    border: 1px solid ButtonText;
  }
  
  /* Form elements */
  input, 
  textarea, 
  select {
    color: FieldText;
    background-color: Field;
    border: 1px solid FieldText;
  }
  
  /* Images and SVGs */
  img, 
  svg {
    forced-color-adjust: auto;
  }
}

/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/ms-high-contrast-killer.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/**
 * Complete killer for -ms-high-contrast media queries
 * This file uses multiple CSS hacks to completely prevent any -ms-high-contrast media queries
 * from being processed or causing deprecation warnings
 */

/*
 * Technique 1: Use @supports to create a rule that will always be applied
 * and override any -ms-high-contrast media queries
 */
@supports (display: block) {
  /* This will match all elements */
  * {
    /* Reset any properties that might be set in -ms-high-contrast media queries */
    forced-color-adjust: none !important;
  }
}

/*
 * Technique 1.5: Use !important to ensure our overrides take precedence
 */
* {
  forced-color-adjust: none !important;
}

/*
 * Technique 2: Create empty media queries that match the deprecated ones
 * This tricks the browser into thinking they're already handled
 */
@media (forced-colors: active) { #ms-high-contrast-blocker {} }
@media (forced-colors: black-on-white) { #ms-high-contrast-blocker {} }
@media (forced-colors: white-on-black) { #ms-high-contrast-blocker {} }
@media (forced-colors: none) { #ms-high-contrast-blocker {} }

/*
 * Technique 3: Use a more specific selector to override any styles
 * that might be set in -ms-high-contrast media queries
 */
html body * {
  forced-color-adjust: none !important;
}

/*
 * Technique 4: Use @media all to create a rule that will always be applied
 * and override any -ms-high-contrast media queries
 */
@media all {
  @media (forced-colors: active),
         (forced-colors: black-on-white),
         (forced-colors: white-on-black),
         (forced-colors: none) {
    /* This empty rule will be processed but won't affect styling */
    #ms-high-contrast-blocker {}
  }
}

/*
 * Technique 5: Use !important to override any styles that might be set
 * in -ms-high-contrast media queries
 */
@media (forced-colors: active),
       (forced-colors: black-on-white),
       (forced-colors: white-on-black),
       (forced-colors: none) {
  * {
    forced-color-adjust: none !important;
  }
}

/*
 * Technique 6: Add proper modern replacements for high contrast mode
 */
@media (forced-colors: active) {
  /* Basic elements */
  body {
    color: CanvasText;
    background-color: Canvas;
    forced-color-adjust: none;
  }

  /* Links */
  a {
    color: LinkText;
    forced-color-adjust: none;
  }

  a:visited {
    color: VisitedText;
    forced-color-adjust: none;
  }

  /* Buttons */
  button,
  input[type="button"],
  input[type="submit"],
  input[type="reset"] {
    color: ButtonText;
    background-color: ButtonFace;
    border: 1px solid ButtonText;
    forced-color-adjust: none;
  }

  /* Form elements */
  input,
  textarea,
  select {
    color: FieldText;
    background-color: Field;
    border: 1px solid FieldText;
    forced-color-adjust: none;
  }
}

/*!**********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/ms-translator-blocker.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************/
/**
 * 微软翻译器属性阻止器
 * 
 * 此CSS文件专门用于阻止Microsoft Translator添加额外属性，
 * 例如_msttexthash, _msthidden, _msthash等，这些属性会导致React hydration警告
 */

/* 重置Microsoft Translator可能添加的所有属性 */
[_msttexthash],
[_msthidden],
[_msthash],
[_mstvisible],
[_mstlang],
[_mstaltindent],
[_mstalt],
[_mstwidth],
[_msthiddenattr],
[_mstplaceholder] {
  /* 覆盖这些属性 */
  _msttexthash: initial !important;
  _msthidden: initial !important;
  _msthash: initial !important;
  _mstvisible: initial !important;
  _mstlang: initial !important;
  _mstaltindent: initial !important;
  _mstalt: initial !important;
  _mstwidth: initial !important;
  _msthiddenattr: initial !important;
  _mstplaceholder: initial !important;
}

/* 为文档添加全局翻译跳过指令 */
html {
  translate: no;
  -webkit-translate: no;
  -ms-translate: no;
}

/* 强制所有元素不被自动翻译 */
body * {
  translate: no;
  -webkit-translate: no;
  -ms-translate: no;
} 
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/product-detail-fix.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/* 产品详情页样式修复 */

/* 修复产品详情页的整体布局 */
.product-detail-page {
  position: relative;
  width: 100%;
  min-height: 100vh;
}

/* 产品详情页容器修复 */
.product-detail-container {
  position: relative;
  z-index: 1;
  background-color: white;
}

/* 产品详情页修复类 */
.product-detail-fix {
  margin-top: 450px !important;
  position: relative;
  z-index: 1;
}

/* 强制覆盖Header Section的padding */
.product-detail-fix .bg-gray-50 .container {
  padding-top: 12rem !important;
  padding-bottom: 12rem !important;
}

/* 更具体的选择器来确保样式生效 */
.product-detail-container .bg-gray-50 .container.mx-auto {
  padding-top: 12rem !important;
  padding-bottom: 12rem !important;
}

/* 顶部导航区域优化 */
.product-detail-navigation {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px 24px;
  margin: -8px -8px 32px -8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}

/* 面包屑导航优化 */
.breadcrumbs {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 0;
  flex-wrap: wrap;
}

.breadcrumbs a {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  padding: 4px 8px;
  border-radius: 6px;
  white-space: nowrap;
}

.breadcrumbs a:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
}

.breadcrumbs .separator {
  color: #d1d5db;
  font-weight: 400;
  margin: 0 4px;
}

.breadcrumbs .current {
  color: #374151;
  font-weight: 600;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 产品展示区域间距修复 */
.product-showcase-section {
  margin-top: 80px;
  margin-bottom: 80px;
  padding: 0 16px;
}

/* 产品大图区域布局优化 - 全屏展示，左右不留空间 */
.product-large-images-section {
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #f0f4f8 0%, #e2e8f0 50%, #f8fafc 100%);
  padding: 60px 0 40px 0;
  margin-top: 60px;
  /* 修复图片溢出问题 - 使用更简单可靠的全屏方法 */
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  position: relative;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.6);
  overflow-x: hidden; /* 防止水平滚动 */
  overflow-y: visible;
}

/* 产品展示区域内容容器 - 移除左右边距，实现真正全屏 */
.product-large-images-section .max-w-7xl {
  max-width: none !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100% !important;
}

/* 产品展示标题区域保持居中，但移除左右边距 */
.product-large-images-section .product-showcase-header {
  padding: 0 !important;
  margin-bottom: 48px;
  text-align: center;
  position: relative;
  z-index: 2;
}

/* 移除产品大图之间的间距 */
.product-large-images-section .space-y-12 {
  gap: 0 !important;
  display: block !important;
}

.product-large-images-section .space-y-12 > div {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

/* 覆盖原有的产品大图展示样式 */
.product-large-gallery {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.product-large-gallery .space-y-12 > div {
  margin-bottom: 0 !important;
}

/* 确保图片容器紧密连接并全屏显示 */
.product-detail-image-container {
  border-radius: 0 !important;
  margin-bottom: 0 !important;
  width: 100% !important; /* 改为100%而不是100vw，避免溢出 */
  margin-left: 0 !important;
  margin-right: 0 !important;
  max-width: none !important;
  overflow: hidden; /* 确保内容不会溢出容器 */
  position: relative;
}

/* 产品大图展示组件全屏样式 */
.product-large-images-section .product-large-gallery {
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 产品大图展示的每个图片项目全屏 */
.product-large-images-section .space-y-12 > div,
.product-large-images-section .product-large-gallery > div {
  width: 100% !important; /* 改为100%而不是100vw，避免溢出 */
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  max-width: none !important;
  overflow: hidden; /* 防止内容溢出 */
}

/* 添加微妙的装饰效果 */
.product-large-images-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(120, 119, 198, 0.3), transparent);
  z-index: 1;
}

.product-large-images-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  z-index: 1;
}

/* 产品信息卡片样式 */
.product-info-card {
  background: white;
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  width: 100%;
  height: -moz-fit-content;
  height: fit-content;
  position: relative;
  z-index: 2;
}

/* 产品特点网格优化 */
.product-features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 24px;
}

.product-feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.1);
  transition: all 0.3s ease;
  font-size: 15px;
}

.product-feature-item:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

/* 缩略图区域 */
.product-thumbnails-section {
  margin-top: 24px;
}

/* 缩略图网格优化 */
.product-thumbnails-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding: 16px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.product-thumbnail-item {
  position: relative;
  aspect-ratio: 4/3;
  border-radius: 12px;
  overflow: hidden;
  border: 3px solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.product-thumbnail-item.active {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3), 0 8px 24px rgba(59, 130, 246, 0.2);
  /* 移除放大效果 */
}

.product-thumbnail-item:hover {
  border-color: #60a5fa;
  /* 移除放大效果 */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.product-thumbnail-item img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.3s ease;
}

/* 移除缩略图悬停放大效果 */

/* 缩略图统一标识器已移除 - 不再显示数字 */

/* 隐藏图片计数器 */
.product-detail-image-container .absolute.bottom-6.left-6,
.product-large-images-section .absolute.bottom-6.left-6,
div[class*="bottom-6"][class*="left-6"][class*="bg-white"],
.absolute.bottom-6.left-6.bg-white\/90,
.group .absolute[class*="bottom-6"][class*="left-6"] {
  display: none !important;
}

/* 隐藏所有包含图片计数格式的元素 */
div:contains("/ 6"),
div:contains("1 /"),
div:contains("2 /"),
div:contains("3 /"),
div:contains("4 /"),
div:contains("5 /"),
div:contains("6 /") {
  display: none !important;
}

/* 缩略图加载效果 */
.product-thumbnail-item img {
  opacity: 0;
  animation: fadeInImage 0.5s ease-out forwards;
}

@keyframes fadeInImage {
  to {
    opacity: 1;
  }
}

/* 兼容其他缩略图组件样式 */
.thumbnails-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
  margin-top: 16px;
  padding: 12px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.thumbnail-item {
  position: relative;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;
  background: white;
}

.thumbnail-item.border-blue-500 {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
  /* 移除选中状态放大效果 */
}

.thumbnail-item:hover {
  border-color: #60a5fa;
  /* 移除放大效果 */
}

/* 产品标题区域优化 */
.product-title-section {
  margin-bottom: 32px;
}

.product-main-title {
  font-size: clamp(1.8rem, 4vw, 3rem);
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.product-category-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 联系按钮优化 */
.product-contact-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px 32px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border-radius: 16px;
  font-weight: 600;
  font-size: 16px;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
  width: 100%;
}

.product-contact-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
}



/* 服务承诺区域 */
.product-service-promises {
  display: flex;
  gap: 24px;
  margin-top: 24px;
  flex-wrap: wrap;
}

.service-promise-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 14px;
}

/* 响应式优化 */
@media (max-width: 1024px) {
  .product-thumbnails-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 14px;
    padding: 14px;
  }

  .product-features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .product-detail-fix {
    margin-top: 300px !important;
    padding-top: 16px !important;
  }

  .product-showcase-section {
    margin-top: 40px;
    margin-bottom: 40px;
  }

  .product-large-images-section {
    padding: 30px 0;
  }

  .product-info-card {
    padding: 24px;
    border-radius: 16px;
  }

  .product-thumbnails-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 12px;
    margin-top: 16px;
  }

  .product-thumbnail-item {
    aspect-ratio: 1/1;
  }

  .product-service-promises {
    flex-direction: column;
    gap: 16px;
  }

  .product-contact-button {
    width: 100%;
    padding: 14px 24px;
  }

  /* 移动端导航区域优化 */
  .product-detail-navigation {
    padding: 16px 20px;
    margin: -8px -8px 24px -8px;
    border-radius: 8px;
  }

  .breadcrumbs {
    font-size: 13px;
    gap: 6px;
  }

  .breadcrumbs a {
    padding: 3px 6px;
  }

  .breadcrumbs .current {
    max-width: 150px;
  }


}

@media (max-width: 640px) {
  .product-features-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .product-thumbnails-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    padding: 10px;
  }

  .product-thumbnail-item {
    aspect-ratio: 1/1;
    border-width: 2px;
  }

  .product-thumbnail-item.active::after {
    width: 16px;
    height: 16px;
    font-size: 10px;
    top: 2px;
    right: 2px;
  }

  .product-feature-item {
    padding: 12px;
    font-size: 14px;
  }
}

/* 产品大图容器 - 全屏显示 */
.product-detail-image-container {
  position: relative;
  width: 100% !important; /* 改为100%而不是100vw，避免溢出 */
  aspect-ratio: 16/9;
  min-height: 400px;
  max-height: 600px;
  border-radius: 0 !important;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  margin-left: 0 !important;
  margin-right: 0 !important;
  max-width: none !important;
  overflow: hidden; /* 确保内容不会溢出容器 */
}

.product-detail-image-container img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.5s ease;
}

/* 移除产品详情图片悬停放大效果 */

/* 产品展示标题区域 */
.product-showcase-header {
  text-align: center;
  margin-bottom: 48px;
  padding: 0 16px;
  position: relative;
  z-index: 2;
}

.product-showcase-title {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 16px;
}

.product-showcase-divider {
  width: 96px;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  margin: 0 auto 16px;
  border-radius: 2px;
}

.product-showcase-description {
  color: #6b7280;
  font-size: 1.125rem;
  max-width: 32rem;
  margin: 0 auto;
  line-height: 1.6;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-slide-in-left {
  opacity: 0;
  animation: slideInFromLeft 0.5s ease-out forwards;
}

.animate-slide-in-right {
  opacity: 0;
  animation: slideInFromRight 0.5s ease-out forwards;
}

/* 导航区域动画 */
.product-detail-navigation {
  animation: fadeInUp 0.4s ease-out forwards;
  opacity: 0;
  animation-delay: 0.1s;
}

/* 延迟动画 */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }

/* 背景图片上的导航样式 - 移除白色背景 */
.product-detail-navigation-overlay {
  background: transparent;
  -webkit-backdrop-filter: none;
          backdrop-filter: none;
  border-radius: 0;
  padding: 1.5rem 2rem;
  box-shadow: none;
  border: none;
  max-width: 1200px;
  margin: 0 auto;
}

.breadcrumbs-overlay {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  flex-wrap: wrap;
}

.breadcrumbs-overlay a {
  color: white;
  text-decoration: none;
  transition: all 0.2s ease;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  background: rgba(0, 0, 0, 0.7);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.breadcrumbs-overlay a:hover {
  color: white;
  background: rgba(0, 0, 0, 0.85);
  transform: translateY(-1px);
  border-color: rgba(255, 255, 255, 0.3);
}

.breadcrumbs-overlay .separator {
  color: white;
  margin: 0 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  font-weight: 600;
  font-size: 1.1rem;
}

.breadcrumbs-overlay .current {
  color: white;
  font-weight: 700;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  padding: 0.375rem 0.75rem;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
}



/* 移动端覆盖样式调整 */
@media (max-width: 768px) {
  .product-detail-navigation-overlay {
    padding: 1rem 1.5rem;
    margin: 0 1rem;
    border-radius: 8px;
  }

  .breadcrumbs-overlay {
    font-size: 0.875rem;
    gap: 0.375rem;
  }

  .breadcrumbs-overlay .current {
    max-width: 150px;
  }

  /* 移动端也确保全屏显示 */
  .product-large-images-section .max-w-7xl {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .product-detail-image-container {
    width: 100% !important; /* 改为100%而不是100vw，避免溢出 */
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}

/* 通用全屏产品展示样式 - 覆盖所有可能的容器限制 */
.product-large-images-section * {
  box-sizing: border-box;
}

/* 确保所有产品展示相关的容器都能全屏 */
.product-large-images-section .container,
.product-large-images-section .mx-auto,
.product-large-images-section [class*="max-w"],
.product-large-images-section [class*="container"] {
  max-width: none !important;
  width: 100% !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* 产品详情图片组件全屏样式 */
.product-large-images-section .w-full {
  width: 100% !important; /* 改为100%而不是100vw，避免溢出 */
  max-width: none !important;
}

/* 产品展示区域的直接子元素全屏 */
.product-large-images-section > div {
  width: 100% !important;
  max-width: none !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* 强制覆盖ProductLargeGallery组件的容器样式 */
.product-large-images-section div[class*="max-w-7xl"],
.product-large-images-section div[class*="mx-auto"],
.product-large-images-section div[class*="px-"] {
  max-width: none !important;
  width: 100% !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* 强制覆盖space-y-12类的样式 */
.product-large-images-section .space-y-12 {
  gap: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 强制覆盖group类的样式，确保全屏 */
.product-large-images-section .group {
  width: 100% !important; /* 改为100%而不是100vw，避免溢出 */
  max-width: none !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  transform: none !important;
  overflow: hidden; /* 防止内容溢出 */
}

/* 移除group的hover效果，因为我们要全屏显示 */
.product-large-images-section .group:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* 防止页面整体产生水平滚动 */
body {
  overflow-x: hidden;
}

/* 确保产品详情页面容器不会溢出 */
.product-detail-page {
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
}

/* 确保图片不会超出容器边界 - 合并图片样式 */
.product-large-images-section img {
  width: 100% !important;
  max-width: 100% !important;
  height: auto;
  display: block;
  -o-object-fit: cover;
     object-fit: cover;
}

/* 强制修复所有可能导致溢出的元素 */
.product-large-images-section,
.product-large-images-section *,
.product-large-images-section *::before,
.product-large-images-section *::after {
  box-sizing: border-box !important;
}

/* 确保没有元素会超出视口宽度 */
.product-large-images-section * {
  max-width: 100% !important;
}

/* 特别处理可能的问题元素 */
.product-large-images-section .space-y-12,
.product-large-images-section .group,
.product-large-images-section .product-detail-image-container,
.product-large-images-section .product-large-gallery {
  contain: layout style !important;
}
/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/hero.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/* 主页英雄区域样式 */
.hero-section {
  position: relative;
  overflow: hidden;
  min-height: 80vh;
  display: flex;
  align-items: center;
  background-color: #f8fafc;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 600px;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  background: linear-gradient(90deg, #2563eb, #7e22ce);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.5;
  margin-bottom: 2rem;
  color: #4b5563;
}

.hero-cta {
  display: flex;
  gap: 1rem;
}

.hero-image {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 50%;
  overflow: hidden;
}

.hero-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
}

.hero-overlay {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.8) 40%, rgba(255,255,255,0) 100%);
  z-index: 1;
}

.hero-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  z-index: 0;
}

.hero-stats {
  display: flex;
  margin-top: 3rem;
  gap: 3rem;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2563eb;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #6b7280;
  font-size: 0.875rem;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.125rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 70vh;
  }
  
  .hero-image {
    width: 100%;
    opacity: 0.15;
  }
  
  .hero-overlay {
    background: rgba(255,255,255,0.8);
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-content {
    max-width: 100%;
    text-align: center;
    padding: 0 1rem;
  }
  
  .hero-cta {
    justify-content: center;
  }
  
  .hero-stats {
    flex-direction: column;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-section {
    min-height: 60vh;
    padding: 4rem 0;
  }
  
  .hero-title {
    font-size: 1.75rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .hero-cta {
    flex-direction: column;
  }
} 
/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/home-page.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/* 页面布局优化 */
.section-spacer {
  height: 120px;
}

.section-container {
  max-width: 1240px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
}

/* 背景样式优化 */
.bg-light {
  background-color: #f7f9fc;
  position: relative;
  overflow: hidden;
}

.bg-dark {
  background-color: #0e1224;
  color: #fff;
  position: relative;
  overflow: hidden;
}

/* 区域模式和装饰 */
.section-pattern {
  position: absolute;
  left: 0;
  width: 100%;
  height: 80px;
  z-index: 1;
}

.section-pattern-top {
  top: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
}

.section-pattern-bottom {
  bottom: 0;
  background: linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
}

.section-pattern-dark {
  background: linear-gradient(180deg, rgba(14, 18, 36, 1) 0%, rgba(14, 18, 36, 0) 100%);
}

.section-pattern-dark.section-pattern-bottom {
  background: linear-gradient(0deg, rgba(14, 18, 36, 1) 0%, rgba(14, 18, 36, 0) 100%);
}

/* 区域样式优化 */
.section-features, .section-about, .section-custom {
  padding: 120px 0;
  position: relative;
  z-index: 1;
}

/* 高端UI元素全局样式 */
.home-page h1,
.home-page h2,
.home-page h3,
.home-page h4,
.home-page h5,
.home-page h6 {
  font-weight: 300;
  letter-spacing: -0.5px;
}

.section-title {
  font-size: 42px; /* 增大字体大小 */
  margin-bottom: 24px;
  line-height: 1.2;
}

.section-subtitle {
  font-size: 18px; /* 增大描述文字大小 */
  line-height: 1.6;
}

.thin-text {
  font-weight: 200;
}

.home-page strong {
  font-weight: 500;
}

.home-page p {
  font-weight: 300;
  line-height: 1.8;
}

.home-page button, .home-page .btn {
  transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1);
}

/* 高端分隔线 */
.elegant-divider {
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #1a1a2e, rgba(26, 26, 46, 0.3));
  margin: 30px 0;
}

/* 微妙的阴影效果 */
.premium-shadow {
  box-shadow: 0 20px 60px rgba(26, 26, 46, 0.08);
}

/* 图片悬停效果 */
.hover-scale {
  overflow: hidden;
}

.hover-scale img {
  transition: transform 0.7s cubic-bezier(0.19, 1, 0.22, 1);
}

/* 移除图片悬停放大效果 */

/* 加载动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

/* 响应式布局优化 */
@media (max-width: 1366px) {
  .section-spacer {
    height: 100px;
  }

  .section-features, .section-about, .section-custom {
    padding: 100px 0;
  }

  .section-title {
    font-size: 36px;
  }

  .section-subtitle {
    font-size: 17px;
  }
}

@media (max-width: 992px) {
  .section-spacer {
    height: 80px;
  }

  .section-features, .section-about, .section-custom {
    padding: 80px 0;
  }

  .section-title {
    font-size: 32px;
  }

  .section-subtitle {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .section-spacer {
    height: 60px;
  }

  .section-features, .section-about, .section-custom {
    padding: 60px 0;
  }

  .section-title {
    font-size: 28px;
  }

  .section-subtitle {
    font-size: 15px;
  }
}

@media (max-width: 576px) {
  .section-spacer {
    height: 50px;
  }

  .section-features, .section-about, .section-custom {
    padding: 50px 0;
  }
}

/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/home-page-fix.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/* 首页轮播图样式优化 */
.hero-slider-wrapper {
  margin-top: -70px !important; /* 使用负边距，让轮播图向上移动 */
  position: relative !important;
  overflow: hidden !important;
  width: 100vw !important; /* 占据整个视口宽度 */
  max-width: 100vw !important;
  margin-left: calc(50% - 50vw) !important; /* 向左延伸到屏幕边缘 */
  margin-right: calc(50% - 50vw) !important; /* 向右延伸到屏幕边缘 */
  left: 0 !important;
  right: 0 !important;
  padding-top: 70px !important; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
}

.hero-slider {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* 轮播图内容样式优化 */
.hero-slider .slide-content {
  padding-top: 50px !important; /* 增加顶部内边距，确保内容不被导航栏遮挡 */
  z-index: 5 !important; /* 确保内容在最上层 */
}

/* 轮播图导航按钮样式优化 */
.hero-slider .arrow {
  z-index: 10 !important; /* 确保导航按钮在最上层 */
}

/* 轮播图指示器样式优化 */
.hero-slider .slider-dots {
  bottom: 20px !important; /* 调整指示器位置 */
  z-index: 10 !important; /* 确保指示器在最上层 */
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/custom-overrides.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/* 覆盖全局样式，隐藏不需要的图标 */
.search-icon,
.account-icon {
  display: none !important;
} 

/* 确保下拉菜单正常显示 */
.dropdown-portal {
  opacity: 1 !important;
  visibility: visible !important;
  pointer-events: auto !important;
  z-index: 100000 !important;
  transition: opacity 0.3s ease, transform 0.3s ease !important;
  transform: translateY(0) !important;
  animation: fadeInMenu 0.3s ease forwards !important;
}

@keyframes fadeInMenu {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/loading-fix.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/**
 * 页面加载过程中的样式修复
 * 解决产品页面在刷新过程中出现的样式重叠问题
 */

/* 在页面加载过程中隐藏内容，直到CSS完全加载 */
.js-loading {
  visibility: hidden;
}

/* 页面加载完成后显示内容 */
.js-loading-complete {
  visibility: visible;
}

/* 产品详情页容器 - 防止在加载过程中出现重叠 */
.product-detail-container {
  position: relative;
  z-index: 1;
  background-color: #fff;
  transition: opacity 0.3s ease;
}

/* 产品详情页顶部横幅 - 确保在加载过程中有正确的位置 */
.product-detail-container .w-full.absolute {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 0 !important;
}

/* 产品详情内容 - 确保在加载过程中有正确的位置 */
.product-detail-container .bg-white.relative {
  position: relative !important;
  z-index: 1 !important;
  background-color: #fff !important;
}

/* 确保导航栏在页面加载过程中有正确的位置和样式 */
.header-main {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;
  transition: background-color 0.3s ease, transform 0.3s ease !important;
}

/* 确保产品页面的内容在导航栏下方 */
.product-list-page,
.product-detail-page {
  padding-top: 80px !important;
  position: relative;
  z-index: 1;
}

/* 添加页面加载过渡效果 */
body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 9999;
  pointer-events: none;
  opacity: 1;
  transition: opacity 0.5s ease;
}

body.loaded::after {
  opacity: 0;
}

/* 确保产品详情页的banner图片在加载过程中有正确的位置 */
.product-detail-container .w-full.absolute img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

/* 防止产品详情页内容在加载过程中闪烁 */
.product-detail-container .container {
  opacity: 0;
  animation: fadeIn 0.5s ease forwards;
  animation-delay: 0.3s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 确保产品列表页在加载过程中有正确的样式 */
.grid.grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3.gap-8,
.products-grid {
  opacity: 0;
  animation: fadeIn 0.5s ease forwards;
  animation-delay: 0.2s;
}

/* 产品列表页面样式修复 */
.product-list-page {
  position: relative;
  z-index: 1;
}

.product-list-content {
  position: relative;
  z-index: 2;
  background-color: #fff;
}

.product-list-container {
  opacity: 0;
  animation: fadeIn 0.5s ease forwards;
  animation-delay: 0.3s;
}

/* 修复产品详情页和产品列表页在刷新过程中的样式重叠问题 */
@media (min-width: 768px) {
  .product-detail-container {
    margin-top: 0 !important;
  }

  .product-detail-container .w-full.absolute {
    height: 450px !important;
  }

  .product-detail-container .bg-white.relative {
    margin-top: 450px !important;
  }
}

/* 移动端样式修复 */
@media (max-width: 767px) {
  .product-detail-container {
    margin-top: 0 !important;
  }

  .product-detail-container .w-full.absolute {
    height: 300px !important;
  }

  .product-detail-container .bg-white.relative {
    margin-top: 300px !important;
  }
}

/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/top-space-fix.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/* 修复页面顶部空白问题 */

/* 全局修复 */
body, html {
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden !important;
}

/* 修复页面顶部空白 */
.product-list-page,
.product-detail-page,
.page-banner,
.PageHeader_pageHeader__wrapper,
[class*='PageHeader_'],
.about-us-page {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* 确保内容不会被导航栏遮挡 */
.page-banner .container,
.PageHeader_pageHeader__content,
[class*='PageHeader_'] .container {
  padding-top: 80px !important; /* 为导航栏留出空间 */
}

/* 修复产品详情页顶部空白 */
.product-detail-container {
  margin-top: 450px !important; /* 为更大的背景图片留出空间 */
}

/* 移除所有可能导致顶部空白的元素 */
.product-page-spacer,
.section-spacer,
.nav-content-separator {
  height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  display: none !important;
}

/* 确保页面内容从顶部开始 */
main, .main-content {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-banner .container,
  .PageHeader_pageHeader__content,
  [class*='PageHeader_'] .container {
    padding-top: 60px !important; /* 移动端减少内边距 */
  }

  .product-detail-container {
    margin-top: 300px !important; /* 移动端调整，为更大的背景图片留出空间 */
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/custom-solutions.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/* 定制解决方案页面增强样式 */

/* 强制确保所有内容可见 */
* {
  visibility: visible !important;
  opacity: 1 !important;
}

/* 页面顶部横幅增强 */
.page-banner {
  background-image: url('/images/holographic-tech-bg.jpg') !important;
  background-size: cover !important;
  background-position: center !important;
  padding: 100px 0 80px !important;
  position: relative !important;
  overflow: hidden !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
  z-index: 10 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.page-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(10, 36, 99, 0.85) 0%, rgba(30, 80, 162, 0.75) 100%);
  z-index: 1;
}

.page-banner::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 35%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  z-index: 2;
}

.page-banner .container {
  position: relative;
  z-index: 3;
}

.page-banner .page-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  letter-spacing: 1px;
}

.page-banner .breadcrumb {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
}

.page-banner .breadcrumb a {
  color: rgba(255, 255, 255, 0.9);
  transition: color 0.3s ease;
}

.page-banner .breadcrumb a:hover {
  color: #ffffff;
  text-decoration: none;
}

.page-banner .separator {
  margin: 0 10px;
  opacity: 0.7;
}

/* 内容区域样式增强 */
.solutions-intro {
  padding: 80px 0 !important;
  background-color: #fff !important;
  position: relative !important;
  z-index: 10 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.solutions-intro .content-grid {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
}

@media (max-width: 768px) {
  .solutions-intro .content-grid {
    grid-template-columns: 1fr;
  }
}

.solutions-intro .content-image {
  position: relative;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  overflow: hidden;
  transform: perspective(1000px) rotateY(-5deg);
  transition: transform 0.5s ease;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 600px;
  margin: 0 auto;
}

.solutions-intro .content-image img {
  width: auto;
  height: auto;
  max-width: 600px;
  max-height: 400px;
  -o-object-fit: contain;
     object-fit: contain;
  display: block;
}

.solutions-intro .content-image:hover {
  transform: perspective(1000px) rotateY(0deg);
}

.solutions-intro .content-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(30, 80, 162, 0.1) 0%, transparent 60%);
  z-index: 1;
}

.solutions-intro .content-text {
  position: relative;
  padding: 30px;
}

.solutions-intro .content-text h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a2e;
  margin-bottom: 30px;
  position: relative;
  padding-bottom: 15px;
}

.solutions-intro .content-text h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #0a2463 0%, #1e50a2 100%);
}

.solutions-intro .content-text p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
  margin-bottom: 20px;
}

/* 服务卡片增强 */
.solutions-list {
  padding: 60px 0 !important;
  background-color: #f8fafd !important;
  position: relative !important;
  z-index: 10 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.solutions-list::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 30%, rgba(30, 80, 162, 0.03) 0%, transparent 70%);
  opacity: 0.8;
}

.solutions-list .section-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #1a1a2e;
  text-align: center;
  margin-bottom: 50px;
  position: relative;
  padding-bottom: 20px;
}

.solutions-list .section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #0a2463 0%, #1e50a2 100%);
}

.solutions-list .services-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
  gap: 30px !important;
  margin-top: 40px !important;
  position: relative !important;
  z-index: 10 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

@media (max-width: 768px) {
  .solutions-list .services-grid {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }
}

.solutions-list .service-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: transform 0.4s ease, box-shadow 0.4s ease;
  background-color: #fff;
  border-top: 5px solid transparent;
  -o-border-image: linear-gradient(90deg, #0a2463, #1e50a2);
     border-image: linear-gradient(90deg, #0a2463, #1e50a2);
  border-image-slice: 1;
}

.solutions-list .service-card:hover {
  transform: translateY(-15px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.solutions-list .service-image {
  position: relative;
  overflow: hidden;
}

.solutions-list .service-image img {
  transition: transform 0.6s ease;
}

/* 移除服务卡片图片悬停放大效果 */

.solutions-list .service-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.1) 0%, transparent 50%);
}

.solutions-list .service-content {
  padding: 30px;
}

.solutions-list .service-content h3 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1a1a2e;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 15px;
}

.solutions-list .service-content h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #0a2463 0%, #1e50a2 100%);
}

.solutions-list .service-content p {
  font-size: 1.05rem;
  line-height: 1.7;
  color: #555;
  margin-bottom: 20px;
}

.solutions-list .service-features {
  margin: 20px 0 25px;
  padding-left: 5px;
}

.solutions-list .service-features li {
  margin-bottom: 12px;
  position: relative;
  padding-left: 28px;
  font-size: 1.05rem;
  color: #555;
}

.solutions-list .service-features li:before {
  content: "\f00c";
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  color: #1e50a2;
  position: absolute;
  left: 0;
  top: 2px;
}

.solutions-list .btn-secondary {
  padding: 12px 28px;
  font-size: 1rem;
  font-weight: 600;
  border-width: 2px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.solutions-list .btn-secondary:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(30, 80, 162, 0.2);
}

/* 流程时间线增强 */
.design-process {
  padding: 80px 0 !important;
  background-color: #fff !important;
  position: relative !important;
  overflow: hidden !important;
  z-index: 10 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.design-process::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle at center, rgba(30, 80, 162, 0.05) 0%, transparent 70%);
  border-radius: 50%;
}

.design-process::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle at center, rgba(30, 80, 162, 0.05) 0%, transparent 70%);
  border-radius: 50%;
}

.design-process .section-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #1a1a2e;
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  padding-bottom: 20px;
}

.design-process .section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, #0a2463 0%, #1e50a2 100%);
}

.design-process .process-timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.design-process .process-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 24px;
  width: 2px;
  background: linear-gradient(to bottom, #0a2463 0%, #1e50a2 100%);
  z-index: 1;
}

.design-process .timeline-item {
  position: relative;
  padding-left: 70px;
  margin-bottom: 40px;
  z-index: 2;
}

.design-process .timeline-number {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #0a2463 0%, #1e50a2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: #fff;
  position: absolute;
  left: 0;
  top: 0;
  box-shadow: 0 5px 15px rgba(30, 80, 162, 0.2);
}

.design-process .timeline-content {
  background-color: #fff;
  padding: 25px 30px;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  border-left: 3px solid #1e50a2;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.design-process .timeline-item:hover .timeline-content {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
}

.design-process .timeline-content h3 {
  font-size: 1.6rem;
  font-weight: 700;
  color: #1a1a2e;
  margin-bottom: 10px;
}

.design-process .timeline-content p {
  font-size: 1.05rem;
  line-height: 1.7;
  color: #555;
  margin-bottom: 0;
}

/* CTA 部分增强 */
.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #0a2463 0%, #1e50a2 100%);
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 75%, transparent 75%);
  background-size: 20px 20px;
  opacity: 0.05;
}

.cta-section::after {
  content: '';
  position: absolute;
  bottom: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

.cta-section .cta-content {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 1;
}

.cta-section .cta-content h2 {
  font-size: 2.8rem;
  font-weight: 700;
  color: #fff;
  margin-bottom: 20px;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.cta-section .cta-content p {
  font-size: 1.2rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 40px;
}

.cta-section .btn-primary {
  padding: 15px 40px;
  font-size: 1.1rem;
  font-weight: 600;
  background-color: #fff;
  color: #1e50a2;
  border-radius: 6px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
}

.cta-section .btn-primary:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

/* 响应式调整 */
@media (max-width: 992px) {
  .page-banner .page-title {
    font-size: 2.8rem;
  }

  .solutions-intro .content-text h2 {
    font-size: 2.2rem;
  }

  .solutions-list .section-title,
  .design-process .section-title,
  .cta-section .cta-content h2 {
    font-size: 2.4rem;
  }
}

@media (max-width: 768px) {
  .page-banner {
    padding: 80px 0 60px;
  }

  .page-banner .page-title {
    font-size: 2.5rem;
  }

  .solutions-intro {
    padding: 60px 0;
  }

  .solutions-intro .content-text h2 {
    font-size: 2rem;
  }

  .solutions-list .section-title,
  .design-process .section-title,
  .cta-section .cta-content h2 {
    font-size: 2.2rem;
  }

  .design-process .timeline-content h3 {
    font-size: 1.4rem;
  }

  .cta-section .cta-content p {
    font-size: 1.1rem;
  }
}

@media (max-width: 576px) {
  .page-banner {
    padding: 60px 0 40px;
  }

  .page-banner .page-title {
    font-size: 2.2rem;
  }

  .solutions-intro .content-text h2 {
    font-size: 1.8rem;
  }

  .solutions-list .section-title,
  .design-process .section-title,
  .cta-section .cta-content h2 {
    font-size: 2rem;
  }

  .solutions-list .service-content h3 {
    font-size: 1.6rem;
  }

  .design-process .timeline-number {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .design-process .timeline-item {
    padding-left: 60px;
  }

  .cta-section {
    padding: 60px 0;
  }

  .cta-section .btn-primary {
    padding: 12px 30px;
    font-size: 1rem;
  }

  /* 小屏幕设备图片进一步优化 */
  .solutions-intro .content-image {
    max-width: 100%;
    width: 100%;
    transform: none;
  }

  .solutions-intro .content-image img {
    max-width: 100%;
    max-height: 300px;
  }
}

/* 设计流程步骤图片优化 - 统一600x400尺寸 */
.step-image {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  height: 400px;
  margin: 0 auto;
  display: block;
}

.step-image img,
.step-image .step-img {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  -o-object-fit: cover;
     object-fit: cover;
  display: block;
  transition: transform 0.3s ease;
}

.step-image:hover img,
.step-image:hover .step-img {
  transform: none;
}

.step-content-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.step-image:hover .step-content-overlay {
  transform: translateY(0);
}

.step-number {
  font-size: 2rem;
  font-weight: bold;
  color: #1e50a2;
  margin-bottom: 10px;
}

.step-content-overlay h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: white;
}

.step-content-overlay p {
  font-size: 0.9rem;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.9);
}

/* 设计流程步骤项目布局 */
.step-item {
  text-align: center;
  margin-bottom: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.design-process .process-steps {
  display: flex;
  flex-direction: column;
  gap: 30px;
  align-items: center;
}

.design-process .steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

/* 响应式调整 - 步骤图片 */
@media (max-width: 768px) {
  .step-image {
    max-width: 100%;
    width: 100%;
  }

  .step-image img {
    max-width: 100%;
    max-height: 250px;
  }

  .design-process .steps-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 576px) {
  .step-image img {
    max-height: 200px;
  }

  .step-content-overlay {
    position: static;
    transform: none;
    background: rgba(0, 0, 0, 0.8);
    margin-top: -50px;
    border-radius: 0 0 8px 8px;
  }
}

/* 强制覆盖页面中的图片样式 */
.premium-card .step-img {
  width: auto !important;
  height: auto !important;
  max-width: 100% !important;
  max-height: none !important;
  -o-object-fit: contain !important;
     object-fit: contain !important;
}

.premium-card {
  width: -moz-fit-content !important;
  width: fit-content !important;
  max-width: 100% !important;
  margin: 0 auto !important;
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

.premium-card:hover {
  box-shadow: none !important;
  background: transparent !important;
}

/* 强制移除section背景色 */
.design-process.bg-light {
  background-color: transparent !important;
  background: transparent !important;
}

.design-process {
  background-color: transparent !important;
  background: transparent !important;
}

/* 更强的覆盖规则 */
section.design-process.section-padding.bg-light {
  background-color: transparent !important;
  background: transparent !important;
}

.bg-light {
  background-color: transparent !important;
  background: transparent !important;
}

/* 最高优先级覆盖 - 针对home-page.css中的.bg-light */
section.bg-light,
.section.bg-light,
div.bg-light {
  background-color: transparent !important;
  background: transparent !important;
  position: relative !important;
  overflow: hidden !important;
}

/* 特别针对设计流程页面 */
body section.design-process.section-padding.bg-light {
  background-color: transparent !important;
  background: transparent !important;
}
/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/unified-cta.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/* 统一CTA区域样式 */

/* 基础CTA区域样式 - 统一所有CTA区域 */
.cta-section,
.contact-cta,
.premium-cta {
  background-color: var(--primary-color, #0a59f7) !important;
  color: white !important;
  padding: 5rem 0 !important;
  text-align: center !important;
  position: relative !important;
  overflow: hidden !important;
}

/* CTA粒子背景效果 - 统一应用到所有CTA区域 */
.cta-section::before,
.contact-cta::before,
.premium-cta::before,
.cta-particles {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.12) 0%, transparent 25%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.12) 0%, transparent 25%) !important;
  z-index: 0 !important;
  pointer-events: none !important;
}

/* 确保内容在粒子效果之上 */
.cta-section .container,
.contact-cta .container,
.premium-cta .container {
  position: relative !important;
  z-index: 1 !important;
}

/* CTA内容区域 */
.cta-section .cta-content,
.contact-cta .cta-content,
.premium-cta .cta-content {
  max-width: 700px !important;
  margin: 0 auto !important;
  text-align: center !important;
  position: relative !important;
  z-index: 1 !important;
}

/* CTA标题样式 */
.cta-section h2,
.contact-cta h2,
.premium-cta h2,
.cta-section .cta-content h2,
.contact-cta .cta-content h2,
.premium-cta .cta-content h2 {
  font-size: 2.5rem !important;
  font-weight: 600 !important;
  color: white !important;
  margin-bottom: 1.2rem !important;
  position: relative !important;
  z-index: 1 !important;
  text-shadow: none !important;
}

/* CTA描述文字样式 */
.cta-section p,
.contact-cta p,
.premium-cta p,
.cta-section .cta-content p,
.contact-cta .cta-content p,
.premium-cta .cta-content p {
  font-size: 1.2rem !important;
  line-height: 1.6 !important;
  color: rgba(255, 255, 255, 0.9) !important;
  margin-bottom: 2.5rem !important;
  max-width: 700px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  position: relative !important;
  z-index: 1 !important;
}

/* CTA按钮样式 - 动画按钮效果 - 最高优先级 */
.btn-primary[data-text],
.cta-section .btn-primary,
.contact-cta .btn-primary,
.premium-cta .btn-primary,
.cta-section .btn-glow,
.contact-cta .btn-glow,
.premium-cta .btn-glow,
a.btn-primary[data-text] {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 50px !important;
  position: relative !important;
  padding: 0 20px !important;
  font-size: 18px !important;
  text-transform: uppercase !important;
  border: 0 !important;
  box-shadow: hsl(210deg 87% 36%) 0px 7px 0px 0px !important;
  background-color: hsl(210deg 100% 44%) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  transition: 31ms cubic-bezier(.5, .7, .4, 1) !important;
  text-decoration: none !important;
  color: white !important;
  font-weight: bold !important;
  letter-spacing: 4px !important;
  z-index: 1 !important;
  cursor: pointer !important;
  margin: 0 auto !important;
  width: auto !important;
  min-width: 200px !important;
}

/* 按钮默认文字 - 最高优先级 */
.btn-primary[data-text]:before,
.cta-section .btn-primary:before,
.contact-cta .btn-primary:before,
.premium-cta .btn-primary:before,
.cta-section .btn-glow:before,
.contact-cta .btn-glow:before,
.premium-cta .btn-glow:before,
a.btn-primary[data-text]:before {
  content: attr(data-text) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: absolute !important;
  inset: 0 !important;
  font-size: 15px !important;
  font-weight: bold !important;
  color: white !important;
  letter-spacing: 2px !important;
  opacity: 1 !important;
  transition: all 0.3s ease !important;
  transform: translateY(0) !important; /* 默认位置 */
  z-index: 2 !important; /* 确保在字母之上 */
}

/* 按钮点击效果 - 最高优先级 */
.btn-primary[data-text]:active,
.cta-section .btn-primary:active,
.contact-cta .btn-primary:active,
.premium-cta .btn-primary:active,
.cta-section .btn-glow:active,
.contact-cta .btn-glow:active,
.premium-cta .btn-glow:active,
a.btn-primary[data-text]:active {
  box-shadow: none !important;
  transform: translateY(7px) !important;
  transition: 35ms cubic-bezier(.5, .7, .4, 1) !important;
}

/* 悬停时隐藏默认文字 - 最高优先级 */
.btn-primary[data-text]:hover:before,
.cta-section .btn-primary:hover:before,
.contact-cta .btn-primary:hover:before,
.premium-cta .btn-primary:hover:before,
.cta-section .btn-glow:hover:before,
.contact-cta .btn-glow:hover:before,
.premium-cta .btn-glow:hover:before,
a.btn-primary[data-text]:hover:before {
  transition: all 0.2s ease !important;
  transform: translateY(-100%) !important; /* 向上滑出 */
  opacity: 0 !important;
}

/* 动画字母样式 - 默认隐藏 - 最高优先级 */
.btn-primary[data-text] i,
.cta-section .btn-primary i,
.contact-cta .btn-primary i,
.premium-cta .btn-primary i,
.cta-section .btn-glow i,
.contact-cta .btn-glow i,
.premium-cta .btn-glow i,
a.btn-primary[data-text] i {
  color: white !important;
  font-size: 15px !important;
  font-weight: bold !important;
  letter-spacing: 0px !important; /* 默认无字母间距 */
  font-style: normal !important;
  transition: all 0.3s ease !important;
  transform: translateY(20px) !important; /* 从下方出现 */
  opacity: 0 !important; /* 默认完全隐藏 */
  display: inline-block !important;
  position: relative !important;
  z-index: 3 !important; /* 确保在默认文字之上 */
}

/* 悬停时显示动画字母 - 最高优先级 */
.btn-primary[data-text]:hover i,
.cta-section .btn-primary:hover i,
.contact-cta .btn-primary:hover i,
.premium-cta .btn-primary:hover i,
.cta-section .btn-glow:hover i,
.contact-cta .btn-glow:hover i,
.premium-cta .btn-glow:hover i,
a.btn-primary[data-text]:hover i {
  transition: all 0.3s ease !important;
  transform: translateY(0px) !important;
  opacity: 1 !important;
  letter-spacing: 2px !important; /* 悬停时增加字母间距 */
}

/* 字母动画延迟效果 - 最高优先级 */
.btn-primary[data-text]:hover i:nth-child(1),
.cta-section .btn-primary:hover i:nth-child(1),
.contact-cta .btn-primary:hover i:nth-child(1),
.premium-cta .btn-primary:hover i:nth-child(1),
.cta-section .btn-glow:hover i:nth-child(1),
.contact-cta .btn-glow:hover i:nth-child(1),
.premium-cta .btn-glow:hover i:nth-child(1),
a.btn-primary[data-text]:hover i:nth-child(1) {
  transition-delay: 0.045s !important;
}

.btn-primary[data-text]:hover i:nth-child(2),
.cta-section .btn-primary:hover i:nth-child(2),
.contact-cta .btn-primary:hover i:nth-child(2),
.premium-cta .btn-primary:hover i:nth-child(2),
.cta-section .btn-glow:hover i:nth-child(2),
.contact-cta .btn-glow:hover i:nth-child(2),
.premium-cta .btn-glow:hover i:nth-child(2),
a.btn-primary[data-text]:hover i:nth-child(2) {
  transition-delay: calc(0.045s * 2) !important;
}

.btn-primary[data-text]:hover i:nth-child(3),
.cta-section .btn-primary:hover i:nth-child(3),
.contact-cta .btn-primary:hover i:nth-child(3),
.premium-cta .btn-primary:hover i:nth-child(3),
.cta-section .btn-glow:hover i:nth-child(3),
.contact-cta .btn-glow:hover i:nth-child(3),
.premium-cta .btn-glow:hover i:nth-child(3),
a.btn-primary[data-text]:hover i:nth-child(3) {
  transition-delay: calc(0.045s * 3) !important;
}

.btn-primary[data-text]:hover i:nth-child(4),
.cta-section .btn-primary:hover i:nth-child(4),
.contact-cta .btn-primary:hover i:nth-child(4),
.premium-cta .btn-primary:hover i:nth-child(4),
.cta-section .btn-glow:hover i:nth-child(4),
.contact-cta .btn-glow:hover i:nth-child(4),
.premium-cta .btn-glow:hover i:nth-child(4),
a.btn-primary[data-text]:hover i:nth-child(4) {
  transition-delay: calc(0.045s * 4) !important;
}

.btn-primary[data-text]:hover i:nth-child(5),
.cta-section .btn-primary:hover i:nth-child(5),
.contact-cta .btn-primary:hover i:nth-child(5),
.premium-cta .btn-primary:hover i:nth-child(5),
.cta-section .btn-glow:hover i:nth-child(5),
.contact-cta .btn-glow:hover i:nth-child(5),
.premium-cta .btn-glow:hover i:nth-child(5),
a.btn-primary[data-text]:hover i:nth-child(5) {
  transition-delay: calc(0.045s * 5) !important;
}

.btn-primary[data-text]:hover i:nth-child(6),
.cta-section .btn-primary:hover i:nth-child(6),
.contact-cta .btn-primary:hover i:nth-child(6),
.premium-cta .btn-primary:hover i:nth-child(6),
.cta-section .btn-glow:hover i:nth-child(6),
.contact-cta .btn-glow:hover i:nth-child(6),
.premium-cta .btn-glow:hover i:nth-child(6),
a.btn-primary[data-text]:hover i:nth-child(6) {
  transition-delay: calc(0.045s * 6) !important;
}

.cta-section .btn-primary:hover i:nth-child(7),
.contact-cta .btn-primary:hover i:nth-child(7),
.premium-cta .btn-primary:hover i:nth-child(7),
.cta-section .btn-glow:hover i:nth-child(7),
.contact-cta .btn-glow:hover i:nth-child(7),
.premium-cta .btn-glow:hover i:nth-child(7) {
  transition-delay: calc(0.045s * 7) !important;
}

.cta-section .btn-primary:hover i:nth-child(8),
.contact-cta .btn-primary:hover i:nth-child(8),
.premium-cta .btn-primary:hover i:nth-child(8),
.cta-section .btn-glow:hover i:nth-child(8),
.contact-cta .btn-glow:hover i:nth-child(8),
.premium-cta .btn-glow:hover i:nth-child(8) {
  transition-delay: calc(0.045s * 8) !important;
}

.cta-section .btn-primary:hover i:nth-child(9),
.contact-cta .btn-primary:hover i:nth-child(9),
.premium-cta .btn-primary:hover i:nth-child(9),
.cta-section .btn-glow:hover i:nth-child(9),
.contact-cta .btn-glow:hover i:nth-child(9),
.premium-cta .btn-glow:hover i:nth-child(9) {
  transition-delay: calc(0.045s * 9) !important;
}

.cta-section .btn-primary:hover i:nth-child(10),
.contact-cta .btn-primary:hover i:nth-child(10),
.premium-cta .btn-primary:hover i:nth-child(10),
.cta-section .btn-glow:hover i:nth-child(10),
.contact-cta .btn-glow:hover i:nth-child(10),
.premium-cta .btn-glow:hover i:nth-child(10) {
  transition-delay: calc(0.045s * 10) !important;
}

.cta-section .btn-primary:hover i:nth-child(11),
.contact-cta .btn-primary:hover i:nth-child(11),
.premium-cta .btn-primary:hover i:nth-child(11),
.cta-section .btn-glow:hover i:nth-child(11),
.contact-cta .btn-glow:hover i:nth-child(11),
.premium-cta .btn-glow:hover i:nth-child(11) {
  transition-delay: calc(0.045s * 11) !important;
}

.cta-section .btn-primary:hover i:nth-child(12),
.contact-cta .btn-primary:hover i:nth-child(12),
.premium-cta .btn-primary:hover i:nth-child(12),
.cta-section .btn-glow:hover i:nth-child(12),
.contact-cta .btn-glow:hover i:nth-child(12),
.premium-cta .btn-glow:hover i:nth-child(12) {
  transition-delay: calc(0.045s * 12) !important;
}

.cta-section .btn-primary:hover i:nth-child(13),
.contact-cta .btn-primary:hover i:nth-child(13),
.premium-cta .btn-primary:hover i:nth-child(13),
.cta-section .btn-glow:hover i:nth-child(13),
.contact-cta .btn-glow:hover i:nth-child(13),
.premium-cta .btn-glow:hover i:nth-child(13) {
  transition-delay: calc(0.045s * 13) !important;
}

.cta-section .btn-primary:hover i:nth-child(14),
.contact-cta .btn-primary:hover i:nth-child(14),
.premium-cta .btn-primary:hover i:nth-child(14),
.cta-section .btn-glow:hover i:nth-child(14),
.contact-cta .btn-glow:hover i:nth-child(14),
.premium-cta .btn-glow:hover i:nth-child(14) {
  transition-delay: calc(0.045s * 14) !important;
}

.cta-section .btn-primary:hover i:nth-child(15),
.contact-cta .btn-primary:hover i:nth-child(15),
.premium-cta .btn-primary:hover i:nth-child(15),
.cta-section .btn-glow:hover i:nth-child(15),
.contact-cta .btn-glow:hover i:nth-child(15),
.premium-cta .btn-glow:hover i:nth-child(15) {
  transition-delay: calc(0.045s * 15) !important;
}

/* 移除所有可能冲突的背景样式 */
.cta-section {
  background: var(--primary-color, #0a59f7) !important;
  background-color: var(--primary-color, #0a59f7) !important;
  background-image: none !important;
  background-gradient: none !important;
}

.contact-cta {
  background: var(--primary-color, #0a59f7) !important;
  background-color: var(--primary-color, #0a59f7) !important;
  background-image: none !important;
  background-gradient: none !important;
}

.premium-cta {
  background: var(--primary-color, #0a59f7) !important;
  background-color: var(--primary-color, #0a59f7) !important;
  background-image: none !important;
  background-gradient: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cta-section,
  .contact-cta,
  .premium-cta {
    padding: 3rem 0 !important;
  }

  .cta-section h2,
  .contact-cta h2,
  .premium-cta h2,
  .cta-section .cta-content h2,
  .contact-cta .cta-content h2,
  .premium-cta .cta-content h2 {
    font-size: 2rem !important;
  }

  .cta-section p,
  .contact-cta p,
  .premium-cta p,
  .cta-section .cta-content p,
  .contact-cta .cta-content p,
  .premium-cta .cta-content p {
    font-size: 1.1rem !important;
    margin-bottom: 2rem !important;
  }

  .cta-section .btn-primary,
  .contact-cta .btn-primary,
  .premium-cta .btn-primary,
  .cta-section .btn-glow,
  .contact-cta .btn-glow,
  .premium-cta .btn-glow {
    padding: 12px 24px !important;
    font-size: 0.9rem !important;
  }
}

/* 确保所有CTA区域都有粒子效果 */
.cta-section:not(.cta-particles):not([class*="particles"])::before,
.contact-cta:not(.cta-particles):not([class*="particles"])::before,
.premium-cta:not(.cta-particles):not([class*="particles"])::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.12) 0%, transparent 25%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.12) 0%, transparent 25%) !important;
  z-index: 0 !important;
  pointer-events: none !important;
}

/* 强制覆盖 - 确保动画按钮样式绝对优先 */
a[data-text].btn-primary,
a.btn-primary[data-text] {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 50px !important;
  position: relative !important;
  padding: 0 20px !important;
  font-size: 18px !important;
  text-transform: uppercase !important;
  border: 0 !important;
  box-shadow: hsl(210deg 87% 36%) 0px 7px 0px 0px !important;
  background-color: hsl(210deg 100% 44%) !important;
  border-radius: 12px !important;
  overflow: visible !important; /* 改为visible以防止内容被裁剪 */
  transition: 31ms cubic-bezier(.5, .7, .4, 1) !important;
  text-decoration: none !important;
  color: white !important;
  font-weight: bold !important;
  letter-spacing: 4px !important;
  z-index: 1 !important;
  cursor: pointer !important;
  min-width: -moz-fit-content !important;
  min-width: fit-content !important; /* 确保按钮宽度适应内容 */
  white-space: nowrap !important; /* 防止文字换行 */
  flex-wrap: nowrap !important; /* 防止flex项目换行 */
}

/* 覆盖Next.js生成的内联样式 - 针对关于我们页面 */
.contact-cta a[data-text].btn-primary,
.contact-cta a.btn-primary[data-text],
.about-us-page .contact-cta .btn-primary {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 50px !important;
  position: relative !important;
  padding: 0 20px !important;
  font-size: 18px !important;
  text-transform: uppercase !important;
  border: 0 !important;
  box-shadow: hsl(210deg 87% 36%) 0px 7px 0px 0px !important;
  background-color: hsl(210deg 100% 44%) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  transition: 31ms cubic-bezier(.5, .7, .4, 1) !important;
  text-decoration: none !important;
  color: white !important;
  font-weight: bold !important;
  letter-spacing: 4px !important;
  z-index: 1 !important;
  cursor: pointer !important;
  gap: 0 !important; /* 移除gap */
  margin: 0 auto !important; /* 居中显示 */
  width: auto !important;
  min-width: 200px !important;
}

/* 针对Next.js生成的JSX类名的特殊覆盖 */
.contact-cta .btn-primary i[class*="jsx-"],
.contact-cta .btn-primary i[class^="jsx-"],
.about-us-page .contact-cta .btn-primary i[class*="jsx-"],
.about-us-page .contact-cta .btn-primary i[class^="jsx-"] {
  display: inline !important;
  font-style: normal !important;
  font-weight: inherit !important;
  color: inherit !important;
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
}

/* 确保按钮内的字符正确显示 */
.contact-cta .btn-primary i,
.about-us-page .contact-cta .btn-primary i {
  display: inline !important;
  font-style: normal !important;
  font-weight: inherit !important;
  color: inherit !important;
  background: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
}

/* 强制覆盖 - 默认文字显示 */
a[data-text].btn-primary:before,
a.btn-primary[data-text]:before {
  content: attr(data-text) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: absolute !important;
  inset: 0 !important;
  font-size: 15px !important;
  font-weight: bold !important;
  color: white !important;
  letter-spacing: 2px !important;
  opacity: 1 !important;
  transition: all 0.3s ease !important;
  transform: translateY(0) !important;
  z-index: 2 !important;
}

/* 强制覆盖 - 字母默认隐藏 */
a[data-text].btn-primary i,
a.btn-primary[data-text] i {
  color: white !important;
  font-size: 15px !important;
  font-weight: bold !important;
  letter-spacing: 0px !important;
  font-style: normal !important;
  transition: all 0.3s ease !important;
  transform: translateY(20px) !important;
  opacity: 0 !important;
  display: inline-block !important;
  position: relative !important;
  z-index: 3 !important;
}

/* 特定放大：首页CustomSolutions组件中的"获取定制服务方案"按钮 */
/* 使用data-text属性来精确定位这个特定按钮 */
a[data-text*="获取"][data-text*="定制"][data-text*="方案"].btn-primary,
a.btn-primary[data-text*="获取"][data-text*="定制"][data-text*="方案"],
a[data-text*="获取全息投影定制方案"].btn-primary,
a.btn-primary[data-text*="获取全息投影定制方案"] {
  transform: scale(1.4) !important; /* 进一步增加放大比例 */
  min-width: 360px !important; /* 增加中文按钮最小宽度 */
  width: auto !important; /* 允许按钮自动调整宽度 */
  height: 80px !important; /* 进一步增加高度 */
  font-size: 20px !important; /* 进一步增大字体 */
  padding: 0 40px !important; /* 进一步增加内边距 */
  margin: 25px auto !important; /* 增加外边距 */
  white-space: nowrap !important; /* 防止文字换行 */
  flex-wrap: nowrap !important; /* 防止flex项目换行 */
  max-width: 95% !important; /* 稍微增加最大宽度 */
}

/* 英文按钮样式 - 需要更大的宽度 */
a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary,
a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"],
a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary,
a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] {
  transform: scale(1.4) !important; /* 进一步增加放大比例 */
  min-width: 520px !important; /* 进一步增加最小宽度 */
  width: auto !important; /* 允许按钮自动调整宽度 */
  height: 80px !important; /* 进一步增加高度 */
  font-size: 20px !important; /* 进一步增大字体 */
  padding: 0 40px !important; /* 进一步增加内边距 */
  margin: 25px auto !important; /* 增加外边距 */
  white-space: nowrap !important; /* 防止文字换行 */
  flex-wrap: nowrap !important; /* 防止flex项目换行 */
  max-width: 98% !important; /* 增加最大宽度以适应英文文本 */
  overflow: visible !important; /* 确保内容不被裁剪 */
}

/* 特定放大：首页CustomSolutions组件中按钮的默认文字 */
a[data-text*="获取"][data-text*="定制"][data-text*="方案"].btn-primary:before,
a.btn-primary[data-text*="获取"][data-text*="定制"][data-text*="方案"]:before,
a[data-text*="获取全息投影定制方案"].btn-primary:before,
a.btn-primary[data-text*="获取全息投影定制方案"]:before,
/* 英文按钮默认文字样式 */
a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary:before,
a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"]:before,
a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary:before,
a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"]:before {
  font-size: 20px !important; /* 进一步增大默认文字字体 */
}

/* 特定放大：首页CustomSolutions组件中按钮的动画字母 */
a[data-text*="获取"][data-text*="定制"][data-text*="方案"].btn-primary i,
a.btn-primary[data-text*="获取"][data-text*="定制"][data-text*="方案"] i,
a[data-text*="获取全息投影定制方案"].btn-primary i,
a.btn-primary[data-text*="获取全息投影定制方案"] i {
  font-size: 20px !important; /* 进一步增大动画字母字体 */
  white-space: nowrap !important; /* 防止字母换行 */
  flex-shrink: 0 !important; /* 防止字母收缩 */
  display: inline-block !important; /* 确保字母正确显示 */
}

/* 英文按钮动画字母样式 - 特殊处理 */
a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary i,
a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"] i,
a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary i,
a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] i {
  font-size: 20px !important; /* 进一步增大动画字母字体 */
  white-space: nowrap !important; /* 防止字母换行 */
  flex-shrink: 0 !important; /* 防止字母收缩 */
  display: inline-block !important; /* 确保字母正确显示 */
  min-width: auto !important; /* 允许字母自适应宽度 */
  letter-spacing: 0 !important; /* 默认无额外字母间距 */
}

/* 特定放大：首页CustomSolutions组件中按钮悬停时的动画字母 */
a[data-text*="获取"][data-text*="定制"][data-text*="方案"].btn-primary:hover i,
a.btn-primary[data-text*="获取"][data-text*="定制"][data-text*="方案"]:hover i,
a[data-text*="获取全息投影定制方案"].btn-primary:hover i,
a.btn-primary[data-text*="获取全息投影定制方案"]:hover i {
  font-size: 20px !important; /* 进一步增大悬停时的字体大小 */
  letter-spacing: 2px !important; /* 悬停时增加字母间距 */
}

/* 英文按钮悬停时动画字母样式 - 减少字母间距以防止溢出 */
a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary:hover i,
a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"]:hover i,
a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary:hover i,
a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"]:hover i {
  font-size: 20px !important; /* 进一步增大悬停时的字体大小 */
  letter-spacing: 1px !important; /* 英文按钮悬停时使用较小的字母间距 */
}

/* 响应式设计 - 平板设备 */
@media (max-width: 768px) {
  /* 英文按钮在平板设备上的样式调整 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"],
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] {
    min-width: 400px !important; /* 在平板上减少最小宽度 */
    font-size: 16px !important; /* 减小字体 */
    padding: 0 25px !important; /* 减少内边距 */
    transform: scale(1.15) !important; /* 减少缩放比例 */
  }

  /* 平板设备上的字母样式 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary i,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"] i,
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary i,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] i {
    font-size: 16px !important;
  }
}

/* 响应式设计 - 手机设备 */
@media (max-width: 576px) {
  /* 英文按钮在手机设备上的样式调整 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"],
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] {
    min-width: 300px !important; /* 在手机上进一步减少最小宽度 */
    font-size: 14px !important; /* 进一步减小字体 */
    padding: 0 20px !important; /* 进一步减少内边距 */
    transform: scale(1.05) !important; /* 进一步减少缩放比例 */
    height: 60px !important; /* 减少高度 */
  }

  /* 手机设备上的字母样式 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary i,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"] i,
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary i,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"] i {
    font-size: 14px !important;
  }

  /* 手机设备上悬停时减少字母间距 */
  a[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"].btn-primary:hover i,
  a.btn-primary[data-text*="GET"][data-text*="CUSTOM"][data-text*="SERVICE"]:hover i,
  a[data-text*="GET YOUR CUSTOM SERVICE PLAN"].btn-primary:hover i,
  a.btn-primary[data-text*="GET YOUR CUSTOM SERVICE PLAN"]:hover i {
    letter-spacing: 0.5px !important; /* 在手机上使用更小的字母间距 */
  }
}

/*!******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/holographic-guide.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************/
/* Holographic Guide Page Styles */

.holographic-guide-page {
  min-height: 100vh !important;
  background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 100%) !important;
  position: relative !important;
  z-index: 1 !important;
  width: 100% !important;
  overflow-x: hidden !important;
}

.holographic-guide-breadcrumb {
  background: white !important;
  border-bottom: 1px solid #e5e7eb !important;
  padding: 1rem 0 !important;
}

.holographic-guide-breadcrumb nav {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  font-size: 0.875rem !important;
  color: #6b7280 !important;
}

.holographic-guide-breadcrumb a {
  color: #6b7280 !important;
  transition: color 0.3s ease !important;
}

.holographic-guide-breadcrumb a:hover {
  color: #2563eb !important;
}

.holographic-guide-container {
  max-width: 1280px !important;
  margin: 0 auto !important;
  padding: 0 1rem !important;
}

.holographic-guide-header {
  text-align: center !important;
  margin-bottom: 3rem !important;
  padding: 4rem 0 !important;
}

.holographic-guide-badge {
  display: inline-flex !important;
  align-items: center !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 9999px !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  background-color: #dbeafe !important;
  color: #2563eb !important;
  margin-bottom: 1rem !important;
}

.holographic-guide-title {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  color: #111827 !important;
  line-height: 1.2 !important;
  margin-bottom: 1.5rem !important;
}

.holographic-guide-title-accent {
  color: #2563eb !important;
}

.holographic-guide-description {
  font-size: 1.125rem !important;
  color: #6b7280 !important;
  line-height: 1.7 !important;
  max-width: 48rem !important;
  margin: 0 auto !important;
}

.holographic-guide-stats {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 1.5rem !important;
  margin-bottom: 4rem !important;
}

.holographic-guide-stat {
  text-align: center !important;
}

.holographic-guide-stat-number {
  font-size: 1.875rem !important;
  font-weight: 700 !important;
  color: #111827 !important;
  margin-bottom: 0.5rem !important;
}

.holographic-guide-stat-label {
  font-size: 0.875rem !important;
  color: #6b7280 !important;
}

.holographic-guide-steps {
  display: flex !important;
  flex-direction: column !important;
  gap: 2rem !important;
}

.holographic-guide-step {
  background: white !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  padding: 2rem !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

.holographic-guide-step:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

.holographic-guide-step-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  color: #111827 !important;
  margin-bottom: 1rem !important;
}

.holographic-guide-step-list {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.holographic-guide-step-item {
  color: #374151 !important;
  margin-bottom: 0.5rem !important;
  padding-left: 1rem !important;
  position: relative !important;
}

.holographic-guide-step-item:before {
  content: "•" !important;
  color: #2563eb !important;
  font-weight: bold !important;
  position: absolute !important;
  left: 0 !important;
}

.holographic-guide-cta {
  margin-top: 4rem !important;
  text-align: center !important;
}

.holographic-guide-cta-box {
  background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%) !important;
  color: white !important;
  border-radius: 0.5rem !important;
  padding: 2rem !important;
}

.holographic-guide-cta-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 1rem !important;
  color: white !important;
}

.holographic-guide-cta-description {
  color: #bfdbfe !important;
  margin-bottom: 1.5rem !important;
  max-width: 32rem !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.holographic-guide-cta-button {
  display: inline-flex !important;
  align-items: center !important;
  padding: 0.75rem 1.5rem !important;
  background: white !important;
  color: #2563eb !important;
  border-radius: 0.375rem !important;
  font-weight: 500 !important;
  text-decoration: none !important;
  transition: background-color 0.3s ease !important;
}

.holographic-guide-cta-button:hover {
  background-color: #f3f4f6 !important;
  color: #2563eb !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .holographic-guide-title {
    font-size: 2.25rem !important;
  }

  .holographic-guide-container {
    padding: 0 1.5rem !important;
  }
}

@media (max-width: 768px) {
  .holographic-guide-title {
    font-size: 2rem !important;
  }

  .holographic-guide-stats {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  .holographic-guide-step {
    padding: 1.5rem !important;
  }

  .holographic-guide-step-title {
    font-size: 1.25rem !important;
  }

  .holographic-guide-header {
    padding: 2rem 0 !important;
  }

  .holographic-guide-cta-box {
    padding: 1.5rem !important;
  }

  .holographic-guide-cta-title {
    font-size: 1.25rem !important;
  }
}

@media (max-width: 640px) {
  .holographic-guide-title {
    font-size: 1.75rem !important;
  }

  .holographic-guide-description {
    font-size: 1rem !important;
  }

  .holographic-guide-stat-number {
    font-size: 1.5rem !important;
  }

  .holographic-guide-cta-button {
    width: 100% !important;
    justify-content: center !important;
    max-width: 300px !important;
  }
}

/* Force override any conflicting styles */
.holographic-guide-page * {
  box-sizing: border-box !important;
}

.holographic-guide-page .container {
  max-width: 1280px !important;
  margin: 0 auto !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

/* Ensure proper spacing */
.holographic-guide-page .space-y-8 > * + * {
  margin-top: 2rem !important;
}

.holographic-guide-page .space-y-2 > * + * {
  margin-top: 0.5rem !important;
}

.holographic-guide-page .mb-16 {
  margin-bottom: 4rem !important;
}

.holographic-guide-page .mb-12 {
  margin-bottom: 3rem !important;
}

.holographic-guide-page .mb-6 {
  margin-bottom: 1.5rem !important;
}

.holographic-guide-page .mb-4 {
  margin-bottom: 1rem !important;
}

/* 确保页面内容不被其他元素覆盖 */
.holographic-guide-page {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.holographic-guide-breadcrumb {
  position: relative !important;
  z-index: 10 !important;
}

.holographic-guide-container {
  position: relative !important;
  z-index: 5 !important;
  display: block !important;
  visibility: visible !important;
}

.holographic-guide-header {
  position: relative !important;
  z-index: 5 !important;
  display: block !important;
  visibility: visible !important;
}

.holographic-guide-stats {
  position: relative !important;
  z-index: 5 !important;
  display: grid !important;
  visibility: visible !important;
}

.holographic-guide-steps {
  position: relative !important;
  z-index: 5 !important;
  display: flex !important;
  visibility: visible !important;
}

.holographic-guide-cta {
  position: relative !important;
  z-index: 5 !important;
  display: block !important;
  visibility: visible !important;
}

/* 防止被弹窗或其他元素覆盖 */
body.holographic-guide-active {
  overflow-x: hidden !important;
}

/* 强制显示页面内容 */
.holographic-guide-page > * {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/holographic-guide-override.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/* 全息投影购买指南页面专用样式覆盖 - 最高优先级 */

/* 使用最高优先级强制隐藏所有旧样式元素 */
html body div.steps-container,
html body div.step-item,
html body div.premium-card,
html body div.reveal-animation,
html body div.jsx-7ed770b744ab1351,
html body div.purchase-steps,
html body div.step-header,
html body div.step-number,
html body div.step-content,
html body div.step-image,
html body div.step-text,
html body ul.step-list,
html body li.step-list,
html body [class*="step-"]:not(.modern-guide-container):not(.modern-guide-container *),
html body [class*="purchase-"]:not(.modern-guide-container):not(.modern-guide-container *),
html body [class*="guide-"]:not(.modern-guide-container):not(.modern-guide-container *) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  height: 0 !important;
  width: 0 !important;
  overflow: hidden !important;
  position: absolute !important;
  left: -9999px !important;
  z-index: -1 !important;
}

/* 确保我们的现代化组件以最高优先级显示 */
html body .modern-guide-container {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  width: 100% !important;
  overflow: visible !important;
  position: relative !important;
  left: auto !important;
  z-index: 1000 !important;
  background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 100%) !important;
  min-height: 100vh !important;
}

/* 确保所有子元素正常显示 */
html body .modern-guide-container * {
  position: relative !important;
  z-index: 1001 !important;
}

/* 特别处理flex和grid布局 */
html body .modern-guide-container .flex {
  display: flex !important;
}

html body .modern-guide-container .grid {
  display: grid !important;
}

html body .modern-guide-container .inline-block {
  display: inline-block !important;
}

html body .modern-guide-container .inline {
  display: inline !important;
}

/* 强制移除任何可能的旧样式表 */
link[href*="purchase-guide"],
style[data-href*="purchase-guide"] {
  display: none !important;
}

/* 强制覆盖任何可能的背景 */
html body {
  background: transparent !important;
}

/* 额外的强制覆盖规则 */
html body .modern-guide-container .container {
  display: block !important;
  visibility: visible !important;
}

html body .modern-guide-container .space-y-8 > * {
  display: block !important;
  visibility: visible !important;
}

html body .modern-guide-container .space-y-4 > * {
  display: block !important;
  visibility: visible !important;
}

/* 确保卡片组件正常显示 */
html body .modern-guide-container .bg-gradient-to-br {
  display: block !important;
  visibility: visible !important;
}

html body .modern-guide-container .rounded-2xl {
  display: block !important;
  visibility: visible !important;
}

/* 确保文本元素正常显示 */
html body .modern-guide-container h1,
html body .modern-guide-container h2,
html body .modern-guide-container h3,
html body .modern-guide-container h4,
html body .modern-guide-container p,
html body .modern-guide-container span,
html body .modern-guide-container div {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 确保按钮和链接正常显示 */
html body .modern-guide-container button,
html body .modern-guide-container a {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 确保图标正常显示 */
html body .modern-guide-container svg {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/custom-playground-design.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
/* Custom Playground Design Page Styles */

.main-content {
  position: relative;
  overflow-x: hidden;
}

.page-header {
  position: relative;
  overflow: hidden;
}

.bg-gradient {
  background: linear-gradient(135deg, #1a1a2e, #16213e) !important;
  color: white !important;
  padding: 100px 0 !important;
  position: relative;
  overflow: hidden;
}

.bg-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.2;
  z-index: 0;
}

.page-title {
  font-size: 3.5rem !important;
  font-weight: 800 !important;
  letter-spacing: 2px !important;
  margin-bottom: 20px !important;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
  opacity: 1;
  transform: translateY(0);
  position: relative;
  z-index: 1;
}

.animate-title {
  animation: fadeInUp 1s forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.breadcrumbs {
  font-size: 1rem !important;
  letter-spacing: 1px !important;
  position: relative;
  z-index: 1;
}

.breadcrumbs a {
  color: rgba(255, 255, 255, 0.8) !important;
  transition: color 0.3s;
}

.breadcrumbs a:hover {
  color: white !important;
}

.section-padding {
  padding: 100px 0 !important;
}

.bg-light {
  background-color: transparent !important;
}

.enhanced {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 60px !important;
  align-items: center !important;
}

.image-decoration {
  position: relative;
  padding: 20px;
}

.rounded-image {
  border-radius: 12px !important;
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15) !important;
  width: 100% !important;
  height: auto !important;
}

.decoration-element {
  position: absolute;
  width: 120px;
  height: 120px;
  border: 4px solid rgba(26, 26, 46, 0.1);
  z-index: -1;
  border-radius: 50%;
}

.top-right {
  top: -20px;
  right: -20px;
}

.bottom-left {
  bottom: -20px;
  left: -20px;
}

.section-title {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 25px !important;
  line-height: 1.2 !important;
}

.section-title.center {
  text-align: center !important;
}

.accent-border {
  position: relative;
  padding-bottom: 15px;
}

.accent-border:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #1a1a2e, #4e54c8);
}

.accent-line {
  position: relative;
  padding-bottom: 15px;
}

.accent-line:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #1a1a2e, #4e54c8);
}

.section-description {
  font-size: 1.2rem !important;
  color: #666 !important;
  margin-bottom: 50px !important;
  max-width: 800px !important;
  line-height: 1.6 !important;
}

.section-description.center {
  text-align: center !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.lead-text {
  font-size: 1.25rem !important;
  line-height: 1.7 !important;
  margin-bottom: 20px !important;
  font-weight: 300 !important;
}

.supporting-text {
  font-size: 1.1rem !important;
  line-height: 1.7 !important;
  color: #555 !important;
  margin-bottom: 30px !important;
}

.intro-stats {
  display: flex !important;
  margin-top: 40px !important;
  gap: 40px !important;
}

.stat-item {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
}

.stat-number {
  font-size: 2.5rem !important;
  font-weight: 800 !important;
  color: #1a1a2e !important;
  margin-bottom: 5px !important;
}

.stat-label {
  font-size: 0.9rem !important;
  color: #666 !important;
  font-weight: 500 !important;
}

.process-steps {
  display: grid !important;
  gap: 40px !important;
}

.step-item {
  position: relative;
}

.premium-card {
  position: relative;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

.premium-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15) !important;
}

.step-img {
  width: 100% !important;
  height: 400px !important;
  -o-object-fit: cover !important;
     object-fit: cover !important;
}

.step-content-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8)) !important;
  color: white !important;
  padding: 40px 30px 30px !important;
}

.step-number {
  font-size: 3rem !important;
  font-weight: 800 !important;
  color: rgba(255, 255, 255, 0.3) !important;
  margin-bottom: 10px !important;
}

.step-content-overlay h3 {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  margin-bottom: 15px !important;
  color: white !important;
}

.step-content-overlay p {
  font-size: 1rem !important;
  line-height: 1.6 !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

.features-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
  gap: 30px !important;
  margin-top: 50px !important;
}

.premium-feature {
  background: white !important;
  padding: 40px 30px !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  text-align: center !important;
}

.premium-feature:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
}

.feature-icon {
  width: 80px !important;
  height: 80px !important;
  background: linear-gradient(135deg, #1a1a2e, #4e54c8) !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 auto 25px !important;
}

.feature-icon i {
  font-size: 2rem !important;
  color: white !important;
}

.premium-feature h3 {
  font-size: 1.3rem !important;
  font-weight: 600 !important;
  margin-bottom: 15px !important;
  color: #1a1a2e !important;
}

.premium-feature p {
  font-size: 1rem !important;
  line-height: 1.6 !important;
  color: #666 !important;
}

.cta-section {
  background: linear-gradient(135deg, #1a1a2e, #16213e) !important;
  color: white !important;
  padding: 100px 0 !important;
  position: relative;
  overflow: hidden;
}

.cta-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
}

.cta-content {
  text-align: center !important;
  position: relative;
  z-index: 1;
}

.cta-content h2 {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 20px !important;
  color: white !important;
}

.cta-content p {
  font-size: 1.2rem !important;
  margin-bottom: 30px !important;
  color: rgba(255, 255, 255, 0.9) !important;
  max-width: 600px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* 只在项目定制指南页面的CTA区域应用这些按钮样式，且不能有data-text属性 */
.main-content .cta-section .btn-primary:not([data-text]) {
  display: inline-block !important;
  padding: 15px 40px !important;
  background: linear-gradient(135deg, #ff6b6b, #ff5252) !important;
  color: white !important;
  border-radius: 50px !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  text-decoration: none !important;
  border: none !important;
  cursor: pointer !important;
}

.main-content .cta-section .btn-primary:not([data-text]):hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3) !important;
  color: white !important;
}

.main-content .cta-section .btn-glow:not([data-text]) {
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.3) !important;
}

/* Animation Classes */
.reveal-animation {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.reveal-animation.is-revealed {
  opacity: 1;
  transform: translateY(0);
}

.delay-100 { transition-delay: 0.1s; }
.delay-200 { transition-delay: 0.2s; }
.delay-300 { transition-delay: 0.3s; }
.delay-400 { transition-delay: 0.4s; }

/* Responsive Design */
@media (max-width: 768px) {
  .page-title {
    font-size: 2.5rem !important;
  }

  .section-title {
    font-size: 2rem !important;
  }

  .enhanced {
    grid-template-columns: 1fr !important;
    gap: 40px !important;
  }

  .intro-stats {
    flex-wrap: wrap !important;
    gap: 20px !important;
  }

  .stat-number {
    font-size: 2rem !important;
  }

  .cta-content h2 {
    font-size: 2.2rem !important;
  }
}

/* Case Studies Styles */
.case-studies {
  background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 100%) !important;
}

.case-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
  gap: 30px !important;
  margin-top: 50px !important;
}

.case-item {
  background: white !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

.case-item:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
}

.case-image {
  position: relative !important;
  overflow: hidden !important;
  height: 250px !important;
}

.case-image img {
  width: 100% !important;
  height: 100% !important;
  -o-object-fit: cover !important;
     object-fit: cover !important;
  transition: transform 0.3s ease !important;
}

.case-item:hover .case-image img {
  transform: scale(1.05) !important;
}

.case-content {
  padding: 25px !important;
}

.case-content h3 {
  font-size: 1.4rem !important;
  font-weight: 700 !important;
  color: #1a1a2e !important;
  margin-bottom: 15px !important;
}

.case-content p {
  color: #666 !important;
  line-height: 1.6 !important;
  margin-bottom: 20px !important;
}

.case-stats {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 8px !important;
}

.stat-badge {
  background: linear-gradient(135deg, #264eca 0%, #2239b4 100%) !important;
  color: white !important;
  padding: 4px 12px !important;
  border-radius: 20px !important;
  font-size: 0.8rem !important;
  font-weight: 500 !important;
}

/* Testimonials Styles */
.design-testimonials {
  background: #f8fafc !important;
}

.testimonial-slider {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
  gap: 30px !important;
  margin-top: 50px !important;
}

.testimonial-item {
  background: white !important;
  border-radius: 12px !important;
  padding: 30px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
}

.quote-icon {
  position: absolute !important;
  top: -15px !important;
  left: 30px !important;
  background: linear-gradient(135deg, #264eca 0%, #2239b4 100%) !important;
  color: white !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.2rem !important;
}

.testimonial-quote p {
  font-size: 1.1rem !important;
  line-height: 1.7 !important;
  color: #333 !important;
  margin-bottom: 25px !important;
  font-style: italic !important;
}

.testimonial-author {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  border-top: 1px solid #eee !important;
  padding-top: 20px !important;
}

.author-info h4 {
  font-size: 1.1rem !important;
  font-weight: 700 !important;
  color: #1a1a2e !important;
  margin-bottom: 5px !important;
}

.client-title {
  color: #666 !important;
  font-size: 0.9rem !important;
}

.rating {
  display: flex !important;
  gap: 3px !important;
}

.rating i {
  color: #ffd700 !important;
  font-size: 1rem !important;
}

@media (max-width: 768px) {
  .case-grid {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }

  .testimonial-slider {
    grid-template-columns: 1fr !important;
    gap: 20px !important;
  }

  .testimonial-author {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 15px !important;
  }
}

@media (max-width: 576px) {
  .premium-grid {
    grid-template-columns: 1fr !important;
  }

  .page-title {
    font-size: 2rem !important;
  }

  .section-padding {
    padding: 50px 0 !important;
  }

  .cta-content h2 {
    font-size: 1.8rem !important;
  }

  .main-content .cta-section .btn-primary:not([data-text]) {
    width: 100% !important;
    max-width: 300px !important;
  }

  .case-grid {
    grid-template-columns: 1fr !important;
  }

  .testimonial-item {
    padding: 20px !important;
  }

  .testimonial-quote p {
    font-size: 1rem !important;
  }
}

/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[11].oneOf[12].use[3]!./app/styles/animations.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/* 页面元素渐入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDelay {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  50% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加动画类 */
.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-fade-in-delay {
  animation: fadeInDelay 1.2s ease-out forwards;
}

/* 背景图缩放动画 */
@keyframes scaleBackground {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.05);
  }
}

.bg-scale {
  animation: scaleBackground 10s ease-in-out infinite alternate;
}

/* 产品页面专用动画 */
@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 产品页面动画类 */
.animate-fade-in-right {
  animation: fadeInRight 1s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 1s ease-out forwards;
}

.animate-fade-in-up {
  animation: fadeInUp 1s ease-out forwards;
}

/* 背景放大效果 */
.bg-zoom {
  transition: transform 8s ease;
}

/* 移除背景悬停放大效果 */

/* 渐变覆盖层动画 */
@keyframes gradientMove {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-overlay {
  background: linear-gradient(270deg, rgba(0,0,0,0.7), rgba(0,0,0,0.4), rgba(0,0,0,0.7));
  background-size: 200% 200%;
  animation: gradientMove 10s ease infinite;
}

/* 粒子动画 */
@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-40px) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-20px) rotate(270deg);
    opacity: 0.8;
  }
}

/* 脉冲发光动画 */
@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

/* 悬停提升动画 */
@keyframes hoverLift {
  from {
    transform: translateY(0) scale(1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }
  to {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }
}

/* 缩放进入动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 弹跳动画 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0,-4px,0);
  }
}

/* 浮动动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 摇摆动画 */
@keyframes swing {
  20% {
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    transform: rotate3d(0, 0, 1, -5deg);
  }
  100% {
    transform: rotate3d(0, 0, 1, 0deg);
  }
}

/* 动画工具类 */
.animate-scale-in {
  animation: scaleIn 0.6s ease-out;
}

.animate-bounce-custom {
  animation: bounce 1s ease-in-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-swing {
  animation: swing 1s ease-in-out;
}

.animate-pulse-glow {
  animation: pulseGlow 3s ease-in-out infinite;
}

/* 悬停效果类 */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
}

/* 延迟动画 */
.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

.animate-delay-500 {
  animation-delay: 0.5s;
}

/* 滚动触发动画 */
.scroll-animate {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-animate.in-view {
  opacity: 1;
  transform: translateY(0);
}

/* 交错动画 */
.stagger-children > * {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.stagger-children.animate > *:nth-child(1) { transition-delay: 0.1s; }
.stagger-children.animate > *:nth-child(2) { transition-delay: 0.2s; }
.stagger-children.animate > *:nth-child(3) { transition-delay: 0.3s; }
.stagger-children.animate > *:nth-child(4) { transition-delay: 0.4s; }
.stagger-children.animate > *:nth-child(5) { transition-delay: 0.5s; }
.stagger-children.animate > *:nth-child(6) { transition-delay: 0.6s; }

.stagger-children.animate > * {
  opacity: 1;
  transform: translateY(0);
}

/* 性能优化 */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 响应式动画 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
