{"..\\app\\components\\ClientHeader.tsx -> ./Header": {"id": "..\\app\\components\\ClientHeader.tsx -> ./Header", "files": ["static/chunks/_app-pages-browser_app_components_Header_tsx.js"]}, "..\\app\\utils\\i18n.ts -> ../dictionaries/en.json": {"id": "..\\app\\utils\\i18n.ts -> ../dictionaries/en.json", "files": []}, "..\\app\\utils\\i18n.ts -> ../dictionaries/zh.json": {"id": "..\\app\\utils\\i18n.ts -> ../dictionaries/zh.json", "files": []}, "..\\node_modules\\@stagewise\\toolbar-next\\dist\\index.js -> @stagewise/toolbar-react": {"id": "..\\node_modules\\@stagewise\\toolbar-next\\dist\\index.js -> @stagewise/toolbar-react", "files": ["static/chunks/_app-pages-browser_node_modules_stagewise_toolbar-react_dist_index_js.js"]}}