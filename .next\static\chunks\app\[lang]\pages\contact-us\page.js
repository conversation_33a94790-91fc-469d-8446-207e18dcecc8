/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/[lang]/pages/contact-us/page"],{

/***/ "(app-pages-browser)/./app/dictionaries lazy recursive ^\\.\\/.*\\.json$":
/*!****************************************************************!*\
  !*** ./app/dictionaries/ lazy ^\.\/.*\.json$ namespace object ***!
  \****************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var map = {
	"./en.json": "(app-pages-browser)/./app/dictionaries/en.json",
	"./zh.json": "(app-pages-browser)/./app/dictionaries/zh.json"
};

function webpackAsyncContext(req) {
	return Promise.resolve().then(function() {
		if(!__webpack_require__.o(map, req)) {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		}

		var id = map[req];
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = function() { return Object.keys(map); };
webpackAsyncContext.id = "(app-pages-browser)/./app/dictionaries lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "(app-pages-browser)/./app/dictionaries sync recursive ^\\.\\/.*\\.json$":
/*!***********************************************!*\
  !*** ./app/dictionaries/ sync ^\.\/.*\.json$ ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var map = {
	"./en.json": "(app-pages-browser)/./app/dictionaries/en.json",
	"./zh.json": "(app-pages-browser)/./app/dictionaries/zh.json"
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = "(app-pages-browser)/./app/dictionaries sync recursive ^\\.\\/.*\\.json$";

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5C%5Blang%5D%5Cpages%5Ccontact-us%5Cpage.tsx&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5C%5Blang%5D%5Cpages%5Ccontact-us%5Cpage.tsx&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/[lang]/pages/contact-us/page.tsx */ \"(app-pages-browser)/./app/[lang]/pages/contact-us/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz1EJTNBJTVDQUlHQy1kbSU1Q0Nyb3NzLWJvcmRlciUyMEUtY29tbWVyY2UlMjBXZWJzaXRlJTIwUHJvamVjdCU1Q25leHRqcyU1Q2FwcCU1QyU1QmxhbmclNUQlNUNwYWdlcyU1Q2NvbnRhY3QtdXMlNUNwYWdlLnRzeCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/NTMwYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEFJR0MtZG1cXFxcQ3Jvc3MtYm9yZGVyIEUtY29tbWVyY2UgV2Vic2l0ZSBQcm9qZWN0XFxcXG5leHRqc1xcXFxhcHBcXFxcW2xhbmddXFxcXHBhZ2VzXFxcXGNvbnRhY3QtdXNcXFxccGFnZS50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5C%5Blang%5D%5Cpages%5Ccontact-us%5Cpage.tsx&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/styles/contact-us.css":
/*!***********************************!*\
  !*** ./app/styles/contact-us.css ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"ecb023d76a14\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9zdHlsZXMvY29udGFjdC11cy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC9zdHlsZXMvY29udGFjdC11cy5jc3M/OGUwNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImVjYjAyM2Q3NmExNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/styles/contact-us.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/[lang]/pages/contact-us/page.tsx":
/*!**********************************************!*\
  !*** ./app/[lang]/pages/contact-us/page.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ContactPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_LanguageProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../components/LanguageProvider */ \"(app-pages-browser)/./app/components/LanguageProvider.tsx\");\n/* harmony import */ var _components_ContactForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../components/ContactForm */ \"(app-pages-browser)/./app/components/ContactForm.tsx\");\n/* harmony import */ var _components_Globe__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/Globe */ \"(app-pages-browser)/./app/components/Globe.tsx\");\n/* harmony import */ var _styles_contact_us_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../styles/contact-us.css */ \"(app-pages-browser)/./app/styles/contact-us.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ContactPage() {\n    _s();\n    const { t } = (0,_components_LanguageProvider__WEBPACK_IMPORTED_MODULE_1__.useLanguage)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"contact-page\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"page-header\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"page-title\",\n                                children: t(\"contact.title\", {\n                                    fallback: \"联系我们\"\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"page-description\",\n                                children: t(\"contact.description\", {\n                                    fallback: \"想要了解更多产品信息或者其他疑问？我们随时为您提供帮助，让我们一起创造更美好的未来。\"\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"contact-content\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"contact-container\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"contact-info\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"map-decoration\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"info-item\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-map-marker-alt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                        lineNumber: 36,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"info-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                children: t(\"contact.office\", {\n                                                                    fallback: \"Our Office\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                lineNumber: 38,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: t(\"contact.address\", {\n                                                                    fallback: \"Guangzhou City, Guangdong Province, China\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                lineNumber: 39,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                        lineNumber: 37,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"info-item\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-envelope\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                        lineNumber: 48,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"info-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                children: t(\"contact.email_us\", {\n                                                                    fallback: \"Email Us\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                lineNumber: 50,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: t(\"contact.email\", {\n                                                                    fallback: \"<EMAIL>\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                lineNumber: 51,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                        lineNumber: 49,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"info-item\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-phone-alt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                        lineNumber: 56,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"info-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                children: t(\"contact.call_us\", {\n                                                                    fallback: \"Call Us\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                lineNumber: 58,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: t(\"contact.phone\", {\n                                                                    fallback: \"+86 13800138000\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                lineNumber: 59,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                        lineNumber: 57,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"info-item\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-clock\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"info-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                children: t(\"contact.working_hours\", {\n                                                                    fallback: \"Working Hours\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                lineNumber: 66,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: t(\"contact.hours\", {\n                                                                    fallback: \"Monday-Friday: 9:00 AM - 6:00 PM\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                lineNumber: 67,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"info-item\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                        className: \"fas fa-globe\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"info-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                children: t(\"contact.social\", {\n                                                                    fallback: \"Follow Us\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                lineNumber: 74,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"social-links\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: \"#\",\n                                                                        \"aria-label\": \"Facebook\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                            className: \"fab fa-facebook\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                            lineNumber: 77,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                        lineNumber: 76,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: \"#\",\n                                                                        \"aria-label\": \"Twitter\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                            className: \"fab fa-twitter\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                            lineNumber: 80,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                        lineNumber: 79,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: \"#\",\n                                                                        \"aria-label\": \"LinkedIn\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                            className: \"fab fa-linkedin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                            lineNumber: 83,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                        lineNumber: 82,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                        href: \"#\",\n                                                                        \"aria-label\": \"Instagram\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                            className: \"fab fa-instagram\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                            lineNumber: 86,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                        lineNumber: 85,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                                lineNumber: 75,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                        lineNumber: 73,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContactForm__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"world-map-section\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        children: t(\"contact.global_presence\", {\n                                            fallback: \"Our Global Presence\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: t(\"contact.global_description\", {\n                                            fallback: \"We serve customers worldwide with high-quality holographic projection equipment and solutions.\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"globe-container\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Globe__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"interactive-globe\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\pages\\\\contact-us\\\\page.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(ContactPage, \"ot2YhC7pP10gRrIouBKIa40vomw=\", false, function() {\n    return [\n        _components_LanguageProvider__WEBPACK_IMPORTED_MODULE_1__.useLanguage\n    ];\n});\n_c = ContactPage;\nvar _c;\n$RefreshReg$(_c, \"ContactPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[lang]/pages/contact-us/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/ContactForm.tsx":
/*!****************************************!*\
  !*** ./app/components/ContactForm.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ContactForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _LanguageProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./LanguageProvider */ \"(app-pages-browser)/./app/components/LanguageProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ContactForm(param) {\n    let { productName } = param;\n    _s();\n    const { t } = (0,_LanguageProvider__WEBPACK_IMPORTED_MODULE_2__.useLanguage)();\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        phone: \"\",\n        country: \"\",\n        message: productName ? \"I am interested in: \".concat(productName) : \"\",\n        playground_size: \"100-500 sqm\",\n        product: productName || \"\"\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitSuccess, setSubmitSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitError, setSubmitError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsClient(true);\n    }, []);\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setSubmitError(\"\");\n        try {\n            // 提交表单数据到API\n            const response = await fetch(\"/api/form-submissions\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(formData)\n            });\n            const result = await response.json();\n            if (result.success) {\n                console.log(\"Form submitted successfully:\", result);\n                setSubmitSuccess(true);\n                // 清空表单\n                setFormData({\n                    name: \"\",\n                    email: \"\",\n                    phone: \"\",\n                    country: \"\",\n                    message: \"\",\n                    playground_size: \"100-500 sqm\",\n                    product: \"\"\n                });\n            } else {\n                throw new Error(result.message || \"Submission failed\");\n            }\n        } catch (error) {\n            setSubmitError(t(\"contact.form.error\", {\n                fallback: \"Submission failed, please try again later\"\n            }));\n            console.error(\"Submit error:\", error);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"contact-form-container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"Get Your Free Quote\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Fill out the form below and our team will get back to you within 24 hours.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"loading-placeholder\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"form-skeleton\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"skeleton-line\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"skeleton-line\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"skeleton-line\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"skeleton-button\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"contact-form-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                children: t(\"contact.form.title\", {\n                    fallback: \"Get Your Free Quote\"\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: t(\"contact.form.subtitle\", {\n                    fallback: \"Fill out the form below and our team will get back to you within 24 hours.\"\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            submitSuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"success-message\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: \"fas fa-check-circle\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: t(\"contact.form.success.title\", {\n                            fallback: \"Thank You!\"\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: t(\"contact.form.success.message\", {\n                            fallback: \"Your message has been sent successfully. We will contact you shortly.\"\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"contact-form\",\n                children: [\n                    productName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"form-group product-inquiry mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-50 p-3 rounded-lg border border-blue-100 flex items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"shrink-0 text-blue-500 mr-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        className: \"h-5 w-5\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        stroke: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-800\",\n                                            children: [\n                                                t(\"contact.form.product_inquiry\", {\n                                                    fallback: \"Product Inquiry\"\n                                                }),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium text-blue-900\",\n                                            children: productName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"form-row\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"name\",\n                                        children: t(\"contact.form.name\", {\n                                            fallback: \"Full Name *\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"name\",\n                                        name: \"name\",\n                                        value: formData.name,\n                                        onChange: handleChange,\n                                        required: true,\n                                        placeholder: t(\"contact.form.name_placeholder\", {\n                                            fallback: \"Your Name\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"email\",\n                                        children: t(\"contact.form.email\", {\n                                            fallback: \"Email Address *\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        id: \"email\",\n                                        name: \"email\",\n                                        value: formData.email,\n                                        onChange: handleChange,\n                                        required: true,\n                                        placeholder: t(\"contact.form.email_placeholder\", {\n                                            fallback: \"Your Email\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"form-row\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"phone\",\n                                        children: t(\"contact.form.phone\", {\n                                            fallback: \"Phone Number\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"tel\",\n                                        id: \"phone\",\n                                        name: \"phone\",\n                                        value: formData.phone,\n                                        onChange: handleChange,\n                                        placeholder: t(\"contact.form.phone_placeholder\", {\n                                            fallback: \"Your Phone\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"country\",\n                                        children: t(\"contact.form.country\", {\n                                            fallback: \"Country *\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"country\",\n                                        name: \"country\",\n                                        value: formData.country,\n                                        onChange: handleChange,\n                                        required: true,\n                                        placeholder: t(\"contact.form.country_placeholder\", {\n                                            fallback: \"Your Country\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"form-group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"playground_size\",\n                                children: t(\"contact.form.playground_size\", {\n                                    fallback: \"Playground Size\"\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"playground_size\",\n                                name: \"playground_size\",\n                                value: formData.playground_size,\n                                onChange: handleChange,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"100-500 sqm\",\n                                        children: t(\"contact.form.size_option1\", {\n                                            fallback: \"100-500 sqm\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"500-1000 sqm\",\n                                        children: t(\"contact.form.size_option2\", {\n                                            fallback: \"500-1000 sqm\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"1000+ sqm\",\n                                        children: t(\"contact.form.size_option3\", {\n                                            fallback: \"1000+ sqm\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"custom\",\n                                        children: t(\"contact.form.size_option4\", {\n                                            fallback: \"Custom Size\"\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"form-group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"message\",\n                                children: t(\"contact.form.message\", {\n                                    fallback: \"Your Message *\"\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"message\",\n                                name: \"message\",\n                                value: formData.message,\n                                onChange: handleChange,\n                                required: true,\n                                placeholder: t(\"contact.form.message_placeholder\", {\n                                    fallback: \"Please describe your project or inquiry\"\n                                }),\n                                rows: 5\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, this),\n                    submitError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"error-message\",\n                        children: submitError\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: \"btn-primary\",\n                        disabled: isSubmitting,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: isSubmitting ? t(\"contact.form.sending\", {\n                                fallback: \"Sending...\"\n                            }) : t(\"contact.form.submit\", {\n                                fallback: \"Get My Free Quote\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\ContactForm.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n_s(ContactForm, \"QYbVW/cqCs0wMlIncv+KUQRjKLo=\", false, function() {\n    return [\n        _LanguageProvider__WEBPACK_IMPORTED_MODULE_2__.useLanguage\n    ];\n});\n_c = ContactForm;\nvar _c;\n$RefreshReg$(_c, \"ContactForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ContactForm.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/Globe.tsx":
/*!**********************************!*\
  !*** ./app/components/Globe.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Globe; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var cobe__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cobe */ \"(app-pages-browser)/./node_modules/cobe/dist/index.esm.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Globe(param) {\n    let { className = \"\" } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [pointerInteracting, setPointerInteracting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pointerInteractionMovement, setPointerInteractionMovement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [width, setWidth] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    let phi = 0;\n    let x = 0;\n    const onResize = ()=>{\n        if (canvasRef.current) {\n            setWidth(canvasRef.current.offsetWidth);\n        }\n    };\n    const onRender = (state)=>{\n        if (!pointerInteracting) {\n            phi += 0.005;\n        }\n        state.phi = phi + x;\n        state.width = width * 2;\n        state.height = width * 2;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!canvasRef.current) return;\n        window.addEventListener(\"resize\", onResize);\n        onResize();\n        const globe = (0,cobe__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(canvasRef.current, {\n            devicePixelRatio: 2,\n            width: width,\n            height: width,\n            phi: 0,\n            theta: 0.3,\n            dark: 1,\n            diffuse: 0.4,\n            mapSamples: 16000,\n            mapBrightness: 1.2,\n            baseColor: [\n                0.1,\n                0.1,\n                0.2\n            ],\n            markerColor: [\n                0,\n                1,\n                1\n            ],\n            glowColor: [\n                0.2,\n                0.6,\n                1\n            ],\n            markers: [\n                // 北美洲\n                {\n                    location: [\n                        40.7128,\n                        -74.006\n                    ],\n                    size: 0.08\n                },\n                {\n                    location: [\n                        34.0522,\n                        -118.2437\n                    ],\n                    size: 0.06\n                },\n                // 南美洲\n                {\n                    location: [\n                        -23.5505,\n                        -46.6333\n                    ],\n                    size: 0.05\n                },\n                {\n                    location: [\n                        19.4326,\n                        -99.1332\n                    ],\n                    size: 0.04\n                },\n                // 欧洲\n                {\n                    location: [\n                        51.5074,\n                        -0.1278\n                    ],\n                    size: 0.06\n                },\n                {\n                    location: [\n                        48.8566,\n                        2.3522\n                    ],\n                    size: 0.05\n                },\n                // 非洲\n                {\n                    location: [\n                        30.0444,\n                        31.2357\n                    ],\n                    size: 0.05\n                },\n                {\n                    location: [\n                        -26.2041,\n                        28.0473\n                    ],\n                    size: 0.04\n                },\n                // 亚洲\n                {\n                    location: [\n                        39.9042,\n                        116.4074\n                    ],\n                    size: 0.08\n                },\n                {\n                    location: [\n                        35.6762,\n                        139.6503\n                    ],\n                    size: 0.06\n                },\n                {\n                    location: [\n                        19.076,\n                        72.8777\n                    ],\n                    size: 0.05\n                },\n                {\n                    location: [\n                        1.3521,\n                        103.8198\n                    ],\n                    size: 0.04\n                },\n                // 大洋洲\n                {\n                    location: [\n                        -33.8688,\n                        151.2093\n                    ],\n                    size: 0.05\n                }\n            ],\n            onRender: onRender\n        });\n        return ()=>{\n            window.removeEventListener(\"resize\", onResize);\n            globe.destroy();\n        };\n    }, [\n        width\n    ]);\n    const handlePointerDown = (e)=>{\n        setPointerInteracting(e.clientX - pointerInteractionMovement);\n        if (canvasRef.current) {\n            canvasRef.current.style.cursor = \"grabbing\";\n        }\n    };\n    const handlePointerUp = ()=>{\n        setPointerInteracting(null);\n        if (canvasRef.current) {\n            canvasRef.current.style.cursor = \"grab\";\n        }\n    };\n    const handlePointerOut = ()=>{\n        setPointerInteracting(null);\n        if (canvasRef.current) {\n            canvasRef.current.style.cursor = \"grab\";\n        }\n    };\n    const handleMouseMove = (e)=>{\n        if (pointerInteracting !== null) {\n            const delta = e.clientX - pointerInteracting;\n            setPointerInteractionMovement(delta);\n            x = delta / 200;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative mx-auto aspect-square w-full max-w-[600px] \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            className: \"h-full w-full cursor-grab [contain:layout_paint_size]\",\n            onPointerDown: handlePointerDown,\n            onPointerUp: handlePointerUp,\n            onPointerOut: handlePointerOut,\n            onMouseMove: handleMouseMove\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Globe.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\Globe.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_s(Globe, \"SoS3tZhdeRCxcgm3aqBYZcb4wH4=\");\n_c = Globe;\nvar _c;\n$RefreshReg$(_c, \"Globe\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/Globe.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/components/LanguageProvider.tsx":
/*!*********************************************!*\
  !*** ./app/components/LanguageProvider.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: function() { return /* binding */ LanguageProvider; },\n/* harmony export */   useLanguage: function() { return /* binding */ useLanguage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/i18n */ \"(app-pages-browser)/./app/utils/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Create context with default values to avoid null checks\nconst defaultContext = {\n    locale: _utils_i18n__WEBPACK_IMPORTED_MODULE_3__.i18n.defaultLocale,\n    dictionary: {},\n    changeLanguage: ()=>{},\n    t: (key)=>key,\n    isHydrated: false\n};\n// Create context\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(defaultContext);\n// Pre-load default dictionary to ensure SSR works consistently\nlet defaultDictionary = {};\n// Try to preload the default dictionary during module initialization\ntry {\n    // This is a workaround for Next.js to make this work in both SSR and client\n    if (true) {\n        // Use dynamic import with catch for better reliability\n        try {\n            // eslint-disable-next-line @typescript-eslint/no-require-imports -- Workaround for Next.js SSR/client dynamic import\n            defaultDictionary = __webpack_require__(\"(app-pages-browser)/./app/dictionaries sync recursive ^\\\\.\\\\/.*\\\\.json$\")(\"./\".concat(_utils_i18n__WEBPACK_IMPORTED_MODULE_3__.i18n.defaultLocale, \".json\"));\n        } catch (e) {\n            // 如果无法直接require，尝试使用默认导入\n            // eslint-disable-next-line @typescript-eslint/no-require-imports -- Workaround for Next.js SSR/client dynamic import\n            const dictModule = __webpack_require__(\"(app-pages-browser)/./app/dictionaries sync recursive ^\\\\.\\\\/.*\\\\.json$\")(\"./\".concat(_utils_i18n__WEBPACK_IMPORTED_MODULE_3__.i18n.defaultLocale, \".json\"));\n            defaultDictionary = dictModule.default || dictModule;\n        }\n        // Validate that the dictionary has common keys\n        if (!defaultDictionary || !defaultDictionary.common) {\n            console.warn(\"Default dictionary missing common keys, falling back to empty dictionary\");\n            defaultDictionary = {\n                common: {}\n            };\n        }\n    }\n} catch (error) {\n    console.error(\"Error pre-loading default dictionary:\", error);\n    defaultDictionary = {\n        common: {}\n    };\n}\n// Provider component\nfunction LanguageProvider(param) {\n    let { children } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // Always start with default dictionary for consistent SSR\n    const [dictionary, setDictionary] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultDictionary);\n    // Always start with default locale for consistent SSR\n    const [locale, setLocale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_utils_i18n__WEBPACK_IMPORTED_MODULE_3__.i18n.defaultLocale);\n    // Add hydration state tracking\n    const [isHydrated, setIsHydrated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Determine current language from URL or localStorage\n    const getCurrentLocale = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        // Try to get from pathname first (works on both server and client)\n        if (pathname) {\n            for (const loc of _utils_i18n__WEBPACK_IMPORTED_MODULE_3__.i18n.locales){\n                // renamed locale to loc to avoid conflict\n                if (pathname.startsWith(\"/\".concat(loc, \"/\")) || pathname === \"/\".concat(loc)) {\n                    return loc;\n                }\n            }\n        }\n        // Try to get from localStorage (client-side only)\n        if ( true && isHydrated) {\n            try {\n                const storedLocale = localStorage.getItem(\"NEXT_LOCALE\");\n                if (storedLocale && _utils_i18n__WEBPACK_IMPORTED_MODULE_3__.i18n.locales.includes(storedLocale)) {\n                    return storedLocale;\n                }\n            } catch (error) {\n                console.error(\"Error accessing localStorage:\", error);\n            }\n        }\n        // Default to English\n        return _utils_i18n__WEBPACK_IMPORTED_MODULE_3__.i18n.defaultLocale;\n    }, [\n        pathname,\n        isHydrated\n    ]); // Added dependencies for useCallback\n    // Mark when component is hydrated on client\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsHydrated(true);\n        // Update locale when client-side hydration is complete\n        const newLocale = getCurrentLocale();\n        if (newLocale !== locale) {\n            setLocale(newLocale);\n        }\n    }, [\n        getCurrentLocale,\n        locale\n    ]); // Added getCurrentLocale and locale to dependencies\n    // Update locale when pathname changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only run this after hydration is complete\n        if (isHydrated) {\n            const newLocale = getCurrentLocale();\n            if (newLocale !== locale) {\n                setLocale(newLocale);\n            }\n        }\n    }, [\n        isHydrated,\n        pathname,\n        getCurrentLocale,\n        locale\n    ]); // Added getCurrentLocale and locale\n    // Load dictionary when locale changes, but only after hydration\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Skip if not hydrated yet\n        if (!isHydrated) return;\n        const loadDictionary = async ()=>{\n            try {\n                // Always load dictionary regardless of locale\n                const dict = await __webpack_require__(\"(app-pages-browser)/./app/dictionaries lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(\"./\".concat(locale, \".json\"));\n                setDictionary(dict.default);\n            } catch (error) {\n                console.error(\"Error loading dictionary:\", error);\n                // Fallback to default locale if there's an error\n                if (locale !== _utils_i18n__WEBPACK_IMPORTED_MODULE_3__.i18n.defaultLocale) {\n                    try {\n                        const defaultDict = await __webpack_require__(\"(app-pages-browser)/./app/dictionaries lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(\"./\".concat(_utils_i18n__WEBPACK_IMPORTED_MODULE_3__.i18n.defaultLocale, \".json\"));\n                        setDictionary(defaultDict.default);\n                    } catch (fallbackError) {\n                        console.error(\"Error loading fallback dictionary:\", fallbackError);\n                        setDictionary(defaultDictionary);\n                    }\n                } else {\n                    setDictionary(defaultDictionary);\n                }\n            }\n        };\n        loadDictionary();\n    }, [\n        locale,\n        isHydrated\n    ]);\n    // Translation function - safe for both server and client\n    const t = function(key) {\n        let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const fallbackString = typeof params.fallback === \"string\" ? params.fallback : key;\n        if (!isHydrated) {\n            return fallbackString;\n        }\n        if (!key || typeof dictionary !== \"object\" || dictionary === null) {\n            return fallbackString;\n        }\n        const keys = key.split(\".\");\n        let currentValue = dictionary;\n        for (const k of keys){\n            if (typeof currentValue !== \"object\" || currentValue === null || !Object.prototype.hasOwnProperty.call(currentValue, k)) {\n                return fallbackString;\n            }\n            currentValue = currentValue[k];\n        }\n        if (typeof currentValue !== \"string\") {\n            return fallbackString;\n        }\n        let result = currentValue;\n        Object.entries(params).forEach((param)=>{\n            let [paramKey, paramValue] = param;\n            if (paramKey !== \"fallback\" && (typeof paramValue === \"string\" || typeof paramValue === \"number\")) {\n                result = result.replace(new RegExp(\"{{\".concat(paramKey, \"}}\"), \"g\"), String(paramValue));\n            }\n        });\n        return result;\n    };\n    // Change language and update URL/localStorage\n    const changeLanguage = (newLocale)=>{\n        if (!_utils_i18n__WEBPACK_IMPORTED_MODULE_3__.i18n.locales.includes(newLocale)) {\n            console.error(\"Language \".concat(newLocale, \" is not supported\"));\n            return;\n        }\n        // Set cookie for middleware compatibility (client-side only)\n        if (true) {\n            try {\n                document.cookie = \"NEXT_LOCALE=\".concat(newLocale, \"; path=/; max-age=31536000\"); // 1 year\n            } catch (error) {\n                console.error(\"Error setting cookie:\", error);\n            }\n        }\n        // Set localStorage for persistence (client-side only)\n        if (true) {\n            try {\n                localStorage.setItem(\"NEXT_LOCALE\", newLocale);\n            } catch (error) {\n                console.error(\"Error setting localStorage:\", error);\n            }\n        }\n        setLocale(newLocale);\n        // Redirect to the same path but with new locale\n        if (pathname) {\n            const currentPath = pathname.replace(/^\\/[a-zA-Z-]+/, \"\") || \"/\";\n            router.push(\"/\".concat(newLocale).concat(currentPath));\n            router.refresh();\n        } else {\n            // Fallback if pathname is null\n            router.push(\"/\".concat(newLocale));\n            router.refresh();\n        }\n    };\n    const contextValue = {\n        locale,\n        dictionary,\n        changeLanguage,\n        t,\n        isHydrated\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: contextValue,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            suppressHydrationWarning: true,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\LanguageProvider.tsx\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\components\\\\LanguageProvider.tsx\",\n        lineNumber: 241,\n        columnNumber: 5\n    }, this);\n}\n_s(LanguageProvider, \"X94iNMRY6EeP1iD39HLt9zEKeKQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname\n    ];\n});\n_c = LanguageProvider;\n// Custom hook to use the language context\nfunction useLanguage() {\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n}\n_s1(useLanguage, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"LanguageProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/LanguageProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/utils/i18n.ts":
/*!***************************!*\
  !*** ./app/utils/i18n.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createI18nInstance: function() { return /* binding */ createI18nInstance; },\n/* harmony export */   getDictionary: function() { return /* binding */ getDictionary; },\n/* harmony export */   i18n: function() { return /* binding */ i18n; }\n/* harmony export */ });\n/* harmony import */ var _dictionaries_zh_json__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dictionaries/zh.json */ \"(app-pages-browser)/./app/dictionaries/zh.json\");\n/* harmony import */ var _dictionaries_en_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../dictionaries/en.json */ \"(app-pages-browser)/./app/dictionaries/en.json\");\n// i18n.ts - Simplified implementation without external dependencies\n\n\n// Supported languages\nconst i18n = {\n    locales: [\n        \"en\",\n        \"zh\"\n    ],\n    defaultLocale: \"en\"\n};\n// 静态字典 - 在动态导入失败时作为后备\nconst staticDictionaries = {\n    en: _dictionaries_en_json__WEBPACK_IMPORTED_MODULE_1__,\n    zh: _dictionaries_zh_json__WEBPACK_IMPORTED_MODULE_0__\n};\n// Create a simple i18n instance (without i18next)\nfunction createI18nInstance() {\n    let locale = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : i18n.defaultLocale;\n    return {\n        locale,\n        t: (key)=>{\n            return key; // This is a placeholder; actual translation will be done in LanguageProvider\n        }\n    };\n}\n// Load dictionary\nasync function getDictionary(locale) {\n    // Ensure locale is one of the supported locales\n    const validLocale = i18n.locales.includes(locale) ? locale : i18n.defaultLocale;\n    try {\n        // 先尝试使用静态导入的字典\n        if (validLocale in staticDictionaries) {\n            return staticDictionaries[validLocale];\n        }\n        throw new Error(\"Locale not found in static dictionaries\");\n    } catch (error) {\n        // 静态导入失败，尝试动态导入\n        try {\n            // Use explicit imports for each language to avoid dynamic import issues\n            if (validLocale === \"en\") {\n                const enDict = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../dictionaries/en.json */ \"(app-pages-browser)/./app/dictionaries/en.json\", 19));\n                return enDict.default || enDict;\n            } else if (validLocale === \"zh\") {\n                const zhDict = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../dictionaries/zh.json */ \"(app-pages-browser)/./app/dictionaries/zh.json\", 19));\n                return zhDict.default || zhDict;\n            } else {\n                // Fallback to English\n                const enDict = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../dictionaries/en.json */ \"(app-pages-browser)/./app/dictionaries/en.json\", 19));\n                return enDict.default || enDict;\n            }\n        } catch (importError) {\n            console.error(\"Error loading dictionary:\", importError);\n            // Provide a minimal fallback dictionary to prevent crashes\n            return {\n                common: {\n                    home: \"Home\",\n                    products: \"Products\",\n                    loading: \"Loading...\"\n                },\n                products: {\n                    title: \"Products\",\n                    all_products_title: \"All Products\",\n                    all_products_subtitle: \"Browse our full range of products\"\n                }\n            };\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/utils/i18n.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false; // Track which Fiber(s) schedule render work.\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n} // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  {\n    if (typeof type.tag === 'number') {\n      error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n    }\n  }\n\n  if (typeof type === 'function') {\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n        return getContextName(context) + '.Consumer';\n\n      case REACT_PROVIDER_TYPE:\n        var provider = type;\n        return getContextName(provider._context) + '.Provider';\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n\n    }\n  }\n\n  return null;\n}\n\nvar assign = Object.assign;\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, source, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, source, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, source, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, source, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), source, ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar loggedTypeFailures = {};\nvar ReactDebugCurrentFrame$1 = ReactSharedInternals.ReactDebugCurrentFrame;\n\nfunction setCurrentlyValidatingElement$1(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame$1.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame$1.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction checkPropTypes(typeSpecs, values, location, componentName, element) {\n  {\n    // $FlowFixMe[incompatible-use] This is okay but Flow doesn't know it.\n    var has = Function.call.bind(hasOwnProperty);\n\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error$1 = void 0; // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            // eslint-disable-next-line react-internal/prod-error-codes\n            var err = Error((componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' + 'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' + 'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.');\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n\n          error$1 = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED');\n        } catch (ex) {\n          error$1 = ex;\n        }\n\n        if (error$1 && !(error$1 instanceof Error)) {\n          setCurrentlyValidatingElement$1(element);\n\n          error('%s: type specification of %s' + ' `%s` is invalid; the type checker ' + 'function must return `null` or an `Error` but returned a %s. ' + 'You may have forgotten to pass an argument to the type checker ' + 'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' + 'shape all require an argument).', componentName || 'React class', location, typeSpecName, typeof error$1);\n\n          setCurrentlyValidatingElement$1(null);\n        }\n\n        if (error$1 instanceof Error && !(error$1.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error$1.message] = true;\n          setCurrentlyValidatingElement$1(element);\n\n          error('Failed %s type: %s', location, error$1.message);\n\n          setCurrentlyValidatingElement$1(null);\n        }\n      }\n    }\n  }\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar ReactCurrentOwner$1 = ReactSharedInternals.ReactCurrentOwner;\nvar RESERVED_PROPS = {\n  key: true,\n  ref: true,\n  __self: true,\n  __source: true\n};\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner$1.current && self && ReactCurrentOwner$1.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner$1.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner$1.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingRef = function () {\n      if (!specialPropRefWarningShown) {\n        specialPropRefWarningShown = true;\n\n        error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingRef.isReactWarning = true;\n    Object.defineProperty(props, 'ref', {\n      get: warnAboutAccessingRef,\n      configurable: true\n    });\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, ref, self, source, owner, props) {\n  var element = {\n    // This tag allows us to uniquely identify this as a React Element\n    $$typeof: REACT_ELEMENT_TYPE,\n    // Built-in properties that belong on the element\n    type: type,\n    key: key,\n    ref: ref,\n    props: props,\n    // Record the component responsible for creating this element.\n    _owner: owner\n  };\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // self and source are DEV only properties.\n\n    Object.defineProperty(element, '_self', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: self\n    }); // Two elements created in two different places should be considered\n    // equal for testing purposes and therefore we hide it from enumeration.\n\n    Object.defineProperty(element, '_source', {\n      configurable: false,\n      enumerable: false,\n      writable: false,\n      value: source\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, source, self) {\n  {\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      ref = config.ref;\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && !RESERVED_PROPS.hasOwnProperty(propName)) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    return ReactElement(type, key, ref, self, source, ReactCurrentOwner$1.current, props);\n  }\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, element._source, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nvar propTypesMisspellWarningShown;\n\n{\n  propTypesMisspellWarningShown = false;\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  {\n    return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n\nfunction getSourceInfoErrorAddendum(source) {\n  {\n    if (source !== undefined) {\n      var fileName = source.fileName.replace(/^.*[\\\\\\/]/, '');\n      var lineNumber = source.lineNumber;\n      return '\\n\\nCheck your code at ' + fileName + ':' + lineNumber + '.';\n    }\n\n    return '';\n  }\n}\n/**\n * Warn if there's no key explicitly set on dynamic arrays of children or\n * object keys are not valid. This allows us to keep track of children between\n * updates.\n */\n\n\nvar ownerHasKeyUseWarning = {};\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = typeof parentType === 'string' ? parentType : parentType.displayName || parentType.name;\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Given an element, validate that its props follow the propTypes definition,\n * provided by the type.\n *\n * @param {ReactElement} element\n */\n\n\nfunction validatePropTypes(element) {\n  {\n    var type = element.type;\n\n    if (type === null || type === undefined || typeof type === 'string') {\n      return;\n    }\n\n    if (type.$$typeof === REACT_CLIENT_REFERENCE) {\n      return;\n    }\n\n    var propTypes;\n\n    if (typeof type === 'function') {\n      propTypes = type.propTypes;\n    } else if (typeof type === 'object' && (type.$$typeof === REACT_FORWARD_REF_TYPE || // Note: Memo only checks outer props here.\n    // Inner props are checked in the reconciler.\n    type.$$typeof === REACT_MEMO_TYPE)) {\n      propTypes = type.propTypes;\n    } else {\n      return;\n    }\n\n    if (propTypes) {\n      // Intentionally inside to avoid triggering lazy initializers:\n      var name = getComponentNameFromType(type);\n      checkPropTypes(propTypes, element.props, 'prop', name, element);\n    } else if (type.PropTypes !== undefined && !propTypesMisspellWarningShown) {\n      propTypesMisspellWarningShown = true; // Intentionally inside to avoid triggering lazy initializers:\n\n      var _name = getComponentNameFromType(type);\n\n      error('Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?', _name || 'Unknown');\n    }\n\n    if (typeof type.getDefaultProps === 'function' && !type.getDefaultProps.isReactClassApproved) {\n      error('getDefaultProps is only used on classic React.createClass ' + 'definitions. Use a static property named `defaultProps` instead.');\n    }\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar didWarnAboutKeySpread = {};\nfunction jsxWithValidation(type, props, key, isStaticChildren, source, self) {\n  {\n    var validType = isValidElementType(type); // We warn in this case but don't throw. We expect the element creation to\n    // succeed and there will likely be errors in render.\n\n    if (!validType) {\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var sourceInfo = getSourceInfoErrorAddendum(source);\n\n      if (sourceInfo) {\n        info += sourceInfo;\n      } else {\n        info += getDeclarationErrorAddendum();\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    }\n\n    var element = jsxDEV$1(type, props, key, source, self); // The result can be nullish if a mock or a custom function is used.\n    // TODO: Drop this when these are no longer allowed as the type argument.\n\n    if (element == null) {\n      return element;\n    } // Skip key warning if the type isn't valid since our key validation logic\n    // doesn't expect a non-string/function type and can throw confusing errors.\n    // We don't want exception behavior to differ between dev and prod.\n    // (Rendering will throw with a helpful message and as soon as the type is\n    // fixed, the key warnings will appear.)\n\n\n    if (validType) {\n      var children = props.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    }\n\n    if (hasOwnProperty.call(props, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(props).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    } else {\n      validatePropTypes(element);\n    }\n\n    return element;\n  }\n} // These two functions exist to still get child warnings in dev\n\nvar jsxDEV = jsxWithValidation ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/NGE1YyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/navigation.js":
/*!*****************************************!*\
  !*** ./node_modules/next/navigation.js ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6IkFBQUEsK0pBQStEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L25hdmlnYXRpb24uanM/M2UxYyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGlzdC9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/cobe/dist/index.esm.js":
/*!*********************************************!*\
  !*** ./node_modules/cobe/dist/index.esm.js ***!
  \*********************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ p; }\n/* harmony export */ });\n/* harmony import */ var phenomenon__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! phenomenon */ \"(app-pages-browser)/./node_modules/phenomenon/dist/phenomenon.mjs\");\nvar M=\"phi\",R=\"theta\",c=\"mapSamples\",O=\"mapBrightness\",N=\"baseColor\",G=\"markerColor\",s=\"glowColor\",S=\"markers\",P=\"diffuse\",X=\"devicePixelRatio\",f=\"dark\",u=\"offset\",m=\"scale\",x=\"opacity\",l=\"mapBaseBrightness\",I={[M]:\"A\",[R]:\"B\",[c]:\"l\",[O]:\"E\",[N]:\"R\",[G]:\"S\",[s]:\"y\",[P]:\"F\",[f]:\"G\",[u]:\"x\",[m]:\"C\",[x]:\"H\",[l]:\"I\"},{PI:i,sin:d,cos:U}=Math,C=r=>[].concat(...r.map(E=>{let[_,o]=E.location;_=_*i/180,o=o*i/180-i;let a=U(_);return[-a*U(o),d(_),a*d(o),E.size]}),[0,0,0,0]),p=(r,E)=>{let _=(e,t,L)=>({type:e,value:typeof E[t]==\"undefined\"?L:E[t]}),o=r.getContext(\"webgl\")?\"webgl\":\"experimental-webgl\",a=new phenomenon__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({canvas:r,contextType:o,context:{alpha:!0,stencil:!1,antialias:!0,depth:!1,preserveDrawingBuffer:!1,...E.context},settings:{[X]:E[X]||1,onSetup:e=>{let t=e.RGB,L=e.UNSIGNED_BYTE,n=e.TEXTURE_2D,T=e.createTexture();e.bindTexture(n,T),e.texImage2D(n,0,t,1,1,0,t,L,new Uint8Array([0,0,0,0]));let A=new Image;A.onload=()=>{e.bindTexture(n,T),e.texImage2D(n,0,t,t,L,A),e.generateMipmap(n);let h=e.getParameter(e.CURRENT_PROGRAM),v=e.getUniformLocation(h,\"J\");e.texParameteri(n,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(n,e.TEXTURE_MAG_FILTER,e.NEAREST),e.uniform1i(v,0)},A.src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAACAAQAAAADMzoqnAAAAAXNSR0IArs4c6QAABA5JREFUeNrV179uHEUAx/Hf3JpbF+E2VASBsmVKTBcpKJs3SMEDcDwBiVJAAewYEBUivIHT0uUBIt0YCovKD0CRjUC4QfHYh8hYXu+P25vZ2Zm9c66gMd/GJ/tz82d3bk8GN4SrByYF2366FNTACIAkivVAAazQdnf3MvAlbNUQfOPAdQDvSAimMWhwy4I2g4SU+Kp04ISLpPBAKLxPyic3O/CCi+Y7rUJbiodcpDOFY7CgxCEXmdYD2EYK2s5lApOx5pEDDYCUwM1XdJUwBV11QQMg59kePSCaPAASQMEL2hwo6TJFgxpg+TgC2ymXPbuvc40awr3D1QCFfbH9kcoqAOkZozpQo0aqAGQRKCog/+tjkgbNFEtg2FffBvBGlSxHoAaAa1u6X4PBAwDiR8FFsrQgeUhfJTSALaB9jy5NCybJPn1SVFiWk7ywN+KzhH1aKAuydhGkbEF4lWohLXDXavlyFgHY7LBnLRdlAP6BS5Cc8RfVDXbkwN/oIvmY+6obbNeBP0JwTuMGu9gTzy1Q4RS/cWpfzszeYwd+CAFrtBW/Hur0gLbJGlD+/OjVwe/drfBxkbbg63dndEDfiEBlAd7ac0BPe1D6Jd8dfbLH+RI0OzseFB5s01/M+gMdAeluLOCAuaUA9Lezo/vSgXoCX9rtEiXnp7Q1W/CNyWcd8DXoS6jH/YZ5vAJEWY2dXFQe2TUgaFaNejCzJ98g6HnlVrsE58sDcYqg+9XY75fPqdoh/kRQWiXKg8MWlJQxUFMPjqnyujhFBE7UxIMjyszk0QwQlFsezImsyvUYYYVED2pk6m0Tg8T04Fwjk2kdAwSACqlM6gRRt3vQYAFGX0Ah7Ebx1H+MDRI5ui0QldH4j7FGcm90XdxD2Jg1AOEAVAKhEFXSn4cKUELurIAKwJ3MArypPscQaLhJFICJ0ohjDySAdH8AhDtCiTuMycH8CXzhH9jUACAO5uMhoAwA5i+T6WAKmmAqnLy80wxHqIPFYpqCwxGaYLt4Dyievg5kEoVEUAhs6pqKgFtDQYOuaXypaWKQfIuwwoGSZgfLsu/XAtI8cGN+h7Cc1A5oLOMhwlIPXuhu48AIvsSBkvtV9wsJRKCyYLfq5lTrQMFd1a262oqBck9K1V0YjQg0iEYYgpS1A9GlXQV5cykwm4A7BzVsxQqo7E+zCegO7Ma7yKgsuOcfKbMBwLC8wvVNYDsANYalEpOAa6zpWjTeMKGwEwC1CiQewJc5EKfgy7GmRAZA4vUVGwE2dPM/g0xuAInE/yG5aZ8ISxWGfYigUVbdyBElTHh2uCwGdfCkOLGgQVBh3Ewp+/QK4CDlR5Ws/Zf7yhCf8pH7vinWAvoVCQ6zz0NX5V/6GkAVV+2/5qsJ/gU8bsxpM8IeAQAAAABJRU5ErkJggg==\"}}});return a.add(\"\",{vertex:\"attribute vec3 aPosition;uniform mat4 uProjectionMatrix;uniform mat4 uModelMatrix;uniform mat4 uViewMatrix;void main(){gl_Position=uProjectionMatrix*uModelMatrix*uViewMatrix*vec4(aPosition,1.);}\",fragment:\"precision highp float;uniform vec2 t,x;uniform vec3 R,S,y;uniform vec4 z[64];uniform float A,B,l,C,D,E,F,G,H,I;uniform sampler2D J;float K=1./l;mat3 L(float a,float b){float c=cos(a),d=cos(b),e=sin(a),f=sin(b);return mat3(d,f*e,-f*c,0.,c,e,f,d*-e,d*c);}vec3 w(vec3 c,out float v){c=c.xzy;float p=max(2.,floor(log2(2.236068*l*3.141593*(1.-c.z*c.z))*.72021));vec2 g=floor(pow(1.618034,p)/2.236068*vec2(1.,1.618034)+.5),d=fract((g+1.)*.618034)*6.283185-3.883222,e=-2.*g,f=vec2(atan(c.y,c.x),c.z-1.),q=floor(vec2(e.y*f.x-d.y*(f.y*l+1.),-e.x*f.x+d.x*(f.y*l+1.))/(d.x*e.y-e.x*d.y));float n=3.141593;vec3 r;for(float h=0.;h<4.;h+=1.){vec2 s=vec2(mod(h,2.),floor(h*.5));float j=dot(g,q+s);if(j>l)continue;float a=j,b=0.;if(a>=524288.)a-=524288.,b+=.803894;if(a>=262144.)a-=262144.,b+=.901947;if(a>=131072.)a-=131072.,b+=.950973;if(a>=65536.)a-=65536.,b+=.475487;if(a>=32768.)a-=32768.,b+=.737743;if(a>=16384.)a-=16384.,b+=.868872;if(a>=8192.)a-=8192.,b+=.934436;if(a>=4096.)a-=4096.,b+=.467218;if(a>=2048.)a-=2048.,b+=.733609;if(a>=1024.)a-=1024.,b+=.866804;if(a>=512.)a-=512.,b+=.433402;if(a>=256.)a-=256.,b+=.216701;if(a>=128.)a-=128.,b+=.108351;if(a>=64.)a-=64.,b+=.554175;if(a>=32.)a-=32.,b+=.777088;if(a>=16.)a-=16.,b+=.888544;if(a>=8.)a-=8.,b+=.944272;if(a>=4.)a-=4.,b+=.472136;if(a>=2.)a-=2.,b+=.236068;if(a>=1.)a-=1.,b+=.618034;float k=fract(b)*6.283185,i=1.-2.*j*K,m=sqrt(1.-i*i);vec3 o=vec3(cos(k)*m,sin(k)*m,i);float u=length(c-o);if(u<n)n=u,r=o;}v=n;return r.xzy;}void main(){vec2 b=(gl_FragCoord.xy/t*2.-1.)/C-x*vec2(1.,-1.)/t;b.x*=t.x/t.y;float c=dot(b,b);vec4 M=vec4(0.);float m=0.;if(c<=.64){for(int d=0;d<2;d++){vec4 e=vec4(0.);float a;vec3 u=vec3(0.,0.,1.),f=normalize(vec3(b,sqrt(.64-c)));f.z*=d>0?-1.:1.,u.z*=d>0?-1.:1.;vec3 g=f*L(B,A),h=w(g,a);float n=asin(h.y),i=acos(-h.x/cos(n));i=h.z<0.?-i:i;float N=max(texture2D(J,vec2(i*.5/3.141593,-(n/3.141593+.5))).x,I),O=smoothstep(8e-3,0.,a),j=dot(f,u),v=pow(j,F)*E,o=N*O*v,T=mix((1.-o)*pow(j,.4),o,G)+.1;e+=vec4(R*T,1.);int U=int(D);float p=0.;for(int k=0;k<64;k++){if(k>=U)break;vec4 q=z[k];vec3 r=q.xyz,P=r-g;float s=q.w;if(dot(P,P)>s*s*4.)continue;vec3 V=w(r,a);a=length(V-g),a<s?p+=smoothstep(s*.5,0.,a):0.;}p=min(1.,p*v),e.xyz=mix(e.xyz,S,p),e.xyz+=pow(1.-j,4.)*y,M+=e*(1.+(d>0?-H:H))/2.;}m=pow(dot(normalize(vec3(-b,sqrt(1.-c))),vec3(0.,0.,1.)),4.)*smoothstep(0.,1.,.2/(c-.64));}else{float Q=sqrt(.2/(c-.64));m=smoothstep(.5,1.,Q/(Q+1.));}gl_FragColor=M+vec4(m*y,m);}\",uniforms:{t:{type:\"vec2\",value:[E.width,E.height]},A:_(\"float\",M),B:_(\"float\",R),l:_(\"float\",c),E:_(\"float\",O),I:_(\"float\",l),R:_(\"vec3\",N),S:_(\"vec3\",G),F:_(\"float\",P),y:_(\"vec3\",s),G:_(\"float\",f),z:{type:\"vec4\",value:C(E[S])},D:{type:\"float\",value:E[S].length},x:_(\"vec2\",u,[0,0]),C:_(\"float\",m,1),H:_(\"float\",x,1)},mode:4,geometry:{vertices:[{x:-100,y:100,z:0},{x:-100,y:-100,z:0},{x:100,y:100,z:0},{x:100,y:-100,z:0},{x:-100,y:-100,z:0},{x:100,y:100,z:0}]},onRender:({uniforms:e})=>{let t={};if(E.onRender){t=E.onRender(t)||t;for(let L in I)t[L]!==void 0&&(e[I[L]].value=t[L]);t[S]!==void 0&&(e[\"z\"].value=C(t[S]),e[\"D\"].value=t[S].length),t.width&&t.height&&(e[\"t\"].value=[t.width,t.height])}}}),a};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/cobe/dist/index.esm.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/phenomenon/dist/phenomenon.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/phenomenon/dist/phenomenon.mjs ***!
  \*****************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\nvar t=[\"x\",\"y\",\"z\"],e=function(t){Object.assign(this,{uniforms:{},geometry:{vertices:[{x:0,y:0,z:0}]},mode:0,modifiers:{},attributes:[],multiplier:1,buffers:[]}),Object.assign(this,t),this.prepareProgram(),this.prepareUniforms(),this.prepareAttributes()};e.prototype.compileShader=function(t,e){var i=this.gl.createShader(t);return this.gl.shaderSource(i,e),this.gl.compileShader(i),i},e.prototype.prepareProgram=function(){var t=this.gl,e=this.vertex,i=this.fragment,r=t.createProgram();t.attachShader(r,this.compileShader(35633,e)),t.attachShader(r,this.compileShader(35632,i)),t.linkProgram(r),t.useProgram(r),this.program=r},e.prototype.prepareUniforms=function(){for(var t=Object.keys(this.uniforms),e=0;e<t.length;e+=1){var i=this.gl.getUniformLocation(this.program,t[e]);this.uniforms[t[e]].location=i}},e.prototype.prepareAttributes=function(){void 0!==this.geometry.vertices&&this.attributes.push({name:\"aPosition\",size:3}),void 0!==this.geometry.normal&&this.attributes.push({name:\"aNormal\",size:3}),this.attributeKeys=[];for(var t=0;t<this.attributes.length;t+=1)this.attributeKeys.push(this.attributes[t].name),this.prepareAttribute(this.attributes[t])},e.prototype.prepareAttribute=function(e){for(var i=this.geometry,r=this.multiplier,s=i.vertices,n=i.normal,a=new Float32Array(r*s.length*e.size),o=0;o<r;o+=1)for(var h=e.data&&e.data(o,r),u=o*s.length*e.size,f=0;f<s.length;f+=1)for(var c=0;c<e.size;c+=1){var l=this.modifiers[e.name];a[u]=void 0!==l?l(h,f,c,this):\"aPosition\"===e.name?s[f][t[c]]:\"aNormal\"===e.name?n[f][t[c]]:h[c],u+=1}this.attributes[this.attributeKeys.indexOf(e.name)].data=a,this.prepareBuffer(this.attributes[this.attributeKeys.indexOf(e.name)])},e.prototype.prepareBuffer=function(t){var e=t.data,i=t.name,r=t.size,s=this.gl.createBuffer();this.gl.bindBuffer(34962,s),this.gl.bufferData(34962,e,35044);var n=this.gl.getAttribLocation(this.program,i);this.gl.enableVertexAttribArray(n),this.gl.vertexAttribPointer(n,r,5126,!1,0,0),this.buffers[this.attributeKeys.indexOf(t.name)]={buffer:s,location:n,size:r}},e.prototype.render=function(t){var e=this,i=this.uniforms,r=this.multiplier,s=this.gl;s.useProgram(this.program);for(var n=0;n<this.buffers.length;n+=1){var a=this.buffers[n],o=a.location,h=a.buffer,u=a.size;s.enableVertexAttribArray(o),s.bindBuffer(34962,h),s.vertexAttribPointer(o,u,5126,!1,0,0)}Object.keys(t).forEach(function(e){i[e].value=t[e].value}),Object.keys(i).forEach(function(t){var r=i[t];e.uniformMap[r.type](r.location,r.value)}),s.drawArrays(this.mode,0,r*this.geometry.vertices.length),this.onRender&&this.onRender(this)},e.prototype.destroy=function(){for(var t=0;t<this.buffers.length;t+=1)this.gl.deleteBuffer(this.buffers[t].buffer);this.gl.deleteProgram(this.program),this.gl=null};var i=function(t){var e=this,i=t||{},r=i.canvas;void 0===r&&(r=document.querySelector(\"canvas\"));var s=i.context;void 0===s&&(s={});var n=i.contextType;void 0===n&&(n=\"experimental-webgl\");var a=i.settings;void 0===a&&(a={});var o=r.getContext(n,Object.assign({alpha:!1,antialias:!1},s));Object.assign(this,{gl:o,canvas:r,uniforms:{},instances:new Map,shouldRender:!0}),Object.assign(this,{devicePixelRatio:1,clearColor:[1,1,1,1],position:{x:0,y:0,z:2},clip:[.001,100]}),Object.assign(this,a),this.uniformMap={float:function(t,e){return o.uniform1f(t,e)},vec2:function(t,e){return o.uniform2fv(t,e)},vec3:function(t,e){return o.uniform3fv(t,e)},vec4:function(t,e){return o.uniform4fv(t,e)},mat2:function(t,e){return o.uniformMatrix2fv(t,!1,e)},mat3:function(t,e){return o.uniformMatrix3fv(t,!1,e)},mat4:function(t,e){return o.uniformMatrix4fv(t,!1,e)}},o.enable(o.DEPTH_TEST),o.depthFunc(o.LEQUAL),!1===o.getContextAttributes().alpha&&(o.clearColor.apply(o,this.clearColor),o.clearDepth(1)),this.onSetup&&this.onSetup(o),window.addEventListener(\"resize\",function(){return e.resize()}),this.resize(),this.render()};i.prototype.resize=function(){var t=this.gl,e=this.canvas,i=this.devicePixelRatio,r=this.position;e.width=e.clientWidth*i,e.height=e.clientHeight*i;var s=t.drawingBufferWidth,n=t.drawingBufferHeight,a=s/n;t.viewport(0,0,s,n);var o=Math.tan(Math.PI/180*22.5),h=[1,0,0,0,0,1,0,0,0,0,1,0,r.x,r.y,(a<1?1:a)*-r.z,1];this.uniforms.uProjectionMatrix={type:\"mat4\",value:[.5/o,0,0,0,0,a/o*.5,0,0,0,0,-(this.clip[1]+this.clip[0])/(this.clip[1]-this.clip[0]),-1,0,0,-2*this.clip[1]*(this.clip[0]/(this.clip[1]-this.clip[0])),0]},this.uniforms.uViewMatrix={type:\"mat4\",value:[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]},this.uniforms.uModelMatrix={type:\"mat4\",value:h}},i.prototype.toggle=function(t){t!==this.shouldRender&&(this.shouldRender=void 0!==t?t:!this.shouldRender,this.shouldRender&&this.render())},i.prototype.render=function(){var t=this;this.gl.clear(16640),this.instances.forEach(function(e){e.render(t.uniforms)}),this.onRender&&this.onRender(this),this.shouldRender&&requestAnimationFrame(function(){return t.render()})},i.prototype.add=function(t,i){void 0===i&&(i={uniforms:{}}),void 0===i.uniforms&&(i.uniforms={}),Object.assign(i.uniforms,JSON.parse(JSON.stringify(this.uniforms))),Object.assign(i,{gl:this.gl,uniformMap:this.uniformMap});var r=new e(i);return this.instances.set(t,r),r},i.prototype.remove=function(t){var e=this.instances.get(t);void 0!==e&&(e.destroy(),this.instances.delete(t))},i.prototype.destroy=function(){var t=this;this.instances.forEach(function(e,i){e.destroy(),t.instances.delete(i)}),this.toggle(!1)};/* harmony default export */ __webpack_exports__[\"default\"] = (i);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/phenomenon/dist/phenomenon.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/dictionaries/en.json":
/*!**********************************!*\
  !*** ./app/dictionaries/en.json ***!
  \**********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = JSON.parse('{"common":{"home":"Home","products":"Products","services":"Services","solutions":"Solutions","about":"About Us","contact":"Contact Us","blog":"Blog","logo_alt":"Guangzhou Junsheng Technology Co., Ltd.","loading":"Loading...","product_not_found":"Product not found","back_to_products":"Back to products","price":"Price","category":"Category","availability":"Availability","in_stock":"In Stock","out_of_stock":"Out of Stock","id":"ID","key_features":"Key Features","feature1":"Customizable design","feature2":"Premium materials","feature3":"Safety compliance","feature4":"Expert installation","contact_now":"Contact Now","product_details":"Product Details","specifications":"Specifications","reviews":"Reviews","related_products":"You may also like","view_details":"View Details","no_products":"No products found in this category","learn_more":"Learn More","contact_us":"Contact Us","submit":"Submit","whatsapp":"WhatsApp","email":"Email","top":"Top","years_experience":"Years"},"home":{"heroTitle":"Custom Indoor Playground Solutions","heroSubtitle":"Leading manufacturer of indoor playground equipment and trampoline parks","ctaButton":"Get a Quote","featuredProducts":"Featured Products","viewAll":"View All Products","slider":{"product":{"alt":"Guangzhou Junsheng Technology Co., Ltd. Equipment","title":"Immersive Dining Experience","subtitle":"Premium holographic projection restaurant solutions, creating unique dining experiences"},"design":{"alt":"Custom Playground Design","title":"Custom Playground Design","subtitle":"Innovative stage visual solutions, enhancing performance and event impact"},"indoor":{"alt":"AR Interactive","title":"AR Interactive Experience","subtitle":"Smart interactive solutions combining AR technology, creating next-generation experience spaces"},"climbing":{"alt":"Motion Climbing","title":"Smart Climbing Experience","subtitle":"Innovative climbing solutions combining sports and technology, inspiring unlimited challenge fun"},"beach":{"alt":"Interactive Beach","title":"Interactive Ocean World","subtitle":"Immersive ocean-themed interactive experience, letting children explore the magical underwater world"}}},"about":{"subtitle":"About Us","title":"About Guangzhou Junsheng Technology Co., Ltd.","paragraph1":"Guangzhou Junsheng Technology Co., Ltd. is a creative technology enterprise specializing in the research, development, production and application of interactive multimedia, AR somatosensory, holographic projection digital sports and other products. Office area is 1300 square meters, showroom area is 500 square meters (holographic banquet hall, naked eye space, children\'s entertainment and educational products).","paragraph2":"Main business: interactive projection products, digital sports series, AR somatosensory, scene games, holographic KTV giant screen, holographic wedding banquet and other customized products. Committed to interactive multimedia creative design and production, software technology research and development, human-computer intelligent interaction research and development, with visual algorithm technology and motion recognition technology as the core.","paragraph3":"Junsheng Technology adheres to the business philosophy of \'Promising the Future, Innovation Never Stops\', injecting more fresh elements into the industry. The company has always pursued excellent service quality, perfect customer experience and high-level professional service team.","exploreButton":"Explore More About Us","stats":{"countries":"Business coverage in 80+ countries and regions","registration":"Safety certifications in 27 countries","partners":"More than 10 global strategic partners","certificates":"Over 100 playground solutions professionally certified","projects":"Projects Completed","experience":"Years of Experience","safety":"Safety Record","office_area":"Office Area","showroom_area":"Showroom Area","service_quality":"Service Quality","years":"Years of Industry Experience"},"team":{"title":"Guangzhou Junsheng Technology Co., Ltd. Team"},"certificates":{"title":"Professional Certifications","description":"We are committed to innovation and quality assurance, ensuring every playground undergoes rigorous testing for safety and reliable operation.","alt":"Certificate"},"cta":{"title":"Ready to Create Your Dream Playground?","button":"Contact Us Now","description":"Ready to create an amazing indoor playground?"},"image_alt":"About Us - Indoor Playground Equipment","company_name":"Junsheng Amusement Equipment","company_profile":"Company Profile","years":"Years","quality":"Quality","about_us":"About Us","global_leading":"Global Leading","holographic_solution_provider":"Holographic Solution Provider","junsheng_description":"Guangzhou Junsheng Technology Co., Ltd., established in 2008, is a high-tech enterprise focusing on holographic projection and interactive multimedia technology. We are committed to providing advanced and innovative visual experience solutions for global customers.","company_rd_description":"The company has a strong R&D team and professional production team, providing comprehensive services from creative planning, content production to system integration, creating unique visual experiences and interactive effects for customers.","mission":{"title":"Our Mission","paragraph1":"We are dedicated to creating innovative and safe indoor playground equipment that brings joy to children worldwide.","paragraph2":"With over 15 years of experience, we combine creativity, safety, and fun to deliver exceptional play experiences."},"tech":{"title":"Core Technologies","item1":{"title":"3D Holographic Projection","description":"Create three-dimensional floating images without special glasses, providing you with a real three-dimensional visual experience"},"item2":{"title":"Naked-Eye 5D Interactive Projection","description":"Combining motion sensing with holographic imaging, allowing audiences to naturally interact with projected content without any devices"},"item3":{"title":"Immersive Restaurant/Banquet Hall","description":"Transform dining spaces into dynamic art canvases, creating unique dining experiences through wall and table projections"},"item4":{"title":"Technology Exhibition Hall","description":"Create engaging interactive display spaces for enterprises and cultural exhibitions, enhancing brand communication effectiveness"}}},"productSections":{"title":"Product Series Showcase","description":"Explore our comprehensive product solutions covering multiple technology fields","clickToView":"Click to view more","section1":{"title":"Interactive Projection Series","description":"Innovative interactive projection technology creating immersive experience spaces"},"section2":{"title":"Holographic Display Series","description":"Advanced holographic projection technology presenting stunning visual effects"},"section3":{"title":"Digital Sandbox Series","description":"Smart digital sandbox enabling precise display and interactive control"},"section4":{"title":"AR Augmented Reality Series","description":"AR augmented reality technology merging virtual and real worlds"},"section5":{"title":"Smart Integrated Machine Series","description":"Integrated smart devices providing complete solutions"}},"products":{"title":"Our Products","filter":{"all":"All Products"},"sort":"Sort by","moreDetails":"More Details","description":"Explore our indoor playground equipment and trampoline park solutions designed for various scenarios. Each solution can be customized to your specific space and requirements.","loading_error":"Error loading products: ","retry":"Retry","no_products":"No products available","no_search_results":"No matching products found","search_keyword":"Search keyword: ","try_other_keywords":"Please try other keywords or browse all products","total_products":"Total {{count}} products, page {{current}} of {{total}}","search_results":"Search \\"{{query}}\\" found {{count}} products, page {{current}} of {{total}}","previous_page":"Previous","next_page":"Next","no_description":"No description available","categories":{"holographic":"Indoor Playground Equipment","interactive":"Trampoline Parks","restaurant":"Soft Play Areas","exhibition":"Adventure Parks","outdoor":"Interactive Play Systems"},"product_categories":{"运动娱乐":"Sports & Entertainment","游戏娱乐":"Gaming & Entertainment","教育科技":"Educational Technology","娱乐社交":"Social Entertainment","商业设备":"Commercial Equipment","教育展示":"Educational Display","商业娱乐":"Commercial Entertainment","儿童娱乐":"Children\'s Entertainment","全息科技":"Holographic Technology"},"indoor":{"description":"Explore our innovative holographic projection solutions designed for spaces of all sizes, creating engaging visual experiences. Each holographic system can be customized to fit your space and content requirements.","features":{"title":"Holographic Projection Features","themes":{"title":"Themed Content","description":"Immersive themed holographic displays that spark imagination and create unforgettable experiences."},"multi":{"title":"Multi-Layer Projection Systems","description":"Exciting multi-dimensional projection spaces featuring 3D holograms, interactive elements, and immersive environments."},"toddler":{"title":"Interactive Zones","description":"Safe, user-friendly interactive zones designed for all age groups."},"interactive":{"title":"Interactive Elements","description":"Engaging activities that promote learning through visual interaction and digital engagement."}},"cta":{"title":"Ready to Create Your Holographic Experience?","description":"Contact our team today to discuss your custom holographic projection project.","button":"Start Now"}},"trampoline":{"description":"Explore our customizable interactive projection solutions designed for maximum engagement and visual impact. Our interactive systems provide thrilling experiences for visitors of all ages.","features":{"title":"Interactive Projection Features","jump":{"title":"Interactive Zones","description":"Multiple interactive areas with professional-grade projection systems for all engagement levels."},"sports":{"title":"Interactive Games","description":"Interactive gaming, virtual sports, and other exciting digital activities."},"foam":{"title":"Immersive Areas","description":"Safe immersive zones with 360-degree projection for practicing and exploring virtual environments."},"safety":{"title":"Safety Systems","description":"Comprehensive safety features including motion sensors, emergency stops, and professional-grade equipment."}},"cta":{"title":"Ready to Build Your Interactive Projection System?","description":"Contact our team today to discuss your custom interactive projection project.","button":"Start Now"}},"small":{"description":"Our 100-500 sqm holographic projection solutions are perfect for smaller venues looking to maximize visual impact in limited space. These compact yet feature-rich designs offer excellent return on investment for shopping malls, exhibition centers, and retail locations.","benefits":{"title":"Benefits of 100-500 sqm Holographic Systems","cost":{"title":"Lower Initial Investment","description":"Smaller footprint means reduced startup costs while still delivering a complete holographic experience."},"location":{"title":"More Location Options","description":"Fits in various commercial spaces, opening up more potential venue choices."},"roi":{"title":"Faster Return on Investment","description":"Lower operating costs and overhead typically result in quicker ROI."},"efficient":{"title":"Space Efficiency","description":"Our designs maximize visual impact per square meter through clever multi-layer projection systems."}},"cta":{"title":"Perfect Solution for Your Small Space?","description":"Contact our team today to discuss how we can create an amazing holographic experience in your 100-500 sqm space.","button":"Get Your Custom Quote"}},"medium":{"description":"Our 500-1000 sqm holographic projection solutions offer the perfect balance of space and functionality, ideal for dedicated exhibition centers and larger commercial venues. These medium-sized systems provide diverse visual experiences while maintaining operational efficiency.","benefits":{"title":"Benefits of 500-1000 sqm Holographic Systems","capacity":{"title":"Optimal Capacity","description":"Accommodate more visitors simultaneously while maintaining comfortable viewing environments."},"variety":{"title":"Greater Content Variety","description":"Room for more diverse holographic elements, themed zones, and specialized interactive activities."},"party":{"title":"Enhanced Event Facilities","description":"Space for multiple event areas, increasing revenue potential from celebrations and corporate events."},"balance":{"title":"Balanced Investment","description":"Perfect balance between investment size and revenue potential for sustainable business."}},"cta":{"title":"The Ideal Size for Your Exhibition Center?","description":"Contact our team today to explore our 500-1000 sqm holographic projection solutions tailored to your needs.","button":"Discuss Your Project"}},"large":{"description":"Our 1000+ sqm holographic projection solutions represent the ultimate in immersive visual experiences. These large-scale exhibition destinations offer comprehensive holographic options, multiple interactive zones, and the capacity to become regional attraction destinations.","benefits":{"title":"Benefits of 1000+ sqm Holographic Systems","destination":{"title":"Destination Appeal","description":"Become a regional exhibition destination attracting visitors from wider geographic areas."},"attractions":{"title":"Multiple Attractions","description":"Space for diverse holographic zones, multiple interactive areas, and complementary digital entertainment options."},"amenities":{"title":"Extended Amenities","description":"Room for full-service visitor centers, cafes, retail areas, and extensive viewing areas for guests."},"revenue":{"title":"Maximum Revenue Potential","description":"Higher capacity and longer visit durations translate to greater revenue opportunities."}},"cta":{"title":"Planning a Major Exhibition Destination?","description":"Contact our team today to discuss your vision for a large-scale holographic projection or exhibition center.","button":"Start Your Large Project"}},"all_products_title":"All Products","all_products_subtitle":"Browse our complete product series","search_title":"Find the Product You Need","search_subtitle":"Search our product catalog to discover the perfect solution for your needs","search_placeholder":"Search product name, model or keywords...","popular_searches":"Popular Searches","search_tags":"Interactive Projection,Digital Sandbox,Holographic Projection,AR Experience,VR Equipment"},"features":{"title":"3D Holographic & Interactive Projection Features","description":"Explore Guangzhou Junsheng Technology\'s leading 3D holographic projection, naked-eye 5D interactive projection, immersive restaurants, and tech exhibition hall solutions. Our innovative visual technologies create stunning experiences for commercial spaces, exhibitions, and events, serving clients in over 80 countries worldwide.","cta":"Get Your Free Quote Now","prev_slide":"Previous","next_slide":"Next","badge":"Innovative Technology","title_prefix":"Advanced","title_main":"Holographic Technology","innovative":"Innovative Technology","learn_more":"Learn More","projection":{"title":"3D Holographic Projection","subtitle":"Stunning Spatial Visual Experience","description":"Using advanced 3D spatial imaging technology to create ultra-high-definition holographic images viewable without glasses or devices, delivering immersive visual impact for audiences.","specs":{"spec1":"4K Ultra HD Projection","spec2":"270° Viewing Angle","spec3":"Auto Brightness Adjust"}},"interactive":{"title":"Naked-Eye 5D Interactive Projection","subtitle":"Immersive Human-Computer Interaction","description":"Integrating advanced motion capture and gesture recognition technology to enable natural interaction between viewers and projected content, creating true immersive 5D experiences for exhibitions, events, and commercial spaces.","specs":{"spec1":"Infrared Tracking System","spec2":"60 FPS Response Rate","spec3":"Multi-touch Support"}},"materials":{"title":"Outdoor Floor Interactive Projection","subtitle":"Innovative Public Space Interaction","description":"High-brightness floor interactive projection systems designed specifically for outdoor environments, combining waterproof technology with intelligent interactive systems to create engaging experiences for plazas, parks, and commercial districts.","specs":{"spec1":"IP65 Waterproof & Dustproof","spec2":"8000 Lumens Brightness","spec3":"All-weather Operation Design"}},"software":{"title":"Immersive Restaurant Solutions","subtitle":"Digital Dining Experience Innovation","description":"Creating immersive dining environments through wall, table, and spatial projection technologies combined with interactive elements, delivering unique themed restaurant experiences that increase customer satisfaction and return rates.","specs":{"spec1":"Multi-zone Synchronization","spec2":"Smart Scene Switching","spec3":"Touch Menu Interaction"}},"application":{"title":"Banquet Hall Multimedia Systems","subtitle":"Premium Event Space AV Solutions","description":"Providing integrated multimedia solutions for hotel banquet halls and conference centers, including holographic projection, LED displays, and integrated lighting and sound systems to create spectacular wedding, celebration, and corporate event spaces.","specs":{"spec1":"Central Control System","spec2":"One-click Scene Switch","spec3":"Multi-channel Audio System"}},"support":{"title":"Tech Exhibition Hall Design & Construction","subtitle":"One-Stop Corporate Showcase Services","description":"Offering complete process services from creative planning and spatial design to multimedia system integration, content production, and construction management, creating modern, tech-forward, interactive exhibition spaces for enterprises.","specs":{"spec1":"3D Spatial Design","spec2":"Custom Content Creation","spec3":"24H Technical Support"}}},"solutions":{"title":"Customized Solutions for Play Environments","cta":"Get Your Custom Service Plan","product1":"3D Electronic Sandbox | Smart Interactive Sand Table System","product2":"AR Motion Trampoline | Augmented Reality Sports Experience","product3":"AR Education System | Immersive Learning Experience","product4":"Holographic Screen System | Naked-Eye 3D Display Technology","product5":"Children Interactive Beach | Safe & Fun Play Zone","product6":"Holographic Stage System | Stunning Visual Performance","product7":"Bowling Interactive System | Tech Sports Entertainment","product8":"Banquet Hall System | Premium Business Display","badge1":"3D Sandbox","badge2":"AR Trampoline","badge3":"AR Education","badge4":"KTV","badge5":"All-in-One","badge6":"Interactive Ball","badge7":"Interactive Football","badge8":"Motion Climbing"},"serviceFeatures":{"title":"After-Sales Service System","description":"Professional - Quality - Service, providing comprehensive after-sales support system for you","guidance":{"title":"Full Guidance","description":"Customers only need to provide basic information such as venue area, we provide door-to-door services including delivery, installation, hardware and software configuration, and site layout"},"delivery":{"title":"Delivery Process","description":"Electronic equipment must be tested for at least 48 hours before shipment to prevent product quality issues"},"afterSales":{"title":"After-Sales Process","description":"Engineering technology 1-on-1 24-hour service, establish after-sales WeChat group, provide remote assistance and solutions at the first time"},"quality":{"title":"Professional - Quality - Service","description":"Free product planning CAD drawings, multiple scenarios, multiple difficulty levels, content continuously enriched and updated to enhance fun, regular products updated annually for free"}},"custom_playground":{"title":"Custom Holographic Projection Design","subtitle":"Need a Custom Holographic Solution? Let Us Create Your Vision!","cta":"Get Your Custom Plan","badge":"Customization","title_prefix":"Custom","title_main":"Holographic Solutions","description":"Tailored holographic projection solutions for your brand, venue, or event, providing one-stop service from creative planning to implementation and delivery. We focus on creating unique visual experiences, adding a sense of technology and future to your project.","cta_button":"Get Custom Solution","contact_button":"Contact Us","image_alt":"Custom Holographic Solutions","page_title":"Project Customization Guide","breadcrumb_home":"Home","breadcrumb_solutions":"Custom Solutions","breadcrumb_current":"Project Customization Guide","design_process_title":"Our Design Process","design_process_subtitle":"Five professional steps to create exceptional holographic experiences, from creative conception to final implementation","loading":"Loading...","learn_more":"Learn More","view_details":"View Details","related_products":"Related Products","product_details":"Product Details","view_more_products":"View More Products","explore_complete_series":"Explore our complete product series","browse_all":"Browse All","back_to_products":"Back to Products","product_gallery":"Product Gallery","application_scenario":"Application Scenario","professional":"Professional","reviews":"reviews","get_quote":"Get Quote","in_stock":"In Stock","professional_installation":"Professional Installation","three_year_warranty":"3-Year Warranty","watch_demo":"Watch Demo","overlay":{"badge":"Premium Custom","title":"Professional Holographic Solutions","description":"Premium Custom · Visual Impact · Interactive Experience"},"experience_years":"Years Experience","services":{"creative":{"title":"Creative Planning","description":"Professional creative team customizes holographic content and visual effects based on brand characteristics"},"spatial":{"title":"Spatial Design","description":"Optimize projection layout and interactive experience design based on venue environment and purpose"},"content":{"title":"3D Content Creation","description":"Professional 3D modeling team creates high-quality holographic projection content and visual effects"},"installation":{"title":"Professional Installation","description":"Experienced engineering team ensures precise equipment installation and system debugging"}},"design_steps":{"step1":{"title":"Creative Conception","description":"Deep understanding of your needs, developing innovative holographic projection concept solutions, ensuring every detail meets your expectations. From visual design to technical architecture, every aspect is carefully planned and repeatedly optimized.","features":["Requirement Analysis","Creative Planning","Concept Design","Solution Confirmation"]},"step2":{"title":"Solution Design","description":"Professional team conducts detailed design, ensuring perfect combination of visual effects and technical feasibility, creating unique experiences. From visual design to technical architecture, every aspect is carefully planned and repeatedly optimized.","features":["Visual Design","Technical Architecture","Interaction Design","Effect Preview"]},"step3":{"title":"Technical Development","description":"Using advanced technology for development and production, ensuring precise presentation of holographic effects, pursuing perfection in every frame. Using the latest holographic projection technology and professional equipment to ensure optimal visual effects.","features":["Content Creation","System Development","Equipment Debugging","Effect Optimization"]},"step4":{"title":"Testing & Optimization","description":"Rigorous testing of every detail, continuous optimization until achieving the best display effect, ensuring foolproof results. Through multiple rounds of testing and optimization, ensuring project stability and perfect presentation.","features":["Function Testing","Performance Optimization","Compatibility Testing","User Experience Optimization"]},"step5":{"title":"Deployment & Implementation","description":"Professional team on-site installation and debugging, ensuring smooth project delivery and providing comprehensive follow-up technical support. From equipment installation to system debugging, full professional service ensuring perfect delivery.","features":["On-site Installation","System Debugging","Training & Delivery","After-sales Support"]}},"projects":{"title":"Our Custom Project Cases","subtitle":"View our successful holographic projection project cases created for different clients","project1":{"title":"Holographic Theme Restaurant","subtitle":"1,200㎡ Underwater Theme Immersive Dining Experience","description":"Large-scale immersive holographic display space, covering 1200 square meters, serving over 500 customers daily.","tags":["Large Commercial","Immersive Experience","Theme Restaurant"],"stats":["1200㎡","500+/day","Industry Benchmark"]},"project2":{"title":"Corporate Holographic Exhibition Hall","subtitle":"800㎡ Corporate Image Display Space","description":"Corporate holographic exhibition hall comprehensively showcases corporate strength and product advantages, enhancing brand influence.","tags":["Corporate Exhibition","Brand Display","Technology Innovation"],"stats":["800㎡","Brand Enhancement","Tech Innovation"]},"project3":{"title":"Museum Holographic Zone","subtitle":"2,000㎡ Historical Culture Theme Display","description":"Cultural artifact holographic restoration display system, allowing precious artifacts to meet audiences in new forms.","tags":["Cultural Heritage","Digital Protection","Museum Project"],"stats":["2000㎡","Ministry Approved","Digital Rebirth"]}},"features":{"title":"Holographic Customization Features","subtitle":"Our holographic customization solutions have multiple advantages, ensuring we create exclusive visual impact experiences for you","feature1":{"title":"8K Ultra HD Quality","description":"Using the latest 8K ultra-high-definition technology, presenting unprecedented detailed and realistic holographic images"},"feature2":{"title":"Cutting-edge Holographic Technology","description":"Using the most advanced naked-eye 3D/5D holographic projection technology, viewable without any equipment"},"feature3":{"title":"Smart Interactive System","description":"Combining motion sensing and AI technology, allowing audiences to naturally interact with projection content"},"feature4":{"title":"Industrial-grade Stability","description":"Military-grade hardware configuration, 24-hour uninterrupted stable operation"},"feature5":{"title":"Exclusive Custom Solutions","description":"Tailor-made exclusive solutions according to your unique needs"},"feature6":{"title":"Full Technical Support","description":"Providing 7*24 hour professional technical team support"}}},"cases":{"title_prefix":"Classic","title_main":"Cases","description":"We provide innovative holographic projection solutions for global clients, meeting the unique needs of various industries","carousel":{"slide1":{"title":"Holographic Projection Exhibition Case","description":"Immersive holographic projection technology showcase"},"slide2":{"title":"Interactive Projection Experience","description":"Innovative interactive projection solutions"},"slide3":{"title":"Digital Restaurant Case","description":"Holographic projection restaurant experience"},"slide4":{"title":"Corporate Exhibition Hall Case","description":"Enterprise-level holographic display solutions"},"slide5":{"title":"Hotel Space Case","description":"Hotel holographic projection decoration"},"slide6":{"title":"Retail Space Case","description":"Retail environment holographic applications"},"slide7":{"title":"Cultural Display Case","description":"Cultural venue holographic projection"},"slide8":{"title":"Science Museum Case","description":"Technology exhibition holographic solutions"},"slide9":{"title":"Education Training Case","description":"Educational industry holographic application solutions"},"slide10":{"title":"Medical Health Case","description":"Medical field holographic projection technology"},"slide11":{"title":"Entertainment Leisure Case","description":"Entertainment venue holographic experience design"},"slide12":{"title":"Commercial Display Case","description":"Commercial space holographic display solutions"},"slide13":{"title":"Cultural Tourism Scenic Area Case","description":"Tourism scenic area holographic projection applications"},"slide14":{"title":"Smart City Case","description":"Urban planning holographic display system"},"slide15":{"title":"Industrial Manufacturing Case","description":"Industrial field holographic technology applications"},"slide16":{"title":"Artistic Creation Case","description":"Art exhibition holographic projection innovation"}},"filter":{"all":"All","exhibition":"Exhibition","interactive":"Interactive Projection","restaurant":"Restaurant","corporate":"Corporate","hotel":"Hotel","retail":"Retail"},"case1":{"title":"Huangshan Zhenqu Town Light and Shadow Paradise","location":"China, Anhui"},"case2":{"title":"Jiangshangyuan Light and Shadow Art Exhibition","location":"China, Fuzhou"},"case3":{"title":"Huizhou Huamao Tiandi \'City of Light\'","location":"China, Huizhou"},"case4":{"title":"Jixi Revolutionary Martyrs Memorial Hall","location":"China, Jixi"},"case5":{"title":"Xishui Mihuatang Children\'s Light and Shadow Agricultural Theme Park","location":"China, Xishui"},"case6":{"title":"Heilongjiang Science and Technology Museum","location":"China, Harbin"},"view_case_btn":"View Case","view_details_btn":"View Details","view_all_btn":"View All Cases"},"globalCases":{"title":"Global Case Showcase: Indoor Playground Projects","solutions":"Indoor Playground & Trampoline Park Solutions","countries":{"usa":"USA","canada":"Canada","saudiArabia":"Saudi Arabia","russia":"Russia","india":"India","indonesia":"Indonesia"},"showcase":{"USA":"USA Showcase","Canada":"Canada Showcase","Saudi_Arabia":"Saudi Arabia Showcase","Russia":"Russia Showcase","India":"India Showcase","Indonesia":"Indonesia Showcase"}},"factory":{"title":"Guangzhou Junsheng Technology Production Base","description":"Take a tour of our state-of-the-art manufacturing facilities where we create high-quality holographic projection solutions with sophisticated craftsmanship and innovative technology.","title_prefix":"Factory Tour","slider_label":"Production Facility Views","info_btn":"View Quality & Certification Info","info_btn_aria":"Show factory information","prev_slide_aria":"Previous slide","next_slide_aria":"Next slide","slide_dot_aria":"Go to slide {{index}}","info_card":{"header":"Quality Assurance & Manufacturing Excellence","description":"Our production facility is equipped with industry-leading equipment and precision instruments, operating strictly according to ISO9001 quality management system to ensure every product meets international standards.","cert_title":"International Quality Certifications"},"stats":{"years":"Years of Industry Experience","products":"Products Delivered"},"certifications":{"ce":"CE European Safety Standards","iso":"ISO 9001:2015 Quality Management"},"slides":{"slide1":"Our main production workshop","slide2":"High-precision optical equipment","slide3":"Product quality testing area","slide4":"R&D and design department"}},"contact":{"title":"Contact Us","form":{"name":"Your Name","email":"Your Email","message":"Your Message","submit":"Send Message","title":"Send us a message","subtitle":"We\'ll get back to you within 24 hours","name_placeholder":"Your Name","email_placeholder":"Your Email","phone":"Phone","phone_placeholder":"Your Phone Number","country":"Country","country_placeholder":"Your Country","playground_size":"Playground Size","size_option1":"100-500 sqm","size_option2":"500-1000 sqm","size_option3":"1000+ sqm","size_option4":"Not sure yet","message_placeholder":"Tell us about your project...","sending":"Sending..."},"address":"Address","phone":"Phone","email":"<EMAIL>","description":"Get in touch with us for your indoor playground needs","office":"Office","email_us":"Email Us","call_us":"Call Us","working_hours":"Working Hours","hours":"Monday - Friday: 9:00 AM - 6:00 PM"},"quote_form":{"title":"Request a Quote","name":"Your Name","phone":"Phone Number","message":"Your Requirements","submit":"Submit","submitting":"Submitting...","success_title":"Submission Successful!","success_message":"Thank you for your inquiry. Our team will contact you as soon as possible.","submit_again":"Submit Again","privacy_note":"By submitting, you agree to our Privacy Policy"},"services":{"title":"Our Services","description":"This is the services page.","quality_control":"Quality Control","safe_standard":"Safety Standards","marketing_support":"Marketing Support","learn_more":"Learn More","intro":{"title":"Comprehensive Support for Your Holographic Projection Business","paragraph1":"At Guangzhou Junsheng Technology Co., Ltd., we provide more than just holographic projection equipment. Our comprehensive services ensure your holographic projection business thrives from concept to ongoing operations.","paragraph2":"With our expert guidance and support in quality control, technical standards, and marketing, you can focus on creating memorable visual experiences for your customers while we handle the technical details."},"offerings":{"title":"Our Service Offerings","quality_control":{"description":"Our rigorous quality control process ensures that every component of your holographic projection system meets the highest standards for stability, durability, and performance.","feature1":"Equipment testing and certification","feature2":"Production oversight and inspection","feature3":"Pre-shipping quality checks","feature4":"Post-installation system verification"},"safe_standard":{"description":"System stability is our top priority. We adhere to international technical standards and provide guidance to ensure your holographic projection system maintains the highest level of stability for all users.","feature1":"Compliance with international technical standards","feature2":"Stability-focused design principles","feature3":"Regular system inspection protocols","feature4":"Technical documentation and certification"},"marketing_support":{"description":"Our marketing experts help you promote your holographic projection business effectively, attracting customers and maximizing your return on investment.","feature1":"Brand development assistance","feature2":"Digital marketing strategies","feature3":"Promotional materials and templates","feature4":"Project launch support"}}},"quality_control":{"description":"This is the quality control page.","intro":{"title":"Our Commitment to Quality","paragraph1":"At Guangzhou Junsheng Technology Co., Ltd., quality is at the heart of everything we do. We understand that playground equipment must be manufactured to the highest standards to ensure safety, durability, and enjoyment.","paragraph2":"Our comprehensive quality control system covers every stage of production, from raw material selection to final product inspection."},"process":{"title":"Our Quality Control Process","step1":{"title":"Raw Material Inspection","description":"Every material that enters our factory undergoes rigorous testing to ensure it meets our quality specifications. We test for composition, strength, and durability."},"step2":{"title":"Production Monitoring","description":"Our quality control team monitors the production process at every stage, conducting regular checks and ensuring compliance with design specifications."},"step3":{"title":"Pre-Shipment Inspection","description":"Before any product leaves our facility, it undergoes comprehensive testing and inspection to ensure it meets all safety and quality standards."},"step4":{"title":"Certification","description":"Our products are certified by international safety standards, including ASTM, EN, CE, TÜV, and more."}}},"safe_standard":{"description":"This is the safety standards page.","intro":{"image_alt":"Guangzhou Junsheng Technology Co., Ltd. Safety Standards","title":"Safety is Our Top Priority","paragraph1":"At Guangzhou Junsheng Technology Co., Ltd., we are committed to creating fun and engaging play environments while putting safety first. We understand the trust that parents, educators, and facility owners place in us when they choose our playground equipment.","paragraph2":"Our commitment to safety begins at the design stage and continues through manufacturing, installation, and beyond. Every component, material, and feature is carefully engineered to meet or exceed international safety standards."},"standards":{"title":"International Safety Standards","astm":"Standard Consumer Safety Performance Specification for Playground Equipment for Public Use (USA)","en":"European Standards for Playground Equipment and Impact Absorbing Playground Surfacing","iso":"International Quality Management System Standards","ce":"European Conformity for Health, Safety, and Environmental Protection Standards","tuv":"German Technical Inspection Association Safety and Quality Certification","csa":"Canadian Standards Association Guidelines for Children\'s Play Spaces and Equipment"},"features":{"title":"Safety Features in Our Equipment","image_alt":"Safety features of Guangzhou Junsheng Technology equipment","rounded_edges":{"title":"Rounded Edges","description":"All components feature rounded edges and corners to prevent injuries."},"non_toxic":{"title":"Non-Toxic Materials","description":"We use only non-toxic, lead-free materials and paints that are safe for children."},"anti_slip":{"title":"Anti-Slip Surfaces","description":"Platforms and steps feature anti-slip surfaces to prevent falls."},"impact_absorbing":{"title":"Impact Absorbing Flooring","description":"Our flooring systems are designed to cushion falls and reduce injury risk."},"secure":{"title":"Secure Connections","description":"All components are securely connected using tamper-proof hardware."},"spacing":{"title":"Proper Spacing","description":"Equipment is properly spaced to prevent overcrowding and collisions."},"enclosed":{"title":"Enclosed Climbing Areas","description":"Elevated platforms feature barriers to prevent falls."},"age_appropriate":{"title":"Age-Appropriate Design","description":"Equipment is designed with age-appropriate features and challenges."}},"cta":{"title":"Ready to Build a Safe Play Environment?","description":"Contact us today to learn how our safety-first approach can benefit your playground project.","button":"Contact Our Safety Experts"}},"marketing_support":{"description":"This is the marketing support page.","intro":{"image_alt":"Indoor Playground Marketing Support","title":"More Than Equipment: Your Success is Our Priority","paragraph1":"At Guangzhou Junsheng Technology Co., Ltd., we understand that creating a successful indoor playground business is about more than just installing quality equipment. Marketing and promotion are key factors in determining the success of your venture.","paragraph2":"That\'s why we offer comprehensive marketing support to help you attract customers, build your brand, and maximize your business potential. Our team of marketing experts will work with you from pre-opening through ongoing operations."},"services":{"title":"Our Marketing Support Services","brand":{"title":"Brand Development","item1":"Logo design assistance","item2":"Brand identity development","item3":"Color scheme and theme recommendations","item4":"Signage design support"},"digital":{"title":"Digital Marketing","item1":"Website design recommendations","item2":"Social media strategy guidance","item3":"Online advertising templates","item4":"SEO optimization tips"},"promotional":{"title":"Promotional Support","item1":"Grand opening event planning","item2":"Promotional material templates","item3":"Seasonal marketing campaigns","item4":"Partnership opportunity guidance"},"opening":{"title":"Pre-Opening Support","item1":"Marketing timeline development","item2":"Community outreach strategies","item3":"Media relations guidance","item4":"Soft opening recommendations"}},"process":{"title":"Marketing Support Process","step1":{"title":"Analysis","description":"We analyze your local market and competitive landscape"},"step2":{"title":"Strategy","description":"Develop a customized marketing strategy for your business"},"step3":{"title":"Implementation","description":"Provide tools and templates for marketing execution"},"step4":{"title":"Training","description":"Train your team on effective marketing techniques"},"step5":{"title":"Ongoing Support","description":"Continue to provide guidance as your business grows"}},"cta":{"title":"Ready to Market Your Playground Successfully?","description":"Contact us today to learn about our comprehensive marketing support services.","button":"Get Marketing Support"}},"footer":{"rights":"All Rights Reserved","privacy":"Privacy Policy","terms":"Terms of Service","services":"Services","solutions":"Solutions","products":"Products","blog":"Blog","blog_posts":"Blog Posts","news":"News","case_studies":"Case Studies","contact_us":"Contact Us","address":"No.3 Jiucun West Road, Dalong Street, Panyu District, Guangzhou, Building 2, Room 502, Junsheng Technology","company_description":"As an industry leader, Guangzhou Junsheng Technology Co., Ltd. specializes in 3D holographic projection, naked-eye 5D interactive projection, immersive restaurants, tech exhibition halls, and innovative visual solutions, providing one-stop services from design to installation, creating stunning visual experiences for clients in over 80 countries.","rights_reserved":"All Rights Reserved","holographic_purchase_guide":"Holographic Project Purchase Guide","custom_design_guide":"Project Customization Guide","quality_control":"Technical Quality Assurance","safe_standard":"System Stability Standards","marketing_support":"Marketing Support","holographic":"Holographic Projection Equipment","interactive":"Interactive Projection Systems","custom_solutions":"Custom Solutions"},"dropdown":{"custom_playground_design":"Custom Holographic Projection Design","purchase_guide":"Holographic Projection Purchase Guide","custom_design":"Custom Holographic Design","quality_control":"Technical Quality Assurance","safe_standard":"System Stability Standards","marketing_support":"Marketing Support","holographic":"Indoor Playground Equipment","interactive":"Trampoline Parks","custom_solutions":"Custom Solutions","interactive_projection_series":"Interactive Projection Series"},"advantages":{"title":"Guangzhou Junsheng Technology Advantages","badge":"Why Choose Us","title_prefix":"Our","title_main":"Core Advantages","subtitle":"Guangzhou Junsheng Technology Co., Ltd. focuses on providing high-quality holographic projection solutions to create value for customers","customization":{"title":"Custom Holographic Solutions","description":"Tailored holographic environments for your specific space and needs"},"support":{"title":"Expert Technical Team","description":"Specialists with 10+ years of holographic projection experience"},"service":{"title":"24-Hour Technical Support","description":"Round-the-clock professional customer service and technical assistance"},"quality":{"title":"Premium Equipment Quality","description":"Using world-class projection equipment and optical materials"},"invoice":{"title":"Global Project Experience","description":"Hundreds of successful cases completed in over 80 countries"},"value":{"title":"One-Stop Service","description":"Complete process from design and content creation to installation"}},"customPlayground":{"title":"Custom Indoor Playground Design","subtitle":"Opening your first playground? Let us provide you with a custom solution!","cta":"Get Your Custom Solution"},"custom_solutions":{"image_alt":"Custom playground solutions","intro":{"title":"Tailored Solutions for Your Vision","paragraph1":"Every space is unique, and so should be your playground.","paragraph2":"Our custom design service brings your specific vision to life."},"list":{"title":"What We Offer"},"design":{"title":"Holographic Project Customization","description":"Bring your creativity to life through our custom design services. Our talented designers will work closely with you to create unique holographic solutions that perfectly meet your needs.","feature1":"Customized design based on your requirements","feature2":"3D visualization of projects","feature3":"Themed design options","feature4":"Space optimization solutions","image_alt":"Custom design process"},"purchase":{"title":"Holographic Technology Purchase Guide","description":"New to holographic and immersive technology? Our comprehensive guide will walk you through all aspects of selecting and implementing the right holographic solutions for your business.","feature1":"Step-by-step implementation process","feature2":"Budget and financial planning","feature3":"Location and space planning advice","feature4":"Technology selection guidance","image_alt":"Purchasing process"},"process":{"title":"Design Process","step1":{"title":"Consultation","description":"Understanding your vision and requirements"},"step2":{"title":"Concept Design","description":"3D rendering and layout planning"},"step3":{"title":"Refinement","description":"Adjusting design based on feedback"},"step4":{"title":"Production","description":"Manufacturing with quality control"}},"cta":{"title":"Ready to Discuss Your Custom Holographic Solution?","description":"Contact our team today to explore how we can create the perfect holographic solution for your needs.","button":"Contact Us Now"}},"purchase_guide":{"description":"A comprehensive guide to purchasing your first indoor playground","intro":{"image_alt":"Indoor playground purchase guide","title":"Your Journey to Playground Ownership","paragraph1":"Starting an indoor playground business is an exciting venture.","paragraph2":"This guide will walk you through every step of the process."},"steps":{"title":"Step-by-Step Guide","pro_tip":"Pro Tip","step1":{"title":"Step 1: Research & Planning","item1":"Analyze local market demand","item2":"Study competitor offerings","item3":"Define target age groups","item4":"Set preliminary budget","item5":"Choose ideal location","tip":"Visit successful playgrounds in other cities for inspiration","image_alt":"Research and planning phase"},"step2":{"title":"Step 2: Space & Design","item1":"Measure available space accurately","item2":"Consider ceiling height requirements","item3":"Plan for parent seating areas","item4":"Include party room if needed","item5":"Design traffic flow patterns","tip":"Leave 30% open space for circulation and future additions","image_alt":"Space planning and design"},"step3":{"title":"Step 3: Budget & Financing","item1":"Equipment costs (40-50% of total)","item2":"Installation and shipping","item3":"Safety surfacing materials","item4":"Permits and insurance","item5":"Marketing and grand opening","tip":"Budget extra 15-20% for unexpected costs","image_alt":"Budget planning"},"step4":{"title":"Step 4: Installation & Launch","item1":"Coordinate delivery schedule","item2":"Supervise installation process","item3":"Complete safety inspections","item4":"Train staff thoroughly","item5":"Plan grand opening event","tip":"Do a soft opening with friends and family first","image_alt":"Installation and launch"}},"considerations":{"title":"Important Considerations","safety":{"title":"Safety First","description":"Never compromise on safety standards and certifications"},"revenue":{"title":"Revenue Planning","description":"Consider multiple income streams: admission, parties, cafe"},"staff":{"title":"Staffing Needs","description":"Plan for adequate supervision and customer service"},"marketing":{"title":"Marketing Strategy","description":"Build awareness before opening with social media and partnerships"}},"cta":{"title":"Ready to Start?","description":"Let our experts guide you through the process","button":"Get Expert Consultation"}},"holographic_guide":{"page_title":"Holographic Project Purchase Guide","badge":"Professional Guidance","main_title_part1":"Your Roadmap to Successful","main_title_part2":"Holographic Projects","description1":"We provide you with a professional guidance roadmap, from investment planning and site selection to equipment selection, with professional guidance at every step. Let your holographic projection project achieve success guarantee from planning to operation, ensuring maximum return on investment and long-term sustainable development.","description2":"Our professional team will accompany you and provide comprehensive support at every key stage from project planning to successful operation, ensuring that your investment achieves the best return while achieving business goals and creating social value.","card_title":"Holographic Project Guide","card_subtitle":"Professional holographic projection procurement guidance solution","process_badge":"Professional Process","steps_title":"Step-by-Step Purchase Guide","steps_description":"From market research to equipment installation, we will guide you through every important step to ensure your investment decisions are wise and effective.","expert_advice":"Expert Advice","stats":{"experience":"Years of Project Experience","cases":"Customer Cases","satisfaction":"Customer Satisfaction"},"steps":{"business":{"title":"Business Planning","subtitle":"Business Planning","point1":"Analyze your business model and determine profit model","point2":"Define target customer groups (age groups, demographics)","point3":"Develop marketing strategies and promotion plans","point4":"Establish operational management systems","expert":"Expert Advice: Based on rich operational experience, we will help you establish a sustainable business model."},"location":{"title":"Location Planning","subtitle":"Site Selection","point1":"Choose high-traffic premium locations","point2":"Ensure adequate parking and convenient facilities","point3":"Consider transportation convenience for easy access by parents and children","point4":"Check local regulations, fire safety, health requirements to ensure compliance","expert":"Expert Advice: Ideal site selection will directly impact the success of holographic projects. We help you find the best location."},"budgeting":{"title":"Budgeting","subtitle":"Budget Planning","point1":"Determine overall investment budget and phased investment","point2":"Equipment procurement budget (holographic equipment accounts for about 60%-70%)","point3":"Decoration, installation, transportation cost budget","point4":"Reserve funds for pre-opening operations and marketing promotion","expert":"Expert Advice: Reasonable budget allocation is key to project success. We help you optimize return on investment."},"equipment":{"title":"Equipment Selection","subtitle":"Equipment Selection","point1":"Select appropriate equipment based on venue size and customer needs","point2":"Choose quality equipment that meets international safety standards","point3":"Consider equipment entertainment value and educational value","point4":"Ensure equipment maintenance convenience and service life","expert":"Expert Advice: Quality equipment is the core of holographic projects. We provide the most suitable equipment solutions."}}}}');

/***/ }),

/***/ "(app-pages-browser)/./app/dictionaries/zh.json":
/*!**********************************!*\
  !*** ./app/dictionaries/zh.json ***!
  \**********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = JSON.parse('{"common":{"home":"首页","products":"产品","services":"服务","solutions":"解决方案","about":"关于我们","contact":"联系我们","blog":"博客","logo_alt":"广州钧盛科技有限公司","loading":"加载中...","product_not_found":"未找到产品","back_to_products":"返回产品列表","price":"价格","category":"分类","availability":"库存状态","in_stock":"有库存","out_of_stock":"缺货","id":"编号","key_features":"主要特点","feature1":"可定制设计","feature2":"优质材料","feature3":"安全合规","feature4":"专业安装","contact_now":"立即联系","product_details":"产品详情","specifications":"规格参数","reviews":"评价","related_products":"你可能还喜欢","view_details":"查看详情","no_products":"此分类下暂无产品","learn_more":"了解更多","contact_us":"联系我们","submit":"提交","whatsapp":"WhatsApp 联系","email":"发送邮件","top":"返回顶部","years_experience":"年行业经验"},"home":{"heroTitle":"定制室内游乐场解决方案","heroSubtitle":"室内游乐设备和蹦床公园领先制造商","ctaButton":"获取报价","featuredProducts":"精选产品","viewAll":"查看所有产品","slider":{"product":{"alt":"广州钧盛科技有限公司设备","title":"沉浸式用餐体验","subtitle":"高端全息投影餐厅解决方案，打造独特用餐体验"},"design":{"alt":"定制游乐场设计","title":"定制游乐场设计","subtitle":"创新舞台视觉方案，提升演出和活动震撼效果"},"indoor":{"alt":"室内游乐场解决方案","title":"AR互动体验","subtitle":"结合AR技术的智能互动解决方案，打造新一代体验空间"},"climbing":{"alt":"Motion Climbing","title":"智能攀岩体验","subtitle":"融合运动与科技的创新攀岩解决方案，激发无限挑战乐趣"},"beach":{"alt":"Interactive Beach","title":"互动海洋世界","subtitle":"沉浸式海洋主题互动体验，让孩子探索神奇的海底世界"}}},"about":{"subtitle":"关于我们","title":"关于广州钧盛科技有限公司","paragraph1":"广州钧盛科技有限公司是一家专注于互动多媒体、AR体感、全息投影数字运动等产品研发生产及应用的创意科技型企业。办公室面积1300平方米，展厅面积500平方米（全息宴会厅、裸眼空间、儿童游乐科普产品）。","paragraph2":"主营业务：互动投影产品、数字运动系列、AR体感、场景类游戏、全息KTV巨幕、全息婚宴以及其他定制类等产品。致力于互动多媒体创意设计与制作、软件技术研发、人机智能交互的研发、以视觉算法技术、运动识别技术等为核心。","paragraph3":"钧盛科技秉承约盛未来、创新不止的经营理念，为行业注入更多的新鲜元素。公司一直追求优秀的服务质量、完善的客户体验以及高水准的专业服务团队。","exploreButton":"了解更多关于我们","image_alt":"关于广州钧盛科技有限公司","company_name":"广州钧盛科技有限公司","company_profile":"公司简介","years":"年行业经验","quality":"品质保证","about_us":"关于我们","global_leading":"全球领先的","holographic_solution_provider":"全息解决方案提供商","junsheng_description":"广州钧盛科技有限公司成立于2008年，是一家专注于全息投影、互动多媒体技术与应用的高新技术企业。我们致力于为全球客户提供先进、创新的视觉体验解决方案。","company_rd_description":"公司拥有强大的研发团队和专业的制作团队，提供从创意策划、内容制作到系统集成的全方位服务，为客户打造独特的视觉体验和互动效果。","mission":{"title":"我们的使命","paragraph1":"在广州钧盛科技有限公司，我们致力于通过创新的游乐设备和互动体验，为儿童创造安全、有趣且具有教育意义的游乐空间。","paragraph2":"我们相信游戏的力量可以促进儿童的身体发展、社交能力和创造力，通过精心设计的游乐环境，我们为商业场所、家庭娱乐中心等领域带来无限可能。"},"stats":{"countries":"服务国家","registration":"在27个国家获得安全认证","partners":"拥有10多个全球战略合作伙伴","certificates":"超过100种游乐场解决方案获得专业认证","projects":"全球项目","experience":"年技术积累","safety":"安全认证","office_area":"办公面积","showroom_area":"展厅面积","service_quality":"优质服务","years":"年行业经验"},"team":{"title":"广州钧盛科技有限公司团队"},"certificates":{"title":"专业认证","description":"我们坚持创新与质量保障，确保每套游乐设备都经过严格测试，安全可靠。","alt":"认证"},"cta":{"title":"准备好打造您的梦想游乐场了吗？","description":"立即联系我们的团队，讨论您的定制游乐场需求。","button":"立即联系我们"},"tech":{"title":"核心技术","item1":{"title":"3D全息投影","description":"无需特殊眼镜即可呈现立体悬浮影像，为您创造真实的三维视觉体验"},"item2":{"title":"裸眼5D互动投影","description":"结合动作感应与全息成像，让观众无需任何设备即可与投影内容自然互动"},"item3":{"title":"沉浸式餐厅/宴会厅","description":"将餐饮空间转变为动态艺术画布，通过墙面和桌面投影创造独特用餐体验"},"item4":{"title":"科技展厅","description":"为企业和文化展览打造引人入胜的互动展示空间，提升品牌传播效果"}}},"productSections":{"title":"产品系列展示","description":"探索我们的全系列产品解决方案，涵盖多个技术领域","clickToView":"点击查看更多","section1":{"title":"互动投影系列","description":"创新的互动投影技术，打造沉浸式体验空间"},"section2":{"title":"全息展示系列","description":"先进的全息投影技术，呈现震撼视觉效果"},"section3":{"title":"数字沙盘系列","description":"智能数字沙盘，实现精准展示与互动控制"},"section4":{"title":"AR增强现实系列","description":"AR增强现实技术，融合虚拟与现实世界"},"section5":{"title":"智能一体机系列","description":"集成化智能设备，提供完整解决方案"}},"products":{"title":"我们的产品","filter":{"all":"所有产品"},"sort":"排序","moreDetails":"更多详情","description":"探索钧盛科技领先的全息投影与互动技术解决方案，包括3D裸眼全息、互动投影、沉浸式空间及数字多媒体系统。我们为展览馆、商业空间、宴会厅等场景提供定制化视觉科技体验。","loading_error":"加载产品时出错: ","retry":"重试","no_products":"暂无产品","no_search_results":"未找到匹配的产品","search_keyword":"搜索关键词: ","try_other_keywords":"请尝试其他关键词或浏览所有产品","total_products":"共 {{count}} 个产品，第 {{current}} 页，共 {{total}} 页","search_results":"搜索 \\"{{query}}\\" 找到 {{count}} 个产品，第 {{current}} 页，共 {{total}} 页","previous_page":"上一页","next_page":"下一页","no_description":"暂无描述","categories":{"holographic":"全息投影","interactive":"互动投影","restaurant":"沉浸式餐厅","exhibition":"科技展厅","outdoor":"户外互动系统"},"product_categories":{"运动娱乐":"运动娱乐","游戏娱乐":"游戏娱乐","教育科技":"教育科技","娱乐社交":"娱乐社交","商业设备":"商业设备","教育展示":"教育展示","商业娱乐":"商业娱乐","儿童娱乐":"儿童娱乐","全息科技":"全息科技"},"indoor":{"description":"探索我们为各年龄段儿童设计的创新室内游乐场解决方案，打造引人入胜的游戏体验。每个游乐场都可以根据您的空间和主题需求进行定制。","features":{"title":"室内游乐场特点","themes":{"title":"主题环境","description":"沉浸式主题游乐区，激发想象力并创造难忘体验。"},"multi":{"title":"多层游戏结构","description":"令人兴奋的垂直游戏空间，配有滑梯、攀爬设备和互动元素。"},"toddler":{"title":"幼儿区","description":"专为年幼儿童设计的安全、适龄游戏区。"},"interactive":{"title":"互动元素","description":"促进通过游戏学习和身体发展的引人入胜的活动。"}},"cta":{"title":"准备好创建您的室内游乐场了吗？","description":"立即联系我们的团队，讨论您的定制室内游乐场项目。","button":"立即开始"}},"trampoline":{"description":"探索我们为最大乐趣和安全设计的可定制蹦床公园解决方案。我们的蹦床公园为各年龄段的访客提供刺激的体验。","features":{"title":"蹦床公园特点","jump":{"title":"跳跃区","description":"多个跳跃区域，配备适合各种技能水平的专业级蹦床。"},"sports":{"title":"蹦床运动","description":"蹦床篮球、躲避球和其他刺激的体育活动。"},"foam":{"title":"泡沫坑区","description":"安全着陆区，填充泡沫块，用于练习翻转和技巧。"},"safety":{"title":"安全系统","description":"全面的安全功能，包括填充表面、围栏和专业级设备。"}},"cta":{"title":"准备好建造您的蹦床公园了吗？","description":"立即联系我们的团队，讨论您的定制蹦床公园项目。","button":"立即开始"}},"small":{"description":"我们的100-500平方米游乐场解决方案非常适合希望在有限空间内最大化游戏价值的小型场所。这些紧凑但功能丰富的设计为购物中心、家庭娱乐中心和零售场所提供了出色的投资回报。","benefits":{"title":"100-500平方米游乐场的优势","cost":{"title":"较低的初始投资","description":"较小的占地面积意味着降低启动成本，同时仍提供完整的游戏体验。"},"location":{"title":"更多位置选择","description":"适合各种商业空间，开辟更多潜在场地选择。"},"roi":{"title":"更快的投资回报","description":"较低的运营成本和管理费用通常会带来更快的投资回报。"},"efficient":{"title":"空间效率","description":"我们的设计通过巧妙的多层结构，最大化每平方米的游戏价值。"}},"cta":{"title":"您的小型空间的完美解决方案？","description":"立即联系我们的团队，讨论我们如何在您的100-500平方米空间内创建令人惊叹的游乐场。","button":"获取您的定制报价"}},"medium":{"description":"我们的500-1000平方米游乐场解决方案提供空间和功能的完美平衡，非常适合专门的家庭娱乐中心和较大的商业场所。这些中型游乐场提供多样化的游戏体验，同时保持运营效率。","benefits":{"title":"500-1000平方米游乐场的优势","capacity":{"title":"最佳容量","description":"同时容纳更多访客，同时保持舒适的游戏环境。"},"variety":{"title":"更丰富的游戏种类","description":"有空间容纳更多样化的游戏元素、主题区域和专业活动。"},"party":{"title":"增强的派对设施","description":"有空间设置多个派对区域，增加庆祝活动和活动的收入潜力。"},"balance":{"title":"平衡投资","description":"在投资规模和收入潜力之间取得完美平衡，实现可持续业务。"}},"cta":{"title":"您的娱乐中心的理想尺寸？","description":"立即联系我们的团队，探索我们根据您的需求定制的500-1000平方米游乐场解决方案。","button":"讨论您的项目"}},"large":{"description":"我们的1000+平方米游乐场解决方案代表了室内游戏体验的终极选择。这些大型娱乐目的地提供全面的游戏选择、多种景点，并有能力成为区域娱乐目的地。","benefits":{"title":"1000+平方米游乐场的优势","destination":{"title":"目的地吸引力","description":"成为吸引更广泛地理区域访客的区域娱乐目的地。"},"attractions":{"title":"多种景点","description":"有空间容纳多样化的游戏区、多种景点和互补的娱乐选择。"},"amenities":{"title":"扩展设施","description":"有空间设置全服务餐厅、咖啡厅、零售区和家长的大量座位。"},"revenue":{"title":"最大收入潜力","description":"更高的容量和更长的访问时间转化为更大的收入机会。"}},"cta":{"title":"计划一个大型娱乐目的地？","description":"立即联系我们的团队，讨论您对大型室内游乐场或娱乐中心的愿景。","button":"开始您的大型项目"}},"all_products_title":"全部产品","all_products_subtitle":"浏览我们的完整产品系列","search_title":"找到您需要的产品","search_subtitle":"搜索我们的产品库，发现最适合您需求的解决方案","search_placeholder":"搜索产品名称、型号或关键词...","popular_searches":"热门搜索","search_tags":"互动投影,数字沙盘,全息投影,AR体验,VR设备"},"features":{"title":"3D全息投影与互动技术特点","description":"探索广州钧盛科技有限公司领先的3D全息投影、裸眼5D互动投影、沉浸式餐厅和科技展厅解决方案。我们的创新视觉技术为商业空间、展览和活动带来震撼体验，已成功服务全球80多个国家的客户。","cta":"立即获取免费报价","learn_more":"了解更多","prev_slide":"上一张","next_slide":"下一张","badge":"创新科技","title_prefix":"先进","title_main":"全息技术","innovative":"创新技术","projection":{"title":"3D全息投影技术","subtitle":"震撼立体视觉体验","description":"采用先进的3D空间成像技术，打造超高清晰度立体全息影像，无需任何眼镜或设备即可观看全息内容，为观众带来身临其境的视觉震撼。","specs":{"spec1":"4K超高清投影","spec2":"270°观看角度","spec3":"自动亮度调节"}},"interactive":{"title":"裸眼5D互动投影","subtitle":"沉浸式人机互动体验","description":"集成先进的动作捕捉和手势识别技术，实现观众与投影内容的自然互动，创造真正的沉浸式5D体验，适用于展览、活动和商业空间。","specs":{"spec1":"红外线追踪系统","spec2":"60帧/秒反应速度","spec3":"多点触控支持"}},"materials":{"title":"户外地面互动投影","subtitle":"创新公共空间互动体验","description":"专为户外环境设计的高亮度地面互动投影系统，结合防水防尘技术和智能互动系统，为广场、公园和商业街区创造引人入胜的互动体验。","specs":{"spec1":"IP65防水防尘","spec2":"8000流明高亮度","spec3":"全天候运行设计"}},"software":{"title":"沉浸式餐厅解决方案","subtitle":"数字化餐饮体验革新","description":"为餐厅打造沉浸式用餐环境，通过墙面、桌面和空间投影技术，结合互动元素，创造独特的主题餐厅体验，提升顾客满意度和回头率。","specs":{"spec1":"多区域同步控制","spec2":"智能场景切换","spec3":"触控菜单交互"}},"application":{"title":"宴会厅多媒体系统","subtitle":"高端宴会空间视听解决方案","description":"为酒店宴会厅、会议中心提供一体化多媒体解决方案，包括全息投影、LED显示、灯光音响系统集成，打造震撼的婚礼、庆典和企业活动空间。","specs":{"spec1":"中控集成系统","spec2":"一键场景切换","spec3":"多通道音频系统"}},"support":{"title":"科技展厅设计施工","subtitle":"企业展示空间一站式服务","description":"提供从创意策划、空间设计到多媒体系统集成、内容制作和施工管理的全流程服务，为企业打造具有科技感和互动性的现代化展示空间。","specs":{"spec1":"3D空间设计","spec2":"定制内容创作","spec3":"24H技术支持"}}},"solutions":{"title":"游乐环境定制解决方案","cta":"获取定制服务方案","product1":"3D电子沙盘 | 智能互动沙盘系统","product2":"AR体感蹦床 | 增强现实运动体验","product3":"AR教育系统 | 沉浸式学习体验","product4":"全息屏幕系统 | 裸眼3D显示技术","product5":"儿童互动沙滩 | 安全趣味游戏区","product6":"全息舞台系统 | 震撼视觉表演","product7":"保龄球互动系统 | 科技运动娱乐","product8":"宴会厅系统 | 高端商务展示","badge1":"3d电子沙盘","badge2":"AR体感蹦床","badge3":"AR教育","badge4":"ktv","badge5":"一体机","badge6":"互动砸球","badge7":"互动足球","badge8":"体感攀岩"},"serviceFeatures":{"title":"售后服务体系","description":"专业-品质-服务，为您提供全方位的售后保障体系","guidance":{"title":"全程指导","description":"客户只需提供场地面积等基本信息，我们提供上门服务，包括配送、安装、软硬件配置、现场布局"},"delivery":{"title":"发货流程","description":"电子设备发货前必须测试48小时以上，防止产品质量问题"},"afterSales":{"title":"售后流程","description":"工程技术1对1 24小时服务，建立售后微信群，第一时间提供远程协助和解决方案"},"quality":{"title":"专业-品质-服务","description":"免费产品规划CAD图纸，多场景、多难度等级，内容持续丰富更新增强趣味性，常规产品每年免费更新"}},"customPlayground":{"title":"定制全息投影设计","subtitle":"正在规划您的第一个全息投影项目？让我们为您提供定制解决方案！","cta":"获取您的定制方案"},"cases":{"title_prefix":"经典","title_main":"案例","description":"我们为全球客户提供创新的全息投影解决方案，满足各行业的独特需求","carousel":{"slide1":{"title":"全息投影展览案例","description":"沉浸式全息投影技术展示"},"slide2":{"title":"互动投影体验","description":"创新互动投影解决方案"},"slide3":{"title":"数字餐厅案例","description":"全息投影餐厅体验"},"slide4":{"title":"企业展厅案例","description":"企业级全息展示方案"},"slide5":{"title":"酒店空间案例","description":"酒店全息投影装饰"},"slide6":{"title":"零售空间案例","description":"零售环境全息应用"},"slide7":{"title":"文化展示案例","description":"文化场馆全息投影"},"slide8":{"title":"科技馆案例","description":"科技展示全息方案"},"slide9":{"title":"教育培训案例","description":"教育行业全息应用方案"},"slide10":{"title":"医疗健康案例","description":"医疗领域全息投影技术"},"slide11":{"title":"娱乐休闲案例","description":"娱乐场所全息体验设计"},"slide12":{"title":"商业展示案例","description":"商业空间全息展示方案"},"slide13":{"title":"文旅景区案例","description":"旅游景区全息投影应用"},"slide14":{"title":"智慧城市案例","description":"城市规划全息展示系统"},"slide15":{"title":"工业制造案例","description":"工业领域全息技术应用"},"slide16":{"title":"艺术创作案例","description":"艺术展览全息投影创新"}},"filter":{"all":"全部","exhibition":"展览","interactive":"互动投影","restaurant":"餐厅","corporate":"企业","hotel":"酒店","retail":"零售"},"case1":{"title":"黄山真趣小镇光影乐园","location":"中国，安徽"},"case2":{"title":"江上园光影艺术展","location":"中国，福州"},"case3":{"title":"惠州华贸天地\\"格光之城\\"","location":"中国，惠州"},"case4":{"title":"鸡西革命烈士纪念馆","location":"中国，鸡西"},"case5":{"title":"嬉水米花堂儿童光影农艺主题乐园","location":"中国，嬉水"},"case6":{"title":"黑龙江省科技馆","location":"中国，哈尔滨"},"view_case_btn":"查看案例","view_details_btn":"查看详情","view_all_btn":"查看全部案例"},"globalCases":{"title":"全球案例展示：卓越全息投影技术","solutions":"全息投影与沉浸式体验解决方案","countries":{"usa":"美国","canada":"加拿大","saudiArabia":"沙特阿拉伯","russia":"俄罗斯","india":"印度","indonesia":"印度尼西亚"},"showcase":{"USA":"美国案例","Canada":"加拿大案例","Saudi_Arabia":"沙特阿拉伯案例","Russia":"俄罗斯案例","India":"印度案例","Indonesia":"印度尼西亚案例"}},"factory":{"title_prefix":"欢迎参观","title":"广州钧盛科技生产基地","description":"参观我们的先进生产设施，了解我们如何以精湛工艺和创新技术打造高品质全息投影解决方案。","slider_label":"生产基地实景","info_btn":"查看品质与认证信息","info_btn_aria":"显示工厂信息","prev_slide_aria":"上一张","next_slide_aria":"下一张","slide_dot_aria":"到第{{index}}张幻灯片","info_card":{"header":"品质保证与制造实力","description":"我们的生产基地配备了行业领先的全息投影设备和精密光学仪器，严格按照ISO9001质量管理体系运作，确保每一件产品都符合国际标准。","cert_title":"国际质量认证"},"stats":{"years":"年行业经验","products":"产品已交付"},"certifications":{"ce":"欧洲安全标准","iso":"质量管理体系"},"slides":{"slide1":"我们的主要生产车间","slide2":"高精度光学设备","slide3":"产品质量检测区域","slide4":"研发与设计部门"}},"contact":{"title":"联系我们","description":"对我们的产品或服务有疑问？我们随时为您提供帮助！填写下面的表单，我们的团队将在24小时内与您联系。","office":"我们的办公室","address":"广州市番禺区大龙街旧村西路3号2栋502钧盛科技","email_us":"给我们发邮件","email":"<EMAIL>","call_us":"给我们打电话","phone":"+86 15989399197","working_hours":"工作时间","hours":"周一至周五：上午9:00 - 下午6:00","form":{"title":"获取您的免费报价","subtitle":"填写下面的表单，我们的团队将在24小时内与您联系。","name":"您的姓名","name_placeholder":"您的姓名","email":"您的电子邮箱","email_placeholder":"您的邮箱","phone":"电话","phone_placeholder":"您的电话","country":"国家 *","country_placeholder":"您的国家","playground_size":"投影区域大小","size_option1":"100-500平方米","size_option2":"500-1000平方米","size_option3":"1000+平方米","size_option4":"自定义大小","message":"您的留言","message_placeholder":"请描述您的项目或咨询","sending":"发送中...","submit":"发送信息"}},"services":{"title":"我们的服务","description":"这是服务页面。","quality_control":"质量控制","safe_standard":"安全标准","marketing_support":"营销支持","learn_more":"了解更多","intro":{"title":"为您的全息投影项目提供全面支持","paragraph1":"在广州钧盛科技有限公司，我们提供的不仅仅是全息投影设备。我们的全面服务确保您的全息投影项目从概念到持续运营都能蓬勃发展。","paragraph2":"通过我们在技术支持、系统稳定性和营销方面的专业指导和支持，您可以专注于为客户创造震撼的视觉体验，而我们则处理技术细节。"},"offerings":{"title":"我们的服务内容","quality_control":{"description":"我们严格的质量控制流程确保您的全息投影系统的每个组件都符合稳定性、耐用性和性能的最高标准。","feature1":"设备测试和认证","feature2":"生产监督和检查","feature3":"发货前质量检查","feature4":"安装后系统验证"},"safe_standard":{"description":"系统稳定性是我们的首要任务。我们遵循国际技术标准，并提供指导，确保您的全息投影系统为所有用户维持最高水平的稳定性。","feature1":"符合国际技术标准","feature2":"以稳定性为中心的设计原则","feature3":"定期系统检查协议","feature4":"技术文档和认证"},"marketing_support":{"description":"我们的营销专家帮助您有效地推广全息投影项目，吸引客户并最大化您的投资回报。","feature1":"品牌开发协助","feature2":"数字营销策略","feature3":"宣传材料和模板","feature4":"项目启动支持"}}},"quality_control":{"description":"这是质量控制页面。","intro":{"title":"我们对质量的承诺","paragraph1":"在广州钧盛科技有限公司，质量是我们所做一切的核心。我们理解，游乐设备必须按照最高标准制造，以确保安全性、耐用性和乐趣。","paragraph2":"我们全面的质量控制系统涵盖了从原材料选择到最终产品检验的生产的每个阶段。"},"process":{"title":"我们的质量控制流程","step1":{"title":"原材料检验","description":"进入我们工厂的每种材料都要经过严格测试，以确保符合我们的质量规格。我们测试其成分、强度和耐用性。"},"step2":{"title":"生产监控","description":"我们的质量控制团队监控每个阶段的生产过程，进行定期检查并确保符合设计规格。"},"step3":{"title":"发货前检验","description":"在任何产品离开我们的设施之前，它都要经过全面测试和检验，以确保符合所有安全和质量标准。"},"step4":{"title":"认证","description":"我们的产品通过国际安全标准认证，包括ASTM、EN、CE、TÜV等。"}}},"safe_standard":{"description":"这是安全标准页面。","intro":{"image_alt":"广州钧盛科技有限公司的安全标准","title":"安全是我们的首要任务","paragraph1":"在广州钧盛科技有限公司，我们致力于创造有趣且吸引人的游戏环境，同时将安全放在首位。我们理解家长、教育工作者和设施所有者在选择我们的游乐设备时对我们的信任。","paragraph2":"我们对安全的承诺从设计阶段开始，贯穿制造、安装及以后的过程。每个组件、材料和功能都经过精心设计，以满足或超过国际安全标准。"},"standards":{"title":"国际安全标准","astm":"公共使用游乐设备的标准消费者安全性能规范（美国）","en":"欧洲游乐设备和冲击吸收游乐场地面标准","iso":"国际质量管理体系标准","ce":"欧洲健康、安全和环境保护标准合格标志","tuv":"德国技术检验协会安全和质量认证","csa":"加拿大标准协会儿童游乐空间和设备指南"},"features":{"title":"我们设备的安全特性","image_alt":"广州钧盛科技有限公司设备的安全特性","rounded_edges":{"title":"圆角边缘：","description":"所有组件都有圆角边缘和角落，以防止受伤。"},"non_toxic":{"title":"无毒材料：","description":"我们只使用对儿童安全的无毒、无铅材料和涂料。"},"anti_slip":{"title":"防滑表面：","description":"平台和台阶采用防滑表面，防止跌倒。"},"impact_absorbing":{"title":"冲击吸收地板：","description":"我们的地板系统设计用于缓冲跌落并降低受伤风险。"},"secure":{"title":"安全连接：","description":"所有部件都使用防篡改硬件牢固连接。"},"spacing":{"title":"适当间距：","description":"设备间距合理，防止拥挤和碰撞。"},"enclosed":{"title":"封闭攀爬区域：","description":"高平台设有障碍物，防止跌落。"},"age_appropriate":{"title":"适龄设计：","description":"设备设计具有适合不同年龄段的特点和挑战。"}},"cta":{"title":"准备好建造安全的游戏环境了吗？","description":"立即联系我们，了解我们以安全为先的方法如何有利于您的游乐场项目。","button":"联系我们的安全专家"}},"marketing_support":{"description":"这是营销支持页面。","intro":{"image_alt":"室内游乐场的营销支持","title":"不仅仅是设备：您的成功是我们的首要任务","paragraph1":"在广州钧盛科技有限公司，我们理解创建成功的室内游乐场业务不仅仅是安装优质设备。营销和推广是决定您的企业成功的关键因素。","paragraph2":"这就是为什么我们提供全面的营销支持，帮助您吸引客户、建立品牌并最大化您的业务潜力。我们的营销专家团队将从开业前到持续运营全程与您合作。"},"services":{"title":"我们的营销支持服务","brand":{"title":"品牌开发","item1":"标志设计协助","item2":"品牌形象开发","item3":"配色方案和主题推荐","item4":"标识设计支持"},"digital":{"title":"数字营销","item1":"网站设计建议","item2":"社交媒体策略指导","item3":"搜索引擎优化(SEO)技巧","item4":"在线声誉管理建议"},"promotional":{"title":"宣传材料","item1":"可定制的传单和宣传册模板","item2":"数字广告模板","item3":"电子邮件营销模板","item4":"宣传视频协助"},"opening":{"title":"盛大开业支持","item1":"盛大开业规划指导","item2":"媒体关系协助","item3":"社区外展策略","item4":"活动营销技巧"}},"process":{"title":"我们的营销支持流程","step1":{"title":"初步咨询","description":"我们从详细咨询开始，了解您的业务目标、目标市场和独特卖点。"},"step2":{"title":"策略制定","description":"我们的营销团队制定根据您的特定需求和市场量身定制的营销策略。"},"step3":{"title":"开业前营销","description":"我们提供支持，在您的游乐场开业前建立期待并引起轰动。"},"step4":{"title":"盛大开业","description":"我们的团队帮助策划和执行成功的盛大开业活动，以最大化初始影响。"},"step5":{"title":"持续支持","description":"我们继续提供营销材料、季节性促销创意和业务增长策略。"}},"cta":{"title":"准备好最大化您的业务潜力了吗？","description":"立即联系我们，了解更多关于我们全面的营销支持服务。","button":"获取营销支持"}},"custom_solutions":{"image_alt":"定制游乐场解决方案","intro":{"title":"为您的独特需求量身定制的游乐场解决方案","paragraph1":"在广州钧盛科技有限公司，我们理解每个游乐场项目都是独一无二的。因此，我们提供根据您的特定要求、空间限制和预算量身定制的解决方案。","paragraph2":"无论您是寻找完全定制设计的游乐场，还是需要关于购买您的第一个室内游乐场的指导，我们的专家团队都将全程为您提供帮助。"},"list":{"title":"我们的定制解决方案"},"design":{"title":"全息项目定制","description":"通过我们的定制设计服务将您的创意变为现实。我们才华横溢的设计师将与您紧密合作，创建完美满足您需求的独特全息解决方案。","feature1":"基于您需求的定制化设计","feature2":"项目的3D可视化呈现","feature3":"主题化设计选项","feature4":"空间优化解决方案","image_alt":"定制游乐场设计"},"purchase":{"title":"全息技术购买指南","description":"刚接触全息和沉浸式技术？我们的综合指南将引导您了解为企业选择和实施正确全息解决方案的各个方面。","feature1":"逐步实施流程","feature2":"预算和财务规划","feature3":"位置和空间规划建议","feature4":"技术选择指导","image_alt":"如何购买您的第一个室内游乐场"},"process":{"title":"我们的定制设计流程","step1":{"title":"初步咨询","description":"我们首先了解您的愿景、需求、空间限制和预算。"},"step2":{"title":"概念开发","description":"我们的设计团队根据您的输入和要求创建初步概念。"},"step3":{"title":"设计完善","description":"我们根据您的反馈和偏好完善所选概念。"},"step4":{"title":"最终设计与实施","description":"一旦获得批准，我们将最终确定设计并开始制造过程。"}},"cta":{"title":"准备好讨论您的全息定制解决方案？","description":"今天就联系我们的团队，探索我们如何为您的需求创造完美的全息解决方案。","button":"立即联系我们"}},"custom_playground":{"title":"定制游乐场设计","description":"为您的品牌、场所或活动量身定制创新的全息投影解决方案，从创意策划到实施交付，提供一站式服务。我们专注于打造独特的视觉体验，为您的项目增添科技感与未来感。","badge":"专属定制","title_prefix":"专属","title_main":"全息解决方案","cta_button":"获取定制方案","contact_button":"联系我们","image_alt":"定制全息解决方案","page_title":"项目定制指南","breadcrumb_home":"首页","breadcrumb_solutions":"定制解决方案","breadcrumb_current":"项目定制指南","design_process_title":"我们的设计流程","design_process_subtitle":"打造卓越全息体验的五步专业流程，从创意构思到最终实施的全程定制服务","loading":"加载中...","learn_more":"了解详情","view_details":"查看详情","related_products":"相关产品","product_details":"产品详情","view_more_products":"查看更多产品","explore_complete_series":"探索我们的完整产品系列","browse_all":"浏览全部","back_to_products":"返回产品中心","product_gallery":"产品展示","application_scenario":"应用场景","professional":"专业级","reviews":"评价","get_quote":"获取报价","in_stock":"现货供应","professional_installation":"专业安装","three_year_warranty":"质保3年","watch_demo":"观看演示","overlay":{"badge":"高端定制","title":"专业全息解决方案","description":"高端定制 · 视觉震撼 · 互动体验"},"experience_years":"年行业经验","services":{"creative":{"title":"创意策划","description":"专业创意团队根据品牌特性定制全息内容与视觉效果"},"spatial":{"title":"空间设计","description":"根据场地环境和用途优化投影布局与互动体验设计"},"content":{"title":"3D内容制作","description":"专业3D建模团队打造高品质全息投影内容与视觉效果"},"installation":{"title":"专业安装","description":"经验丰富的工程团队确保设备精准安装和系统调试"}},"design_steps":{"step1":{"title":"创意构思","description":"深入了解您的需求，制定创新的全息投影概念方案，确保每个细节都符合您的期望。从视觉设计到技术架构，每一个环节都经过精心规划和反复优化。","features":["需求分析","创意策划","概念设计","方案确认"]},"step2":{"title":"方案设计","description":"专业团队进行详细设计，确保视觉效果与技术可行性完美结合，打造独一无二的体验。从视觉设计到技术架构，每一个环节都经过精心规划和反复优化。","features":["视觉设计","技术架构","交互设计","效果预览"]},"step3":{"title":"技术开发","description":"运用先进技术进行开发制作，保证全息效果的精准呈现，每一帧都追求完美。采用最新的全息投影技术和专业设备，确保最佳的视觉效果。","features":["内容制作","系统开发","设备调试","效果优化"]},"step4":{"title":"测试优化","description":"严格测试每个细节，持续优化直至达到最佳展示效果，确保万无一失。通过多轮测试和调优，保证项目的稳定性和完美呈现。","features":["功能测试","性能优化","兼容性测试","用户体验优化"]},"step5":{"title":"部署实施","description":"专业团队现场安装调试，确保项目顺利交付并提供全方位后续技术支持。从设备安装到系统调试，全程专业服务，确保完美交付。","features":["现场安装","系统调试","培训交付","售后支持"]}},"projects":{"title":"我们的定制项目案例","subtitle":"查看我们为不同客户打造的成功全息投影项目案例","project1":{"title":"全息主题餐厅","subtitle":"1,200㎡海底主题沉浸式用餐体验","description":"大型沉浸式全息展示空间，占地1200平米，日均接待客户超过500人次。","tags":["大型商业","沉浸式体验","主题餐厅"],"stats":["1200㎡","500+/日","行业标杆"]},"project2":{"title":"企业全息展厅","subtitle":"800㎡企业形象展示空间","description":"企业全息展厅全方位展示企业实力和产品优势，提升品牌影响力。","tags":["企业展厅","品牌展示","科技创新"],"stats":["800㎡","品牌提升","科技创新"]},"project3":{"title":"博物馆全息展区","subtitle":"2,000㎡历史文化主题展示","description":"文物全息复原展示系统，让珍贵文物以全新形式与观众见面。","tags":["文化遗产","数字化保护","博物馆项目"],"stats":["2000㎡","文化部认可","数字重生"]}},"features":{"title":"全息定制特色","subtitle":"我们的全息定制解决方案具备多项优势，确保为您打造专属的视觉震撼体验","feature1":{"title":"8K超高清画质","description":"采用最新8K超高清技术，呈现前所未有的细腻逼真全息影像"},"feature2":{"title":"尖端全息技术","description":"采用最先进的裸眼3D/5D全息投影技术，无需任何设备即可观看"},"feature3":{"title":"智能互动系统","description":"结合动作感应与AI技术，让观众自然地与投影内容互动"},"feature4":{"title":"工业级稳定性","description":"军工级硬件配置，24小时不间断稳定运行"},"feature5":{"title":"专属定制方案","description":"根据您的独特需求量身定制专属解决方案"},"feature6":{"title":"全程技术支持","description":"提供7*24小时专业技术团队支持"}},"subtitle":"将您的独特愿景变为现实，我们提供定制设计服务","cta":"开始定制设计"},"purchase_guide":{"description":"这是关于如何购买您的第一个室内游乐场的指南。","intro":{"image_alt":"室内游乐场购买指南","title":"您成功室内游乐场的路线图","paragraph1":"购买您的第一个室内游乐场是一项令人兴奋的冒险，但也需要仔细规划和考虑。这份全面指南将引导您完成从初始概念到盛大开业的整个过程，确保您的室内游乐场业务成功起步。","paragraph2":"在广州钧盛科技有限公司，我们已经帮助数百位客户建立了成功的室内游乐场业务，我们分享我们的专业知识，使您的旅程更加顺畅。"},"steps":{"title":"逐步购买指南","pro_tip":"专业提示：","step1":{"title":"市场研究与业务规划","item1":"分析您的本地市场，确定需求和竞争","item2":"定义您的目标受众（年龄段、人口统计）","item3":"制定包括财务预测的业务计划","item4":"确定您的独特卖点","item5":"研究当地法规和许可要求","tip":"专注于创造独特体验，使您的游乐场与当地现有选择区分开来。","image_alt":"业务规划"},"step2":{"title":"位置与空间规划","item1":"选择具有良好可见度和可达性的战略位置","item2":"确保有足够空间容纳游乐设备、座位、派对房间等","item3":"考虑天花板高度要求（大多数结构至少需要4-5米）","item4":"规划支持区域：洗手间、厨房/咖啡厅、储藏室、办公室","item5":"验证区域限制和建筑规范","tip":"理想位置应平衡经济实惠的租金与高人流量和家庭可达性。","image_alt":"位置规划"},"step3":{"title":"预算与融资","item1":"建立涵盖业务各方面的全面预算","item2":"规划游乐设备（通常占总投资的30-40%）","item3":"包括装修、地板、暖通空调、电气等成本","item4":"为开业前费用、营销、员工培训编制预算","item5":"探索融资选项：贷款、投资者、设备融资","tip":"始终包括至少15-20%的应急资金，用于意外支出。","image_alt":"预算与融资"},"step4":{"title":"设备选择与设计","item1":"与广州钧盛科技有限公司等信誉良好的供应商合作","item2":"选择适合您目标年龄段的设备","item3":"考虑空间效率和人流","item4":"选择吸引您市场的主题","item5":"确保所有设备符合安全标准","tip":"从一开始就投资优质设备 – 它减少维护成本并为客户提供更好的体验。","image_alt":"设备选择"}},"considerations":{"title":"成功的关键考虑因素","safety":{"title":"安全第一","description":"在游乐场业务的各个方面优先考虑安全，从设备选择到日常运营。这是不可协商的。"},"revenue":{"title":"收入来源","description":"开发入场费以外的多种收入来源：生日派对、餐饮服务、商品销售、会员计划等。"},"staff":{"title":"员工质量","description":"雇佣喜欢与儿童一起工作的员工，并提供运营和客户服务方面的全面培训。"},"marketing":{"title":"营销一致性","description":"实施一致的营销努力，特别关注父母寻找活动的数字渠道。"}},"cta":{"title":"准备好开始您的室内游乐场之旅了吗？","description":"联系我们的团队进行免费咨询，了解我们如何帮助您创建成功的室内游乐场业务。","button":"安排咨询"}},"footer":{"rights":"版权所有","privacy":"隐私政策","terms":"服务条款","services":"服务","solutions":"解决方案","blog":"博客","blog_posts":"博客文章","news":"新闻","case_studies":"案例研究","contact_us":"联系我们","address":"广州市海珠区琶洲大道东8号","company_description":"作为行业领导者，广州钧盛科技有限公司专注于3D全息投影、裸眼5D互动投影和沉浸式展示的全方位解决方案，提供从设计到安装的一站式服务，为全球80多个国家的客户创造震撼视觉体验。","rights_reserved":"版权所有","products":"产品","holographic_purchase_guide":"全息项目购买指南","custom_design_guide":"项目定制指南","quality_control":"技术品质保障","safe_standard":"系统检定标准","marketing_support":"营销支持","holographic":"全息投影设备","interactive":"交互投影系统","custom_solutions":"定制解决方案"},"dropdown":{"custom_playground_design":"项目定制指南","purchase_guide":"全息项目购买指南","custom_design":"定制投影设计","quality_control":"技术品质保障","safe_standard":"系统稳定标准","marketing_support":"营销支持","holographic":"全息投影设备","interactive":"交互投影系统","custom_solutions":"定制解决方案","interactive_projection_series":"互动投影系列"},"advantages":{"title":"广州钧盛科技有限公司的优势","badge":"为什么选择我们","title_prefix":"我们的","title_main":"核心优势","subtitle":"广州钧盛科技有限公司专注于提供高品质的全息投影解决方案，为客户创造价值","customization":{"title":"定制全息解决方案","description":"根据您的空间和需求定制专属全息投影环境"},"support":{"title":"专业技术团队","description":"拥有10年+全息投影技术经验的专家团队"},"service":{"title":"24小时技术支持","description":"全天候专业客户服务和技术支持"},"quality":{"title":"高端设备品质","description":"采用国际一流投影设备和光学材料"},"invoice":{"title":"全球项目经验","description":"已在80多个国家完成数百个成功案例"},"value":{"title":"一站式服务","description":"从设计、内容制作到安装调试的全流程服务"}},"quote_form":{"title":"咨询领取报价","name":"您的姓名","phone":"联系电话","message":"需求备注","submit":"提交","submitting":"提交中...","success_title":"提交成功！","success_message":"感谢您的咨询，我们的团队将尽快与您联系。","submit_again":"再次提交","privacy_note":"提交即视为同意我们的隐私政策"},"holographic_project":{"intro":{"title":"将您的创意转化为震撼视觉体验","paragraph1":"在广州骏升，我们专注于创建定制化的全息投影项目，完美契合您的创意、空间和需求。我们经验丰富的设计团队将与您紧密合作，开发创新、引人入胜的全息视觉环境，使您的项目与众不同。","paragraph2":"无论您是为商业空间规划全息展示、为家庭娱乐中心设计沉浸式体验，还是为特殊场所打造专业全息效果，我们的定制设计服务都能确保您的项目取得成功。","image_alt":"全息项目定制"},"process":{"title":"我们的设计流程","step1":{"title":"初步咨询","description":"我们从详细的讨论开始，了解您的创意、需求、空间限制、目标受众和预算。这有助于我们为您的全息项目定制打下基础。","image_alt":"初步咨询"},"step2":{"title":"概念开发","description":"我们的设计师根据您的输入创建初步概念，融入创新的全息元素、最佳视觉效果和交互体验。我们会提供多个概念供您反馈。","image_alt":"概念开发"},"step3":{"title":"3D可视化","description":"一旦您选定了偏好的概念，我们会创建详细的3D渲染图，让您的全息项目栩栩如生。这些可视化帮助您准确了解项目的外观和功能。","image_alt":"3D可视化"},"step4":{"title":"详细设计与工程","description":"我们的工程团队制定详细的技术规格，确保所有设计满足技术标准和结构要求，同时保持创意愿景。","image_alt":"详细设计与工程"},"step5":{"title":"生产与安装","description":"设计获批后，我们按照精确规格制造所有组件。我们的专业安装团队将您的全息项目变为现实，确保正确组装和技术安全。","image_alt":"生产与安装"}},"features":{"title":"全息定制特色","themed":{"title":"沉浸式体验","description":"创造从科幻世界到历史场景、从自然风光到抽象艺术的各种沉浸式全息体验。"},"space":{"title":"空间优化","description":"通过巧妙的设计解决方案，有效利用可用空间，在任何大小的环境中实现最佳全息效果。"},"age":{"title":"多场景应用","description":"为不同场景设计专属全息方案，包括商业展示、教育培训、娱乐体验和艺术装置等多种应用场景。"},"custom":{"title":"定制化解决方案","description":"匹配您的品牌标识，集成创新技术，打造真正独特的全息投影体验。"}},"cta":{"title":"准备创建您的定制全息项目？","description":"今天就联系我们的设计团队，开启打造独特全息体验的旅程。","button":"开始您的设计之旅"}},"holographic_guide":{"page_title":"全息投影项目购买指南","badge":"专业指导","main_title_part1":"您成功全息投影项目的","main_title_part2":"路线图","description1":"我们为您提供一个专业的指导路线图，让您从投资规划、选址定位到设备选择，每个环节都能得到专业指导。让您的全息投影项目从规划到运营都能获得成功保障，确保投资回报最大化和长期可持续发展。","description2":"我们的专业团队将陪伴您，从项目规划到成功运营的每一个关键环节提供全方位支持，确保您的投资获得最佳回报，实现商业目标的同时创造社会价值。","card_title":"全息项目指南","card_subtitle":"专业的全息投影采购指导方案","process_badge":"专业流程","steps_title":"逐步购买指南","steps_description":"从市场调研到设备安装，我们将指导您完成每个重要步骤，确保您的投资决策明智且有效。","expert_advice":"专业建议","stats":{"experience":"年项目经验","cases":"客户案例","satisfaction":"客户满意度"},"steps":{"business":{"title":"商业规划","subtitle":"商业规划","point1":"分析您的业务模式，确定盈利模式","point2":"定义目标客户群体（年龄段、人口统计）","point3":"制定营销策略和推广计划","point4":"建立运营管理体系","expert":"专业建议：基于丰富的运营经验，我们将帮助您建立可持续发展的商业模式。"},"location":{"title":"选址规划","subtitle":"选址规划","point1":"选择具有高客流量的优质地段位置","point2":"确保有足够的停车位和便利设施","point3":"考虑交通便利性，让家长和孩子容易到达","point4":"检查当地法规、消防、卫生等要求，确保合规","expert":"专业建议：理想的选址将直接影响全息项目的成功，我们帮助您找到最佳位置。"},"budgeting":{"title":"预算规划","subtitle":"预算规划","point1":"确定总体投资预算和分阶段投入","point2":"设备采购预算（全息设备约占60%-70%）","point3":"装修装饰、安装、运输等费用预算","point4":"为开业前期运营、市场推广预留资金","expert":"专业建议：合理的预算分配是项目成功的关键，我们帮您优化投资回报。"},"equipment":{"title":"设备选择","subtitle":"设备选择","point1":"根据场地面积和客户需求选择合适的设备","point2":"选择符合国际安全标准的优质设备","point3":"考虑设备的趣味性和教育价值","point4":"确保设备的维护便利性和使用寿命","expert":"专业建议：优质的设备是全息项目的核心，我们提供最适合的设备解决方案。"}}}}');

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CAIGC-dm%5CCross-border%20E-commerce%20Website%20Project%5Cnextjs%5Capp%5C%5Blang%5D%5Cpages%5Ccontact-us%5Cpage.tsx&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);