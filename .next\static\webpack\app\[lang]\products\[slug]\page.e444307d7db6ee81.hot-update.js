"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lang]/products/[slug]/page",{

/***/ "(app-pages-browser)/./app/[lang]/products/[slug]/page.tsx":
/*!*********************************************!*\
  !*** ./app/[lang]/products/[slug]/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProductPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _utils_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils/i18n */ \"(app-pages-browser)/./app/utils/i18n.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Cpu,Monitor,Play,Star,Users,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// 移除复杂的导入\n// 产品骨架屏组件\nfunction ProductSkeleton() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-16 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-4 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-20 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-4 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-4 w-24 bg-gray-200 rounded\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 md:px-8 lg:px-12 py-8 md:py-12 lg:py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-start\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-200 rounded-2xl product-image-1920x1080\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-3\",\n                                            children: [\n                                                ...Array(4)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-200 rounded-xl aspect-video\"\n                                                }, i, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 35,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8 lg:pl-8 animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-12 w-4/5 bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 44,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-8 w-24 bg-gray-200 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 w-full bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 w-full bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 w-3/4 bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-6 w-20 bg-gray-200 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 sm:grid-cols-2 gap-3\",\n                                                    children: [\n                                                        ...Array(4)\n                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-12 bg-gray-200 rounded-xl\"\n                                                        }, i, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 60,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4 pt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-14 w-48 bg-gray-200 rounded-2xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 w-20 bg-gray-200 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 69,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 w-16 bg-gray-200 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 70,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_c = ProductSkeleton;\n// 产品详情页面组件\nfunction ProductPage(param) {\n    let { params } = param;\n    var _dict_common, _dict_common1, _product_images, _dict_common2, _dict_common3, _dict_common4, _dict_common5, _dict_common6, _dict_common7, _dict_common8, _dict_common9, _dict_common10;\n    _s();\n    const { slug, lang } = params;\n    // 状态\n    const [dict, setDict] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({});\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const [selectedImageIndex, setSelectedImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    // 获取数据 - 使用国际化API\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        // 定义一个异步函数来获取数据\n        const fetchData = async ()=>{\n            console.log(\"[产品详情] 开始加载产品数据: \".concat(slug, \", 语言: \").concat(lang));\n            try {\n                setLoading(true);\n                setError(null);\n                // 获取字典\n                const dictionary = await (0,_utils_i18n__WEBPACK_IMPORTED_MODULE_1__.getDictionary)(lang);\n                setDict(dictionary);\n                // 使用国际化API获取产品详情\n                const response = await fetch(\"/api/products/by-slug/\".concat(slug, \"?lang=\").concat(lang));\n                if (response.ok) {\n                    const productData = await response.json();\n                    if (productData.product) {\n                        console.log(\"[产品详情] 成功获取产品数据:\", productData.product.name);\n                        setProduct(productData.product);\n                        setLoading(false);\n                        return;\n                    }\n                }\n                // 如果API失败，回退到直接读取JSON文件\n                console.log(\"[产品详情] API失败，回退到JSON文件\");\n                const jsonResponse = await fetch(\"/mock-products.json\");\n                if (jsonResponse.ok) {\n                    const products = await jsonResponse.json();\n                    const foundProduct = products.find((p)=>p.slug === slug);\n                    if (foundProduct) {\n                        // 根据语言选择相应的字段\n                        const localizedProduct = {\n                            ...foundProduct,\n                            name: lang === \"en\" ? foundProduct.name_en || foundProduct.name : foundProduct.name,\n                            description: lang === \"en\" ? foundProduct.description_en || foundProduct.description : foundProduct.description,\n                            category: lang === \"en\" ? foundProduct.category_en || foundProduct.category : foundProduct.category,\n                            features: lang === \"en\" ? foundProduct.features_en || foundProduct.features : foundProduct.features\n                        };\n                        console.log(\"[产品详情] 从JSON文件获取产品数据:\", localizedProduct.name);\n                        setProduct(localizedProduct);\n                        setLoading(false);\n                        return;\n                    }\n                }\n                // 如果没有找到产品，抛出错误\n                throw new Error(lang === \"zh\" ? \"产品不存在\" : \"Product not found\");\n            } catch (error) {\n                console.error(\"[产品详情] 加载失败:\", error);\n                setError(error instanceof Error ? error.message : lang === \"zh\" ? \"加载失败\" : \"Loading failed\");\n                setLoading(false);\n            }\n        };\n        fetchData();\n    }, [\n        lang,\n        slug\n    ]);\n    // 渲染加载状态\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    style: {\n                        marginTop: \"0\",\n                        display: \"block\",\n                        lineHeight: 0\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-200 w-full\",\n                        style: {\n                            height: \"300px\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"product-detail-container product-detail-fix\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSkeleton, {}, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // 渲染错误状态\n    if (error || !product) {\n        var _dict_common11, _dict_common12;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    style: {\n                        marginTop: \"0\",\n                        display: \"block\",\n                        lineHeight: 0\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-100 w-full\",\n                        style: {\n                            height: \"100px\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"product-detail-container product-detail-fix\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto px-4 py-12 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 text-red-500\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-16 w-16 mx-auto\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    stroke: \"currentColor\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold mb-4 text-gray-800\",\n                                children: ((_dict_common11 = dict.common) === null || _dict_common11 === void 0 ? void 0 : _dict_common11.product_not_found) || (lang === \"zh\" ? \"产品不存在\" : \"Product Not Found\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-8\",\n                                children: error || (lang === \"zh\" ? \"无法找到请求的产品\" : \"The requested product could not be found\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\".concat(lang, \"/products\"),\n                                className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                children: ((_dict_common12 = dict.common) === null || _dict_common12 === void 0 ? void 0 : _dict_common12.products) || (lang === \"zh\" ? \"浏览产品\" : \"Browse Products\")\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true);\n    }\n    // 获取产品图片 - 使用实际图片\n    const getProductImages = (product)=>{\n        if (product.images && product.images.length > 0) {\n            return product.images;\n        }\n        // 如果没有图片，返回默认占位符\n        return [\n            \"/images/products/placeholder.jpg\"\n        ];\n    };\n    // 获取产品规格信息\n    const getProductSpecifications = (product)=>{\n        var _product_category, _product_name, _product_category1, _product_name1, _product_category2, _product_name2;\n        // 根据产品类型返回不同的规格\n        if (product.slug === \"ktv\" || ((_product_category = product.category) === null || _product_category === void 0 ? void 0 : _product_category.includes(\"KTV\")) || ((_product_name = product.name) === null || _product_name === void 0 ? void 0 : _product_name.includes(\"KTV\"))) {\n            return [\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    title: \"投影技术\",\n                    desc: \"4K全息投影系统\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    title: \"容纳人数\",\n                    desc: \"最多20人同时体验\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    title: \"音响配置\",\n                    desc: \"7.1环绕立体声\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    title: \"处理器\",\n                    desc: \"实时渲染引擎\"\n                }\n            ];\n        } else if (((_product_category1 = product.category) === null || _product_category1 === void 0 ? void 0 : _product_category1.includes(\"蹦床\")) || ((_product_name1 = product.name) === null || _product_name1 === void 0 ? void 0 : _product_name1.includes(\"蹦床\"))) {\n            return [\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    title: \"AR技术\",\n                    desc: \"增强现实互动系统\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    title: \"适用年龄\",\n                    desc: \"3-15岁儿童\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    title: \"安全等级\",\n                    desc: \"欧盟CE认证\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    title: \"传感器\",\n                    desc: \"高精度动作捕捉\"\n                }\n            ];\n        } else if (((_product_category2 = product.category) === null || _product_category2 === void 0 ? void 0 : _product_category2.includes(\"沙盘\")) || ((_product_name2 = product.name) === null || _product_name2 === void 0 ? void 0 : _product_name2.includes(\"沙盘\"))) {\n            return [\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    title: \"投影技术\",\n                    desc: \"3D立体投影\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    title: \"互动方式\",\n                    desc: \"手势识别控制\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    title: \"教育内容\",\n                    desc: \"多学科课程包\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    title: \"系统配置\",\n                    desc: \"智能学习算法\"\n                }\n            ];\n        } else {\n            return [\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                    title: \"显示技术\",\n                    desc: \"高清数字显示\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                    title: \"用户体验\",\n                    desc: \"多人互动支持\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    title: \"音效系统\",\n                    desc: \"立体声音响\"\n                },\n                {\n                    icon: _barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    title: \"控制系统\",\n                    desc: \"智能化管理\"\n                }\n            ];\n        }\n    };\n    const specifications = getProductSpecifications(product);\n    const mappedImages = getProductImages(product);\n    const thumbnails = mappedImages;\n    // 正常渲染产品详情 - 现代化设计\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative product-detail-page\",\n        style: {\n            marginTop: 0,\n            paddingTop: 0\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full absolute top-0 left-0 right-0\",\n                style: {\n                    height: \"450px\",\n                    marginTop: \"0\",\n                    zIndex: 0\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/images/products/product-banner.png\",\n                        alt: \"Product Banner\",\n                        className: \"w-full h-full object-cover\",\n                        loading: \"eager\",\n                        fetchPriority: \"high\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        style: {\n                            zIndex: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"product-detail-navigation-overlay\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"breadcrumbs-overlay animate-slide-in-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\".concat(lang),\n                                                children: ((_dict_common = dict.common) === null || _dict_common === void 0 ? void 0 : _dict_common.home) || (lang === \"zh\" ? \"首页\" : \"Home\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"separator\",\n                                                children: \"/\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\".concat(lang, \"/products\"),\n                                                children: ((_dict_common1 = dict.common) === null || _dict_common1 === void 0 ? void 0 : _dict_common1.products) || (lang === \"zh\" ? \"产品\" : \"Products\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"separator\",\n                                                children: \"/\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"current\",\n                                                children: product.title || product.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"product-detail-container bg-white relative\",\n                style: {\n                    marginTop: \"450px\",\n                    zIndex: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-8 py-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-[1.2fr_1fr] gap-24 items-start scale-125 origin-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative aspect-[16/9] bg-gray-100 rounded-lg overflow-hidden\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: thumbnails[selectedImageIndex] || ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : _product_images[0]) || \"/images/products/placeholder.jpg\",\n                                                        alt: product.title || product.name,\n                                                        className: \"w-full h-full object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    product.video_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-900 shadow-lg px-6 py-3 rounded-lg transition-all group\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"w-5 h-5 group-hover:scale-110 transition-transform\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                ((_dict_common2 = dict.common) === null || _dict_common2 === void 0 ? void 0 : _dict_common2.watch_demo) || (lang === \"zh\" ? \"观看演示\" : \"Watch Demo\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this),\n                                            thumbnails && thumbnails.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2 overflow-x-auto pb-2 -mx-2 px-2\",\n                                                children: thumbnails.map((thumb, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setSelectedImageIndex(index),\n                                                        className: \"flex-shrink-0 w-24 h-20 rounded border-2 overflow-hidden transition-colors \".concat(selectedImageIndex === index ? \"border-gray-900\" : \"border-gray-200 hover:border-gray-400\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: thumb || \"/images/products/placeholder.jpg\",\n                                                            alt: \"视图 \".concat(index + 1),\n                                                            className: \"object-cover w-full h-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-gray-900 text-white px-3 py-1 rounded text-sm\",\n                                                            children: ((_dict_common3 = dict.common) === null || _dict_common3 === void 0 ? void 0 : _dict_common3.professional) || (lang === \"zh\" ? \"专业级\" : \"Professional\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                [\n                                                                    ...Array(5)\n                                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cpu_Monitor_Play_Star_Users_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4 fill-gray-900 text-gray-900\"\n                                                                    }, i, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 25\n                                                                    }, this)),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600 ml-2\",\n                                                                    children: [\n                                                                        \"4.9 (128 \",\n                                                                        ((_dict_common4 = dict.common) === null || _dict_common4 === void 0 ? void 0 : _dict_common4.reviews) || (lang === \"zh\" ? \"评价\" : \"reviews\"),\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-4xl lg:text-5xl font-light text-gray-900 mb-4 leading-tight\",\n                                                            children: product.title || product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xl text-gray-600 leading-relaxed\",\n                                                            children: product.description || (lang === \"zh\" ? \"专业级互动设备，采用先进技术为用户提供沉浸式体验解决方案。\" : \"Professional interactive equipment using advanced technology to provide immersive experience solutions.\")\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6\",\n                                                    children: specifications.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(spec.icon, {\n                                                                            className: \"w-5 h-5 text-gray-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-gray-900\",\n                                                                            children: spec.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 377,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 text-sm\",\n                                                                    children: spec.desc\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                href: \"/\".concat(lang, \"/pages/contact-us\"),\n                                                                className: \"flex-1 bg-gray-900 hover:bg-gray-800 text-white h-12 px-6 rounded-lg flex items-center justify-center transition-colors\",\n                                                                children: ((_dict_common5 = dict.common) === null || _dict_common5 === void 0 ? void 0 : _dict_common5.get_quote) || (lang === \"zh\" ? \"获取报价\" : \"Get Quote\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        ((_dict_common6 = dict.common) === null || _dict_common6 === void 0 ? void 0 : _dict_common6.in_stock) || (lang === \"zh\" ? \"现货供应\" : \"In Stock\")\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: ((_dict_common7 = dict.common) === null || _dict_common7 === void 0 ? void 0 : _dict_common7.professional_installation) || (lang === \"zh\" ? \"专业安装\" : \"Professional Installation\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: ((_dict_common8 = dict.common) === null || _dict_common8 === void 0 ? void 0 : _dict_common8.three_year_warranty) || (lang === \"zh\" ? \"质保3年\" : \"3-Year Warranty\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-6xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl lg:text-5xl font-light text-gray-900 mb-6\",\n                                                children: ((_dict_common9 = dict.common) === null || _dict_common9 === void 0 ? void 0 : _dict_common9.product_information) || (lang === \"zh\" ? \"产品信息\" : \"Product Information\")\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed\",\n                                                children: lang === \"zh\" ? \"详细了解产品的技术规格、核心特性和应用场景\" : \"Learn more about technical specifications, key features and application scenarios\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-b border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                                    className: \"flex\",\n                                                    children: [\n                                                        {\n                                                            id: \"overview\",\n                                                            label: lang === \"zh\" ? \"产品概述\" : \"Overview\"\n                                                        },\n                                                        {\n                                                            id: \"features\",\n                                                            label: lang === \"zh\" ? \"核心特性\" : \"Features\"\n                                                        },\n                                                        {\n                                                            id: \"specs\",\n                                                            label: lang === \"zh\" ? \"技术规格\" : \"Specifications\"\n                                                        },\n                                                        {\n                                                            id: \"applications\",\n                                                            label: lang === \"zh\" ? \"应用场景\" : \"Applications\"\n                                                        }\n                                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setActiveTab(tab.id),\n                                                            className: \"flex-1 px-6 py-4 text-sm font-medium transition-colors \".concat(activeTab === tab.id ? \"bg-gray-50 text-gray-900 border-b-2 border-gray-900\" : \"text-gray-500 hover:text-gray-700 hover:bg-gray-50\"),\n                                                            children: tab.label\n                                                        }, tab.id, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-8\",\n                                                children: [\n                                                    activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"prose max-w-none\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg text-gray-700 leading-relaxed\",\n                                                                    children: product.description || (lang === \"zh\" ? \"专业级互动设备，采用先进技术为用户提供沉浸式体验解决方案。\" : \"Professional interactive equipment using advanced technology to provide immersive experience solutions.\")\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 457,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            product.features && product.features.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                        children: lang === \"zh\" ? \"主要特点\" : \"Key Features\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                        children: product.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-2 h-2 bg-gray-900 rounded-full flex-shrink-0\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                        lineNumber: 470,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-700\",\n                                                                                        children: feature\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                        lineNumber: 471,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 469,\n                                                                                columnNumber: 31\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeTab === \"features\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                        children: specifications.map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(spec.icon, {\n                                                                                className: \"w-6 h-6 text-gray-700\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                lineNumber: 487,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 486,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"text-lg font-semibold text-gray-900\",\n                                                                                    children: spec.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 490,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-gray-600\",\n                                                                                    children: spec.desc\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 491,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 489,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, index, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 482,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeTab === \"specs\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                                            children: lang === \"zh\" ? \"基本规格\" : \"Basic Specifications\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 504,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between py-2 border-b border-gray-100\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-600\",\n                                                                                            children: lang === \"zh\" ? \"产品类型\" : \"Product Type\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                            lineNumber: 509,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-900\",\n                                                                                            children: product.type || (lang === \"zh\" ? \"互动设备\" : \"Interactive Equipment\")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                            lineNumber: 510,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 508,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between py-2 border-b border-gray-100\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-600\",\n                                                                                            children: lang === \"zh\" ? \"产品分类\" : \"Category\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                            lineNumber: 513,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-900\",\n                                                                                            children: product.category || (lang === \"zh\" ? \"运动娱乐\" : \"Sports & Entertainment\")\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                            lineNumber: 514,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 512,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between py-2 border-b border-gray-100\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-600\",\n                                                                                            children: lang === \"zh\" ? \"库存状态\" : \"Stock Status\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                            lineNumber: 517,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"\".concat(product.in_stock ? \"text-green-600\" : \"text-red-600\"),\n                                                                                            children: product.in_stock ? lang === \"zh\" ? \"现货供应\" : \"In Stock\" : lang === \"zh\" ? \"缺货\" : \"Out of Stock\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                            lineNumber: 518,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 516,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 507,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                                            children: lang === \"zh\" ? \"技术特性\" : \"Technical Features\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 526,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"space-y-3\",\n                                                                            children: specifications.slice(0, 3).map((spec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex justify-between py-2 border-b border-gray-100\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-600\",\n                                                                                            children: spec.title\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                            lineNumber: 532,\n                                                                                            columnNumber: 33\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-900\",\n                                                                                            children: spec.desc\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                            lineNumber: 533,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, index, true, {\n                                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                                    lineNumber: 531,\n                                                                                    columnNumber: 31\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeTab === \"applications\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-8\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                                            children: [\n                                                                {\n                                                                    title: lang === \"zh\" ? \"商业场所\" : \"Commercial Venues\",\n                                                                    desc: lang === \"zh\" ? \"购物中心、游乐园、主题公园等商业娱乐场所\" : \"Shopping malls, amusement parks, theme parks and other commercial entertainment venues\",\n                                                                    icon: \"\\uD83C\\uDFE2\"\n                                                                },\n                                                                {\n                                                                    title: lang === \"zh\" ? \"教育机构\" : \"Educational Institutions\",\n                                                                    desc: lang === \"zh\" ? \"学校、培训中心、科技馆等教育培训场所\" : \"Schools, training centers, science museums and other educational venues\",\n                                                                    icon: \"\\uD83C\\uDF93\"\n                                                                },\n                                                                {\n                                                                    title: lang === \"zh\" ? \"体验中心\" : \"Experience Centers\",\n                                                                    desc: lang === \"zh\" ? \"体验馆、展示厅、互动中心等专业体验场所\" : \"Experience halls, showrooms, interactive centers and other professional venues\",\n                                                                    icon: \"\\uD83C\\uDFAF\"\n                                                                }\n                                                            ].map((app, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-4xl\",\n                                                                            children: app.icon\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-lg font-semibold text-gray-900\",\n                                                                            children: app.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 565,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-gray-600 text-sm leading-relaxed\",\n                                                                            children: app.desc\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                            lineNumber: 566,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl lg:text-5xl font-light text-gray-900 mb-6\",\n                                        children: ((_dict_common10 = dict.common) === null || _dict_common10 === void 0 ? void 0 : _dict_common10.product_gallery) || (lang === \"zh\" ? \"产品展示\" : \"Product Gallery\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed\",\n                                        children: lang === \"zh\" ? \"专业级\".concat(product.category || \"互动设备\", \"在不同应用场景中的实际效果展示\") : \"Professional \".concat(product.category || \"interactive equipment\", \" showcased in various application scenarios\")\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-0\",\n                                children: mappedImages.filter(Boolean).map((image, index)=>{\n                                    var _dict_common, _dict_common1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-full h-screen bg-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: image || \"/images/products/placeholder.jpg\",\n                                                alt: \"\".concat(product.title || product.name, \" - \").concat(((_dict_common = dict.common) === null || _dict_common === void 0 ? void 0 : _dict_common.application_scenario) || (lang === \"zh\" ? \"应用场景\" : \"Application scenario\"), \" \").concat(index + 1),\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"container mx-auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white text-lg font-medium\",\n                                                        children: [\n                                                            product.title || product.name,\n                                                            \" - \",\n                                                            ((_dict_common1 = dict.common) === null || _dict_common1 === void 0 ? void 0 : _dict_common1.application_scenario) || (lang === \"zh\" ? \"应用场景\" : \"Application Scenario\"),\n                                                            \" \",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 591,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n                lineNumber: 300,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\products\\\\[slug]\\\\page.tsx\",\n        lineNumber: 266,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductPage, \"Uzsa2ydllt3CX7Io1AHebWWioLk=\");\n_c1 = ProductPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ProductSkeleton\");\n$RefreshReg$(_c1, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[lang]/products/[slug]/page.tsx\n"));

/***/ })

});