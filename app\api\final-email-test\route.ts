import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 开始最终邮件测试...');
    
    // 测试数据
    const testData = {
      id: 9999,
      name: "李总",
      email: "<EMAIL>",
      phone: "+86 138-0000-9999",
      country: "中国",
      playground_size: "3000+ sqm",
      product: "AR全息互动娱乐系统",
      message: "您好，我们是一家大型娱乐集团，在全国拥有20多家大型娱乐场所。我们对贵公司的AR全息互动娱乐系统非常感兴趣，希望了解：1. 完整的产品线和技术参数 2. 大批量采购的优惠政策 3. 全国范围的安装和维护服务 4. 投资回报率分析。我们计划投资5000万元用于设备升级，希望能够建立长期合作关系。",
      created_at: new Date().toISOString()
    };

    const targetEmail = '<EMAIL>';
    
    // 检查可用的邮件服务
    const emailServices = {
      resend: !!process.env.RESEND_API_KEY,
      smtp: !!(process.env.SMTP_USER && process.env.SMTP_PASS),
      sendgrid: !!process.env.SENDGRID_API_KEY
    };

    console.log('📧 可用的邮件服务:', emailServices);

    // 方案1: 使用Resend (发送到验证邮箱，包含转发说明)
    if (emailServices.resend) {
      try {
        const { Resend } = require('resend');
        const resend = new Resend(process.env.RESEND_API_KEY);
        
        console.log('📤 尝试使用Resend发送...');
        
        const result = await resend.emails.send({
          from: '跨境电商网站 <<EMAIL>>',
          to: ['<EMAIL>'],
          subject: `🔔 重要：新客户咨询 - ${testData.name} (请转发到 ${targetEmail})`,
          html: `
            <div style="font-family: 'Segoe UI', Arial, sans-serif; max-width: 650px; margin: 0 auto; background: #f8f9fa;">
              <!-- 头部 -->
              <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 40px 30px; text-align: center; border-radius: 10px 10px 0 0;">
                <h1 style="margin: 0; font-size: 32px; font-weight: bold;">🚨 重要客户咨询</h1>
                <p style="margin: 15px 0 0 0; font-size: 18px; opacity: 0.95;">高价值客户 - 需要立即处理</p>
              </div>

              <!-- 转发提醒 -->
              <div style="background: #fff3cd; border-left: 5px solid #ffc107; padding: 20px; margin: 0;">
                <h3 style="color: #856404; margin: 0 0 10px 0; font-size: 18px;">📧 重要提醒</h3>
                <p style="color: #856404; margin: 0; font-size: 16px; line-height: 1.5;">
                  <strong>请立即将此邮件转发到：${targetEmail}</strong><br>
                  或直接联系客户：${testData.phone}
                </p>
              </div>

              <!-- 客户信息 -->
              <div style="background: white; padding: 30px;">
                <div style="background: #e3f2fd; padding: 25px; border-radius: 10px; margin-bottom: 25px; border-left: 5px solid #2196f3;">
                  <h2 style="color: #1565c0; margin: 0 0 20px 0; font-size: 22px;">👤 客户详情</h2>
                  <div style="display: grid; gap: 12px;">
                    <div style="display: flex; padding: 8px 0; border-bottom: 1px solid #e1f5fe;">
                      <span style="font-weight: bold; color: #1565c0; width: 120px;">姓名:</span>
                      <span style="color: #333; font-size: 16px;">${testData.name}</span>
                    </div>
                    <div style="display: flex; padding: 8px 0; border-bottom: 1px solid #e1f5fe;">
                      <span style="font-weight: bold; color: #1565c0; width: 120px;">邮箱:</span>
                      <span style="color: #333; font-size: 16px;">${testData.email}</span>
                    </div>
                    <div style="display: flex; padding: 8px 0; border-bottom: 1px solid #e1f5fe;">
                      <span style="font-weight: bold; color: #1565c0; width: 120px;">电话:</span>
                      <span style="color: #333; font-size: 16px; font-weight: bold;">${testData.phone}</span>
                    </div>
                    <div style="display: flex; padding: 8px 0; border-bottom: 1px solid #e1f5fe;">
                      <span style="font-weight: bold; color: #1565c0; width: 120px;">国家:</span>
                      <span style="color: #333; font-size: 16px;">${testData.country}</span>
                    </div>
                    <div style="display: flex; padding: 8px 0; border-bottom: 1px solid #e1f5fe;">
                      <span style="font-weight: bold; color: #1565c0; width: 120px;">场地规模:</span>
                      <span style="color: #333; font-size: 16px; font-weight: bold;">${testData.playground_size}</span>
                    </div>
                    <div style="display: flex; padding: 8px 0;">
                      <span style="font-weight: bold; color: #1565c0; width: 120px;">产品需求:</span>
                      <span style="color: #333; font-size: 16px; font-weight: bold;">${testData.product}</span>
                    </div>
                  </div>
                </div>

                <!-- 客户留言 -->
                <div style="background: #f1f8e9; padding: 25px; border-radius: 10px; border-left: 5px solid #4caf50; margin-bottom: 25px;">
                  <h2 style="color: #2e7d32; margin: 0 0 15px 0; font-size: 22px;">💬 客户留言</h2>
                  <p style="color: #2e7d32; margin: 0; font-size: 16px; line-height: 1.8; font-weight: 500;">${testData.message}</p>
                </div>

                <!-- 处理建议 -->
                <div style="background: #ffebee; padding: 25px; border-radius: 10px; border-left: 5px solid #f44336; margin-bottom: 25px;">
                  <h3 style="color: #c62828; margin: 0 0 15px 0; font-size: 20px;">🎯 立即行动</h3>
                  <ol style="color: #c62828; margin: 0; padding-left: 20px; font-size: 16px; line-height: 1.6;">
                    <li><strong>转发邮件</strong>到 ${targetEmail}</li>
                    <li><strong>电话联系</strong>：${testData.phone}</li>
                    <li><strong>邮件回复</strong>：${testData.email}</li>
                    <li><strong>准备资料</strong>：产品介绍、价格方案、案例展示</li>
                  </ol>
                </div>

                <!-- 提交信息 -->
                <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; text-align: center;">
                  <p style="margin: 0; color: #666; font-size: 14px;">
                    <strong>📅 提交时间:</strong> ${new Date(testData.created_at).toLocaleString('zh-CN')}<br>
                    <strong>🆔 咨询ID:</strong> #${testData.id}<br>
                    <strong>📧 邮件服务:</strong> Resend 专业版<br>
                    <strong>🎯 目标收件人:</strong> ${targetEmail}
                  </p>
                </div>
              </div>

              <!-- 底部 -->
              <div style="background: #37474f; color: white; padding: 25px; text-align: center; border-radius: 0 0 10px 10px;">
                <p style="margin: 0; font-size: 16px;">跨境电商网站 - 客户关系管理系统</p>
                <p style="margin: 10px 0 0 0; font-size: 14px; opacity: 0.8;">此邮件由系统自动发送，请及时处理客户咨询</p>
              </div>
            </div>
          `,
          text: `
🚨 重要客户咨询 - ${testData.name}

📧 重要提醒：请立即将此邮件转发到 ${targetEmail}

👤 客户信息：
- 姓名：${testData.name}
- 邮箱：${testData.email}
- 电话：${testData.phone}
- 国家：${testData.country}
- 场地规模：${testData.playground_size}
- 产品需求：${testData.product}

💬 客户留言：
${testData.message}

🎯 立即行动：
1. 转发邮件到 ${targetEmail}
2. 电话联系：${testData.phone}
3. 邮件回复：${testData.email}
4. 准备资料：产品介绍、价格方案、案例展示

📅 提交时间：${new Date(testData.created_at).toLocaleString('zh-CN')}
🆔 咨询ID：#${testData.id}
📧 邮件服务：Resend 专业版
🎯 目标收件人：${targetEmail}

跨境电商网站 - 客户关系管理系统
          `
        });

        if (result.data?.id) {
          console.log('✅ Resend发送成功:', result.data.id);
          return NextResponse.json({
            success: true,
            message: '邮件发送成功！请检查您的邮箱 <EMAIL>',
            service: 'Resend',
            messageId: result.data.id,
            sentTo: '<EMAIL>',
            targetEmail: targetEmail,
            action: '请将收到的邮件转发到 <EMAIL>',
            customerPhone: testData.phone
          });
        }
      } catch (resendError) {
        console.error('❌ Resend发送失败:', resendError);
      }
    }

    // 备用方案：详细的控制台记录
    console.log('\n' + '='.repeat(100));
    console.log('📧 邮件通知系统 - 控制台记录');
    console.log('='.repeat(100));
    console.log(`🎯 目标收件人: ${targetEmail}`);
    console.log(`📱 客户电话: ${testData.phone} (可直接联系)`);
    console.log(`📧 客户邮箱: ${testData.email}`);
    console.log('');
    console.log('🚨 高价值客户咨询 - 需要立即处理');
    console.log('');
    console.log('👤 客户详情:');
    console.log(`   姓名: ${testData.name}`);
    console.log(`   邮箱: ${testData.email}`);
    console.log(`   电话: ${testData.phone}`);
    console.log(`   国家: ${testData.country}`);
    console.log(`   场地规模: ${testData.playground_size}`);
    console.log(`   产品需求: ${testData.product}`);
    console.log('');
    console.log('💬 客户留言:');
    console.log(`   ${testData.message}`);
    console.log('');
    console.log('🎯 立即行动建议:');
    console.log(`   1. 电话联系客户: ${testData.phone}`);
    console.log(`   2. 邮件回复客户: ${testData.email}`);
    console.log(`   3. 发送邮件到: ${targetEmail}`);
    console.log(`   4. 准备产品资料和报价方案`);
    console.log('');
    console.log(`📅 提交时间: ${new Date(testData.created_at).toLocaleString('zh-CN')}`);
    console.log(`🆔 咨询ID: #${testData.id}`);
    console.log('='.repeat(100));

    return NextResponse.json({
      success: true,
      message: '邮件通知已记录到控制台，请查看服务器日志',
      method: 'console_log',
      customerInfo: {
        name: testData.name,
        phone: testData.phone,
        email: testData.email,
        product: testData.product
      },
      targetEmail: targetEmail,
      action: '请直接联系客户或发送邮件通知',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 邮件测试失败:', error);
    return NextResponse.json({
      success: false,
      message: '邮件测试发生错误',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: '最终邮件测试系统',
    endpoint: '/api/final-email-test',
    method: 'POST',
    targetEmail: '<EMAIL>',
    solutions: [
      'Resend转发到验证邮箱 (<EMAIL>)',
      '控制台详细记录',
      '客户联系信息提供'
    ],
    note: '确保重要客户咨询不会丢失'
  });
}
