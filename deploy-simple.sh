#!/bin/bash

echo "🚀 选择部署方式："
echo "1. Vercel (推荐 - 免费)"
echo "2. Railway (包含数据库)"
echo "3. 阿里云函数计算"
echo "4. Netlify"

read -p "请选择 (1-4): " choice

case $choice in
  1)
    echo "📦 部署到 Vercel..."
    
    # 检查是否安装了Vercel CLI
    if ! command -v vercel &> /dev/null; then
        echo "安装 Vercel CLI..."
        npm install -g vercel
    fi
    
    # 部署
    echo "🚀 开始部署..."
    vercel --prod
    
    echo "✅ 部署完成！"
    echo "🌐 您的网站已上线，访问链接将显示在上方"
    ;;
    
  2)
    echo "🚂 部署到 Railway..."
    
    # 检查是否安装了Railway CLI
    if ! command -v railway &> /dev/null; then
        echo "安装 Railway CLI..."
        npm install -g @railway/cli
    fi
    
    # 登录和部署
    echo "🔐 请先登录 Railway..."
    railway login
    
    echo "🚀 开始部署..."
    railway up
    
    echo "✅ 部署完成！"
    ;;
    
  3)
    echo "☁️ 部署到阿里云函数计算..."
    echo "请访问: https://fc.console.aliyun.com/"
    echo "选择 Web函数 -> Next.js 模板"
    echo "上传项目代码即可"
    ;;
    
  4)
    echo "🌐 部署到 Netlify..."
    
    # 检查是否安装了Netlify CLI
    if ! command -v netlify &> /dev/null; then
        echo "安装 Netlify CLI..."
        npm install -g netlify-cli
    fi
    
    # 构建和部署
    echo "🔨 构建项目..."
    npm run build
    
    echo "🚀 开始部署..."
    netlify deploy --prod --dir=.next
    
    echo "✅ 部署完成！"
    ;;
    
  *)
    echo "❌ 无效选择"
    exit 1
    ;;
esac

echo ""
echo "🎉 部署完成！"
echo "💡 提示："
echo "  - 如果使用数据库，记得配置环境变量"
echo "  - 域名可以在平台控制台自定义"
echo "  - 支持自动部署，推送代码即可更新"
