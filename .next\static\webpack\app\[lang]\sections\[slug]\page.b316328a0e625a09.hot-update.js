"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lang]/sections/[slug]/page",{

/***/ "(app-pages-browser)/./app/[lang]/sections/[slug]/page.tsx":
/*!*********************************************!*\
  !*** ./app/[lang]/sections/[slug]/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SectionPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _components_LanguageProvider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../components/LanguageProvider */ \"(app-pages-browser)/./app/components/LanguageProvider.tsx\");\n/* harmony import */ var _components_PageHeader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../components/PageHeader */ \"(app-pages-browser)/./app/components/PageHeader.tsx\");\n/* harmony import */ var _components_SeriesGallery__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../components/SeriesGallery */ \"(app-pages-browser)/./app/components/SeriesGallery.tsx\");\n/* harmony import */ var _utils_i18n__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../../utils/i18n */ \"(app-pages-browser)/./app/utils/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// 板块数据配置\nconst sectionsData = {\n    \"interactive-projection\": {\n        slug: \"interactive-projection\",\n        title: \"互动投影系列\",\n        description: \"创新的互动投影技术，打造沉浸式体验空间，让用户与数字内容进行自然交互\",\n        category: \"interactive\",\n        images: [\n            \"/images/product-sections/0007.jpg\",\n            \"/images/products/interactive-ball/interactive-ball-1.jpg\",\n            \"/images/products/interactive-ball/interactive-ball-2.jpg\",\n            \"/images/products/interactive-ball/interactive-ball-3.jpg\",\n            \"/images/products/interactive-ball/interactive-ball-5.jpg\",\n            \"/images/products/interactive-ball/interactive-ball-6.jpg\",\n            \"/images/products/interactive-football-1.jpg\"\n        ],\n        products: [\n            {\n                id: \"1\",\n                name: \"互动砸球系统\",\n                image: \"/images/products/interactive-ball/interactive-ball-1.jpg\",\n                slug: \"interactive-ball-system\",\n                description: \"创新的互动砸球游戏系统，结合投影技术和体感识别\"\n            },\n            {\n                id: \"2\",\n                name: \"互动足球游戏\",\n                image: \"/images/products/interactive-football-1.jpg\",\n                slug: \"interactive-football\",\n                description: \"沉浸式足球互动体验，让运动更加有趣\"\n            },\n            {\n                id: \"3\",\n                name: \"蹦床互动系统\",\n                image: \"/images/products/trampoline-1.jpg\",\n                slug: \"trampoline-interactive\",\n                description: \"结合蹦床运动的互动投影娱乐系统\"\n            }\n        ]\n    },\n    \"interactive-projection-series\": {\n        slug: \"interactive-projection-series\",\n        title: \"互动投影系列\",\n        description: \"创新的互动投影技术，打造沉浸式体验空间，让用户与数字内容进行自然交互\",\n        category: \"interactive\",\n        images: [\n            \"/images/product-sections/0007.jpg\",\n            \"/images/products/interactive-ball/interactive-ball-1.jpg\",\n            \"/images/products/interactive-ball/interactive-ball-2.jpg\",\n            \"/images/products/interactive-ball/interactive-ball-3.jpg\",\n            \"/images/products/interactive-ball/interactive-ball-5.jpg\",\n            \"/images/products/interactive-ball/interactive-ball-6.jpg\",\n            \"/images/products/interactive-football-1.jpg\"\n        ],\n        products: [\n            {\n                id: \"1\",\n                name: \"互动砸球系统\",\n                image: \"/images/products/interactive-ball/interactive-ball-1.jpg\",\n                slug: \"interactive-ball-system\",\n                description: \"创新的互动砸球游戏系统，结合投影技术和体感识别\"\n            },\n            {\n                id: \"2\",\n                name: \"互动足球游戏\",\n                image: \"/images/products/interactive-football-1.jpg\",\n                slug: \"interactive-football\",\n                description: \"沉浸式足球互动体验，让运动更加有趣\"\n            },\n            {\n                id: \"3\",\n                name: \"蹦床互动系统\",\n                image: \"/images/products/trampoline-1.jpg\",\n                slug: \"trampoline-interactive\",\n                description: \"结合蹦床运动的互动投影娱乐系统\"\n            }\n        ]\n    },\n    \"holographic-display\": {\n        slug: \"holographic-display\",\n        title: \"全息展示系列\",\n        description: \"先进的全息投影技术，呈现震撼视觉效果，创造前所未有的视觉体验\",\n        category: \"holographic\",\n        images: [\n            \"/images/product-sections/0012.jpg\",\n            \"/images/products/hologram-dining-1.jpg\",\n            \"/images/products/hologram-dining-2.jpg\",\n            \"/images/products/hologram-stage-1.jpg\",\n            \"/images/products/hologram-stage-2.jpg\",\n            \"/images/products/产品介绍模板_01.jpg\",\n            \"/images/products/产品介绍模板_02.jpg\"\n        ],\n        products: [\n            {\n                id: \"2\",\n                name: \"全息沙盘\",\n                image: \"/images/products/hologram-dining-1.jpg\",\n                slug: \"holographic-sand-table\",\n                description: \"全息投影沙盘展示系统，实现立体化信息展示\"\n            },\n            {\n                id: \"3\",\n                name: \"全息舞台\",\n                image: \"/images/products/hologram-stage-1.jpg\",\n                slug: \"holographic-stage\",\n                description: \"震撼的全息舞台表演系统，打造视觉盛宴\"\n            },\n            {\n                id: \"4\",\n                name: \"全息餐厅\",\n                image: \"/images/products/hologram-dining-2.jpg\",\n                slug: \"holographic-dining\",\n                description: \"沉浸式全息餐厅体验，重新定义用餐环境\"\n            }\n        ]\n    },\n    \"digital-sandbox\": {\n        slug: \"digital-sandbox\",\n        title: \"数字沙盘系列\",\n        description: \"智能数字沙盘，实现精准展示与互动控制，为规划展示提供创新解决方案\",\n        category: \"digital\",\n        images: [\n            \"/images/product-sections/0021.jpg\",\n            \"/images/products/3d-1.jpg\",\n            \"/images/products/3d-2.jpg\",\n            \"/images/products/3d-3.jpg\",\n            \"/images/products/3d-4.jpg\",\n            \"/images/products/3d-5.jpg\",\n            \"/images/products/3d-6.jpg\",\n            \"/images/products/3d-sandbox-1.jpg\"\n        ],\n        products: [\n            {\n                id: \"5\",\n                name: \"智能规划沙盘\",\n                image: \"/images/products/3d-2.jpg\",\n                slug: \"3d-planning\",\n                description: \"智能化城市规划展示沙盘，支持实时数据更新\"\n            },\n            {\n                id: \"6\",\n                name: \"互动数字沙盘\",\n                image: \"/images/products/3d-sandbox-1.jpg\",\n                slug: \"interactive-sandbox\",\n                description: \"支持多点触控的互动数字沙盘系统\"\n            }\n        ]\n    },\n    \"ar-reality\": {\n        slug: \"ar-reality\",\n        title: \"AR增强现实系列\",\n        description: \"AR增强现实技术，融合虚拟与现实世界，创造全新的交互体验\",\n        category: \"ar\",\n        images: [\n            \"/images/product-sections/0035.jpg\",\n            \"/images/products/ar-1.jpg\",\n            \"/images/products/ar-2.jpg\",\n            \"/images/products/ar-3.jpg\",\n            \"/images/products/ar-4.jpg\",\n            \"/images/products/ar-education-1.jpg\",\n            \"/images/products/ar-education-2.jpg\",\n            \"/images/products/ar-education-3.jpg\"\n        ],\n        products: [\n            {\n                id: \"4\",\n                name: \"AR教育系统\",\n                image: \"/images/products/ar-1.jpg\",\n                slug: \"ar\",\n                description: \"AR增强现实教育系统，提供沉浸式学习体验\"\n            },\n            {\n                id: \"7\",\n                name: \"AR展示系统\",\n                image: \"/images/products/ar-education-1.jpg\",\n                slug: \"ar-display\",\n                description: \"专业AR展示解决方案，适用于展览展示\"\n            },\n            {\n                id: \"8\",\n                name: \"AR互动体验\",\n                image: \"/images/products/ar-education-2.jpg\",\n                slug: \"ar-interactive\",\n                description: \"创新AR互动体验系统，打造未来科技感\"\n            }\n        ]\n    },\n    \"smart-integrated\": {\n        slug: \"smart-integrated\",\n        title: \"智能一体机系列\",\n        description: \"集成化智能设备，提供完整解决方案，满足多样化应用需求\",\n        category: \"smart\",\n        images: [\n            \"/images/product-sections/0046.jpg\",\n            \"/images/products/ktv-1.jpg\",\n            \"/images/products/ktv-2.jpg\",\n            \"/images/products/ktv-3.jpg\",\n            \"/images/products/ktv-4.jpg\",\n            \"/images/products/ktv-5.jpg\",\n            \"/images/products/ktv-6.jpg\",\n            \"/images/products/-1.jpg\"\n        ],\n        products: [\n            {\n                id: \"5\",\n                name: \"KTV智能系统\",\n                image: \"/images/products/ktv-1.jpg\",\n                slug: \"ktv\",\n                description: \"KTV智能娱乐系统，提供全方位娱乐体验\"\n            },\n            {\n                id: \"9\",\n                name: \"智能会议系统\",\n                image: \"/images/products/ktv-2.jpg\",\n                slug: \"smart-meeting\",\n                description: \"高效智能会议解决方案，提升办公效率\"\n            },\n            {\n                id: \"10\",\n                name: \"多媒体一体机\",\n                image: \"/images/products/-1.jpg\",\n                slug: \"multimedia-system\",\n                description: \"集成多媒体功能的智能一体机设备\"\n            }\n        ]\n    }\n};\nfunction SectionPage() {\n    var _dict_common, _dict_common1, _dict_common2, _dict_common3, _dict_common4, _dict_common5, _dict_common6;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { locale } = (0,_components_LanguageProvider__WEBPACK_IMPORTED_MODULE_6__.useLanguage)();\n    const [sectionData, setSectionData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [dict, setDict] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    // 加载字典\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const loadDictionary = async ()=>{\n            try {\n                const dictionary = await (0,_utils_i18n__WEBPACK_IMPORTED_MODULE_9__.getDictionary)(locale);\n                setDict(dictionary);\n            } catch (error) {\n                console.error(\"Failed to load dictionary:\", error);\n                // 设置默认字典\n                setDict({\n                    common: {\n                        home: locale === \"zh\" ? \"首页\" : \"Home\",\n                        products: locale === \"zh\" ? \"产品中心\" : \"Products\",\n                        related_products: locale === \"zh\" ? \"相关产品\" : \"Related Products\",\n                        product_details: locale === \"zh\" ? \"产品详情\" : \"Product Details\",\n                        view_more_products: locale === \"zh\" ? \"查看更多产品\" : \"View More Products\",\n                        explore_complete_series: locale === \"zh\" ? \"探索我们的完整产品系列\" : \"Explore our complete product series\",\n                        browse_all: locale === \"zh\" ? \"浏览全部\" : \"Browse All\",\n                        back_to_products: locale === \"zh\" ? \"返回产品中心\" : \"Back to Products\"\n                    }\n                });\n            }\n        };\n        loadDictionary();\n    }, [\n        locale\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!params) {\n            router.push(\"/\".concat(locale, \"/products\"));\n            return;\n        }\n        const slug = params.slug;\n        const data = sectionsData[slug];\n        if (data) {\n            setSectionData(data);\n        } else {\n            // 如果找不到对应的板块，重定向到产品页面\n            router.push(\"/\".concat(locale, \"/products\"));\n        }\n    }, [\n        params === null || params === void 0 ? void 0 : params.slug,\n        router,\n        locale\n    ]);\n    if (!sectionData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: locale === \"zh\" ? \"加载中...\" : \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                lineNumber: 310,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n            lineNumber: 309,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"section-page\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PageHeader__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        title: \"\",\n                        subtitle: \"\",\n                        bgImage: sectionData.images[0],\n                        height: \"md:h-[400px] h-[300px]\",\n                        overlayOpacity: 0.7,\n                        useImageComponent: true,\n                        animationEffect: \"up\",\n                        className: \"mb-16\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"absolute inset-0 flex items-center justify-center z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"container mx-auto px-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 animate-fade-in-up\",\n                                            children: sectionData.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        sectionData.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-lg md:text-xl text-gray-200 max-w-3xl mx-auto mb-6 animate-fade-in-up-delay\",\n                                            children: sectionData.description\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"breadcrumbs-overlay flex items-center justify-center space-x-2 text-sm animate-slide-in-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/\".concat(locale),\n                                            className: \"text-white hover:text-white transition-all duration-200 px-3 py-2 rounded-lg bg-black/30 backdrop-blur-sm border border-white/20 hover:bg-black/50 hover:border-white/30 hover:-translate-y-0.5 font-medium text-shadow-lg\",\n                                            children: ((_dict_common = dict.common) === null || _dict_common === void 0 ? void 0 : _dict_common.home) || (locale === \"zh\" ? \"首页\" : \"Home\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-white/80 mx-2 text-base font-bold text-shadow-lg\",\n                                            children: \"/\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/\".concat(locale, \"/products\"),\n                                            className: \"text-white hover:text-white transition-all duration-200 px-3 py-2 rounded-lg bg-black/30 backdrop-blur-sm border border-white/20 hover:bg-black/50 hover:border-white/30 hover:-translate-y-0.5 font-medium text-shadow-lg\",\n                                            children: ((_dict_common1 = dict.common) === null || _dict_common1 === void 0 ? void 0 : _dict_common1.products) || (locale === \"zh\" ? \"产品中心\" : \"Products\")\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-white/80 mx-2 text-base font-bold text-shadow-lg\",\n                                            children: \"/\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-white font-semibold px-3 py-2 rounded-lg bg-white/15 backdrop-blur-sm border border-white/30 text-shadow-lg\",\n                                            children: sectionData.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"container mx-auto px-4 mb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SeriesGallery__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sectionSlug: sectionData.slug\n                }, void 0, false, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 374,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, this),\n            sectionData.products.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"container mx-auto px-4 mb-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"related-products\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-2xl font-bold text-gray-900 mb-8 text-center\",\n                            children: ((_dict_common2 = dict.common) === null || _dict_common2 === void 0 ? void 0 : _dict_common2.related_products) || (locale === \"zh\" ? \"相关产品\" : \"Related Products\")\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"products-horizontal-scroll\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"flex gap-6 overflow-x-auto pb-4 scrollbar-hide\",\n                                children: [\n                                    sectionData.products.map((product)=>{\n                                        var _dict_common;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/\".concat(locale, \"/products\"),\n                                            className: \"product-mini-card group flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300 group-hover:shadow-lg group-hover:-translate-y-1 w-72\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"relative w-full h-40\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                                src: product.image,\n                                                                alt: product.name,\n                                                                fill: true,\n                                                                style: {\n                                                                    objectFit: \"cover\"\n                                                                },\n                                                                className: \"transition-transform duration-300 group-hover:scale-105\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"absolute top-3 left-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium\",\n                                                                    children: sectionData.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-base font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors line-clamp-1\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-gray-600 text-sm line-clamp-2 mb-3\",\n                                                                children: product.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                                                                        children: ((_dict_common = dict.common) === null || _dict_common === void 0 ? void 0 : _dict_common.product_details) || (locale === \"zh\" ? \"产品详情\" : \"Product Details\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"fas fa-arrow-right text-blue-500 text-sm transition-transform group-hover:translate-x-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, product.id, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 19\n                                        }, this);\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                        href: \"/\".concat(locale, \"/products\"),\n                                        className: \"view-more-card group flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border-2 border-dashed border-blue-200 overflow-hidden transition-all duration-300 group-hover:border-blue-400 group-hover:shadow-lg w-72 h-full flex items-center justify-center min-h-[240px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-center p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-200 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"fas fa-plus text-blue-600 text-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-base font-semibold text-gray-700 mb-2\",\n                                                        children: ((_dict_common3 = dict.common) === null || _dict_common3 === void 0 ? void 0 : _dict_common3.view_more_products) || (locale === \"zh\" ? \"查看更多产品\" : \"View More Products\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"text-sm text-gray-500 mb-3\",\n                                                        children: ((_dict_common4 = dict.common) === null || _dict_common4 === void 0 ? void 0 : _dict_common4.explore_complete_series) || (locale === \"zh\" ? \"探索我们的完整产品系列\" : \"Explore our complete product series\")\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"flex items-center justify-center text-blue-600 text-sm font-medium\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-3d32dd8e0cf21aba\",\n                                                                children: ((_dict_common5 = dict.common) === null || _dict_common5 === void 0 ? void 0 : _dict_common5.browse_all) || (locale === \"zh\" ? \"浏览全部\" : \"Browse All\")\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"fas fa-arrow-right ml-2 transition-transform group-hover:translate-x-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                lineNumber: 379,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"container mx-auto px-4 mb-16 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                    href: \"/\".concat(locale, \"/products\"),\n                    className: \"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"jsx-3d32dd8e0cf21aba\" + \" \" + \"fas fa-arrow-left mr-2\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, this),\n                        ((_dict_common6 = dict.common) === null || _dict_common6 === void 0 ? void 0 : _dict_common6.back_to_products) || (locale === \"zh\" ? \"返回产品中心\" : \"Back to Products\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n                lineNumber: 456,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"3d32dd8e0cf21aba\",\n                children: '.section-gallery.jsx-3d32dd8e0cf21aba{max-width:1e3px;margin:0 auto}.main-image-container.jsx-3d32dd8e0cf21aba{max-width:800px;margin:0 auto}.thumbnails-grid.jsx-3d32dd8e0cf21aba{max-width:600px;margin:0 auto}.line-clamp-1.jsx-3d32dd8e0cf21aba{display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;overflow:hidden}.line-clamp-2.jsx-3d32dd8e0cf21aba{display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.scrollbar-hide.jsx-3d32dd8e0cf21aba{-ms-overflow-style:none;scrollbar-width:none}.scrollbar-hide.jsx-3d32dd8e0cf21aba::-webkit-scrollbar{display:none}.products-horizontal-scroll.jsx-3d32dd8e0cf21aba{position:relative}.products-horizontal-scroll.jsx-3d32dd8e0cf21aba::before,.products-horizontal-scroll.jsx-3d32dd8e0cf21aba::after{content:\"\";position:absolute;top:0;bottom:0;width:20px;z-index:10;pointer-events:none}.products-horizontal-scroll.jsx-3d32dd8e0cf21aba::before{left:0;background:-webkit-linear-gradient(left,rgba(255,255,255,1),rgba(255,255,255,0));background:-moz-linear-gradient(left,rgba(255,255,255,1),rgba(255,255,255,0));background:-o-linear-gradient(left,rgba(255,255,255,1),rgba(255,255,255,0));background:linear-gradient(to right,rgba(255,255,255,1),rgba(255,255,255,0))}.products-horizontal-scroll.jsx-3d32dd8e0cf21aba::after{right:0;background:-webkit-linear-gradient(right,rgba(255,255,255,1),rgba(255,255,255,0));background:-moz-linear-gradient(right,rgba(255,255,255,1),rgba(255,255,255,0));background:-o-linear-gradient(right,rgba(255,255,255,1),rgba(255,255,255,0));background:linear-gradient(to left,rgba(255,255,255,1),rgba(255,255,255,0))}.product-mini-card.jsx-3d32dd8e0cf21aba:hover .bg-blue-100.jsx-3d32dd8e0cf21aba{background-color:rgb(219 234 254)}.animate-slide-in-left.jsx-3d32dd8e0cf21aba{-webkit-animation:slideInLeft 1s ease-out.5s both;-moz-animation:slideInLeft 1s ease-out.5s both;-o-animation:slideInLeft 1s ease-out.5s both;animation:slideInLeft 1s ease-out.5s both}.animate-fade-in-up.jsx-3d32dd8e0cf21aba{-webkit-animation:fadeInUp.8s ease-out;-moz-animation:fadeInUp.8s ease-out;-o-animation:fadeInUp.8s ease-out;animation:fadeInUp.8s ease-out}.animate-fade-in-up-delay.jsx-3d32dd8e0cf21aba{-webkit-animation:fadeInUp.8s ease-out.2s both;-moz-animation:fadeInUp.8s ease-out.2s both;-o-animation:fadeInUp.8s ease-out.2s both;animation:fadeInUp.8s ease-out.2s both}@-webkit-keyframes slideInLeft{from{opacity:0;-webkit-transform:translatex(-30px);transform:translatex(-30px)}to{opacity:1;-webkit-transform:translatex(0);transform:translatex(0)}}@-moz-keyframes slideInLeft{from{opacity:0;-moz-transform:translatex(-30px);transform:translatex(-30px)}to{opacity:1;-moz-transform:translatex(0);transform:translatex(0)}}@-o-keyframes slideInLeft{from{opacity:0;-o-transform:translatex(-30px);transform:translatex(-30px)}to{opacity:1;-o-transform:translatex(0);transform:translatex(0)}}@keyframes slideInLeft{from{opacity:0;-webkit-transform:translatex(-30px);-moz-transform:translatex(-30px);-o-transform:translatex(-30px);transform:translatex(-30px)}to{opacity:1;-webkit-transform:translatex(0);-moz-transform:translatex(0);-o-transform:translatex(0);transform:translatex(0)}}@-webkit-keyframes fadeInUp{from{opacity:0;-webkit-transform:translatey(20px);transform:translatey(20px)}to{opacity:1;-webkit-transform:translatey(0);transform:translatey(0)}}@-moz-keyframes fadeInUp{from{opacity:0;-moz-transform:translatey(20px);transform:translatey(20px)}to{opacity:1;-moz-transform:translatey(0);transform:translatey(0)}}@-o-keyframes fadeInUp{from{opacity:0;-o-transform:translatey(20px);transform:translatey(20px)}to{opacity:1;-o-transform:translatey(0);transform:translatey(0)}}@keyframes fadeInUp{from{opacity:0;-webkit-transform:translatey(20px);-moz-transform:translatey(20px);-o-transform:translatey(20px);transform:translatey(20px)}to{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}}.text-shadow-lg.jsx-3d32dd8e0cf21aba{text-shadow:0 2px 4px rgba(0,0,0,.8),0 0 8px rgba(0,0,0,.5)}.breadcrumbs-overlay.jsx-3d32dd8e0cf21aba a.jsx-3d32dd8e0cf21aba{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px);-webkit-box-shadow:0 2px 8px rgba(0,0,0,.3);-moz-box-shadow:0 2px 8px rgba(0,0,0,.3);box-shadow:0 2px 8px rgba(0,0,0,.3)}.breadcrumbs-overlay.jsx-3d32dd8e0cf21aba a.jsx-3d32dd8e0cf21aba:hover{-webkit-box-shadow:0 4px 12px rgba(0,0,0,.4);-moz-box-shadow:0 4px 12px rgba(0,0,0,.4);box-shadow:0 4px 12px rgba(0,0,0,.4);-webkit-transform:translatey(-2px);-moz-transform:translatey(-2px);-ms-transform:translatey(-2px);-o-transform:translatey(-2px);transform:translatey(-2px)}.breadcrumbs-overlay.jsx-3d32dd8e0cf21aba span.jsx-3d32dd8e0cf21aba:last-child{-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px);-webkit-box-shadow:0 2px 8px rgba(0,0,0,.2);-moz-box-shadow:0 2px 8px rgba(0,0,0,.2);box-shadow:0 2px 8px rgba(0,0,0,.2)}@media(max-width:768px){.products-horizontal-scroll.jsx-3d32dd8e0cf21aba .flex.jsx-3d32dd8e0cf21aba{padding-left:1rem;padding-right:1rem}.product-mini-card.jsx-3d32dd8e0cf21aba .w-72.jsx-3d32dd8e0cf21aba{width:16rem}.breadcrumbs-overlay.jsx-3d32dd8e0cf21aba{font-size:.75rem}.breadcrumbs-overlay.jsx-3d32dd8e0cf21aba a.jsx-3d32dd8e0cf21aba,.breadcrumbs-overlay.jsx-3d32dd8e0cf21aba span.jsx-3d32dd8e0cf21aba{padding:.375rem .5rem}}'\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\app\\\\[lang]\\\\sections\\\\[slug]\\\\page.tsx\",\n        lineNumber: 319,\n        columnNumber: 5\n    }, this);\n}\n_s(SectionPage, \"WenloY4mgKOnIMLW/ZrN91EYxFI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _components_LanguageProvider__WEBPACK_IMPORTED_MODULE_6__.useLanguage\n    ];\n});\n_c = SectionPage;\nvar _c;\n$RefreshReg$(_c, \"SectionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[lang]/sections/[slug]/page.tsx\n"));

/***/ })

});