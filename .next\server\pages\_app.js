"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./components/ApiPortFixer.jsx":
/*!*************************************!*\
  !*** ./components/ApiPortFixer.jsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_apiUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/apiUtils */ \"./lib/apiUtils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\r\n * API端口修复组件\r\n * \r\n * 此组件劫持全局fetch和XMLHttpRequest API，\r\n * 自动将API请求的端口号修复为当前页面使用的端口号\r\n */ const ApiPortFixer = ()=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // 只在客户端执行\n        if (true) return;\n        // 保存原始fetch函数\n        const originalFetch = window.fetch;\n        // 重写fetch函数\n        window.fetch = function(url, options) {\n            // 修复URL中的端口号\n            const fixedUrl = (0,_lib_apiUtils__WEBPACK_IMPORTED_MODULE_1__.fixApiUrl)(url);\n            // 调用原始fetch\n            return originalFetch.call(this, fixedUrl, options);\n        };\n        // 保存原始XMLHttpRequest.open方法\n        const originalXhrOpen = XMLHttpRequest.prototype.open;\n        // 重写XMLHttpRequest.open方法\n        XMLHttpRequest.prototype.open = function(method, url, ...args) {\n            // 修复URL中的端口号\n            const fixedUrl = (0,_lib_apiUtils__WEBPACK_IMPORTED_MODULE_1__.fixApiUrl)(url);\n            // 调用原始open方法\n            return originalXhrOpen.call(this, method, fixedUrl, ...args);\n        };\n        // 添加端口检测日志\n        console.log(`[API端口修复] 当前使用端口: ${window.location.port || \"80/443\"}`);\n        // 清理函数\n        return ()=>{\n            // 恢复原始函数\n            window.fetch = originalFetch;\n            XMLHttpRequest.prototype.open = originalXhrOpen;\n        };\n    }, []);\n    // 此组件不渲染任何内容\n    return null;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ApiPortFixer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/ApiPortFixer.jsx\n");

/***/ }),

/***/ "./components/DbConnectionMonitor.jsx":
/*!********************************************!*\
  !*** ./components/DbConnectionMonitor.jsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\r\n * 数据库连接监控组件\r\n * \r\n * 监控数据库连接状态并显示状态指示器\r\n * 在连接失败时自动尝试重新连接\r\n */ const DbConnectionMonitor = ()=>{\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"checking\");\n    const [lastError, setLastError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [retryCount, setRetryCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // 是否为开发环境\n    const isDev = \"development\" === \"development\";\n    // 检查数据库健康状态\n    const checkDbHealth = async ()=>{\n        try {\n            const response = await fetch(\"/api/db-health\", {\n                method: \"GET\",\n                headers: {\n                    \"Cache-Control\": \"no-cache\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setConnectionStatus(data.status);\n                setLastError(data.error || null);\n                // 如果连接恢复，重置重试计数\n                if (data.status === \"healthy\") {\n                    setRetryCount(0);\n                }\n                return data.status === \"healthy\";\n            } else {\n                setConnectionStatus(\"error\");\n                setLastError(`HTTP错误: ${response.status}`);\n                return false;\n            }\n        } catch (error) {\n            setConnectionStatus(\"error\");\n            setLastError(error.message);\n            return false;\n        }\n    };\n    // 初始检查和定时轮询\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 初始检查\n        checkDbHealth();\n        // 设置轮询间隔\n        const interval = setInterval(()=>{\n            checkDbHealth().then((isHealthy)=>{\n                // 如果不健康，增加重试计数\n                if (!isHealthy) {\n                    setRetryCount((prev)=>prev + 1);\n                }\n            });\n        }, 30000); // 30秒检查一次\n        return ()=>clearInterval(interval);\n    }, []);\n    // 控制组件可见性\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // 只在开发环境或连接出错时显示\n        setIsVisible(isDev || connectionStatus !== \"healthy\");\n    }, [\n        connectionStatus,\n        isDev\n    ]);\n    // 如果组件不可见，不渲染任何内容\n    if (!isVisible) return null;\n    // 状态颜色映射\n    const statusColors = {\n        \"healthy\": \"bg-green-500\",\n        \"unhealthy\": \"bg-red-500\",\n        \"checking\": \"bg-yellow-500\",\n        \"error\": \"bg-red-500\"\n    };\n    // 状态文本映射\n    const statusText = {\n        \"healthy\": \"数据库连接正常\",\n        \"unhealthy\": \"数据库连接异常\",\n        \"checking\": \"正在检查数据库连接...\",\n        \"error\": \"数据库连接失败\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 shadow-lg rounded-lg bg-white dark:bg-gray-800 p-3 text-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `w-3 h-3 rounded-full ${statusColors[connectionStatus] || \"bg-gray-500\"}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\DbConnectionMonitor.jsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: statusText[connectionStatus] || \"未知状态\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\DbConnectionMonitor.jsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined),\n                    retryCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500\",\n                        children: [\n                            \"(重试: \",\n                            retryCount,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\DbConnectionMonitor.jsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>checkDbHealth(),\n                        className: \"ml-2 px-2 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded\",\n                        children: \"重试\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\DbConnectionMonitor.jsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setIsVisible(false),\n                        className: \"ml-2 px-2 py-1 text-xs bg-gray-300 hover:bg-gray-400 text-gray-800 rounded\",\n                        children: \"隐藏\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\DbConnectionMonitor.jsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\DbConnectionMonitor.jsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined),\n            lastError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 text-xs text-red-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            \"错误: \",\n                            lastError\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\DbConnectionMonitor.jsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-1 text-gray-500\",\n                        children: \"使用模拟数据作为降级措施，数据可能不是最新的。\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\DbConnectionMonitor.jsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\DbConnectionMonitor.jsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\DbConnectionMonitor.jsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DbConnectionMonitor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/DbConnectionMonitor.jsx\n");

/***/ }),

/***/ "./components/FloatingCacheCleaner.jsx":
/*!*********************************************!*\
  !*** ./components/FloatingCacheCleaner.jsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\r\n * 悬浮缓存清理按钮\r\n * 方便开发人员快速清除缓存\r\n */ const FloatingCacheCleaner = ({ onlyInDev = true })=>{\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 是否显示组件\n    const isDev = \"development\" === \"development\";\n    if (onlyInDev && !isDev) return null;\n    // 清除缓存\n    const clearCache = async (type = \"all\")=>{\n        setIsLoading(true);\n        setResult(null);\n        try {\n            const response = await fetch(`/api/cache/clear?type=${type}`, {\n                method: \"GET\",\n                headers: {\n                    \"Cache-Control\": \"no-cache\",\n                    \"Pragma\": \"no-cache\"\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setResult({\n                    success: true,\n                    message: data.message || `缓存已清除 (${type})`\n                });\n                // 如果是客户端缓存，刷新页面\n                if (type === \"client\" || type === \"all\") {\n                    setTimeout(()=>{\n                        window.location.reload();\n                    }, 1500);\n                }\n            } else {\n                setResult({\n                    success: false,\n                    message: `清除失败: ${response.status}`\n                });\n            }\n        } catch (error) {\n            setResult({\n                success: false,\n                message: error.message || \"发生错误\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 清除localStorage\n    const clearLocalStorage = ()=>{\n        try {\n            localStorage.clear();\n            setResult({\n                success: true,\n                message: \"浏览器存储已清除\"\n            });\n            setTimeout(()=>{\n                window.location.reload();\n            }, 1500);\n        } catch (error) {\n            setResult({\n                success: false,\n                message: error.message || \"清除失败\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 left-4 z-50 flex flex-col items-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-full shadow-lg flex items-center justify-center\",\n                title: \"缓存管理\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M18 6V4H6v2\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M12 2v6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                            x: \"2\",\n                            y: \"8\",\n                            width: \"20\",\n                            height: \"14\",\n                            rx: \"2\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M6 16h.01\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M10 16h.01\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M14 16h.01\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M18 16h.01\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 min-w-[200px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm font-medium mb-2\",\n                        children: \"缓存清理\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>clearCache(\"all\"),\n                                disabled: isLoading,\n                                className: \"w-full text-left px-3 py-2 text-sm rounded bg-red-100 hover:bg-red-200 text-red-800 disabled:opacity-50\",\n                                children: \"清除所有缓存\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>clearCache(\"client\"),\n                                disabled: isLoading,\n                                className: \"w-full text-left px-3 py-2 text-sm rounded bg-blue-100 hover:bg-blue-200 text-blue-800 disabled:opacity-50\",\n                                children: \"清除客户端缓存\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearLocalStorage,\n                                disabled: isLoading,\n                                className: \"w-full text-left px-3 py-2 text-sm rounded bg-yellow-100 hover:bg-yellow-200 text-yellow-800 disabled:opacity-50\",\n                                children: \"清除浏览器存储\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined),\n                    result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `mt-2 p-2 text-xs rounded ${result.success ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"}`,\n                        children: [\n                            result.message,\n                            result.success && (result.message.includes(\"客户端\") || result.message.includes(\"浏览器\")) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"italic mt-1\",\n                                children: \"页面将在1.5秒后刷新...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                                lineNumber: 136,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                        lineNumber: 133,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\components\\\\FloatingCacheCleaner.jsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FloatingCacheCleaner);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/FloatingCacheCleaner.jsx\n");

/***/ }),

/***/ "./components/HighContrastFixer.jsx":
/*!******************************************!*\
  !*** ./components/HighContrastFixer.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HighContrastFixer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\r\n * HighContrastFixer组件\r\n * \r\n * 这个组件解决Microsoft Edge中-ms-high-contrast属性弃用的问题，\r\n * 通过在应用启动时注入一个脚本来拦截相关API调用并替换为现代标准。\r\n */ function HighContrastFixer() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // 避免重复初始化\n        if (window.__highContrastFixerInitialized) return;\n        window.__highContrastFixerInitialized = true;\n        // 创建并注入改进版修复脚本\n        const script = document.createElement(\"script\");\n        script.id = \"high-contrast-fixer-script\";\n        script.innerHTML = `\r\n      (function() {\r\n        'use strict';\r\n        \r\n        // 存储原始的CSS API方法\r\n        const originalSetProperty = CSSStyleDeclaration.prototype.setProperty;\r\n        const originalGetPropertyValue = CSSStyleDeclaration.prototype.getPropertyValue;\r\n        const originalRemoveProperty = CSSStyleDeclaration.prototype.removeProperty;\r\n        const originalGetComputedStyle = window.getComputedStyle;\r\n        const originalMatchMedia = window.matchMedia;\r\n        \r\n        // 属性映射\r\n        const propertyMap = {\r\n          \"forced-colors\": 'forced-colors',\r\n          \"forced-color-adjust\": 'forced-color-adjust',\r\n          'forcedColors': 'forcedColors',\r\n          'forcedColorsAdjust': 'forcedColorAdjust'\r\n        };\r\n        \r\n        // 重写setProperty方法\r\n        CSSStyleDeclaration.prototype.setProperty = function(propertyName, value, priority) {\r\n          if (propertyName in propertyMap) {\r\n            return originalSetProperty.call(this, propertyMap[propertyName], value, priority);\r\n          }\r\n          return originalSetProperty.call(this, propertyName, value, priority);\r\n        };\r\n        \r\n        // 重写getPropertyValue方法\r\n        CSSStyleDeclaration.prototype.getPropertyValue = function(propertyName) {\r\n          if (propertyName in propertyMap) {\r\n            console.log(\\`[High Contrast Fix] 重定向CSS属性获取: \\${propertyName} → \\${propertyMap[propertyName]}\\`);\r\n            return originalGetPropertyValue.call(this, propertyMap[propertyName]);\r\n          }\r\n          return originalGetPropertyValue.call(this, propertyName);\r\n        };\r\n        \r\n        // 重写removeProperty方法\r\n        CSSStyleDeclaration.prototype.removeProperty = function(propertyName) {\r\n          if (propertyName in propertyMap) {\r\n            return originalRemoveProperty.call(this, propertyMap[propertyName]);\r\n          }\r\n          return originalRemoveProperty.call(this, propertyName);\r\n        };\r\n        \r\n        // 重写matchMedia方法\r\n        window.matchMedia = function(query) {\r\n          if (typeof query === 'string') {\r\n            // 替换-ms-high-contrast媒体查询\r\n            query = query.replace(/\\\\(\\\\s*-ms-high-contrast\\\\s*:\\\\s*[^\\\\)]+\\\\)/g, '(forced-colors: active)');\r\n          }\r\n          return originalMatchMedia.call(window, query);\r\n        };\r\n        \r\n        // 替换已存在的CSS规则\r\n        function processCSSRules() {\r\n          try {\r\n            for (let i = 0; i < document.styleSheets.length; i++) {\r\n              const sheet = document.styleSheets[i];\r\n              \r\n              try {\r\n                // 跳过无法访问的样式表（如跨域样式表）\r\n                if (!sheet.cssRules) continue;\r\n                \r\n                // 处理每条规则\r\n                for (let j = 0; j < sheet.cssRules.length; j++) {\r\n                  const rule = sheet.cssRules[j];\r\n                  \r\n                  // 处理媒体查询规则\r\n                  if (rule.type === CSSRule.MEDIA_RULE) {\r\n                    if (rule.media && rule.media.mediaText.includes('-ms-high-contrast')) {\r\n                      const newMediaText = rule.media.mediaText.replace(\r\n                        /\\\\(\\\\s*-ms-high-contrast\\\\s*:\\\\s*[^\\\\)]+\\\\)/g,\r\n                        '(forced-colors: active)'\r\n                      );\r\n                      \r\n                      // 创建新规则\r\n                      const cssText = rule.cssText.replace(rule.media.mediaText, newMediaText);\r\n                      \r\n                      try {\r\n                        // 删除旧规则并插入新规则\r\n                        sheet.deleteRule(j);\r\n                        sheet.insertRule(cssText, j);\r\n                      } catch (e) {\r\n                        // 某些环境下可能无法修改规则\r\n                        console.debug('[High Contrast Fix] 无法替换媒体查询规则', e);\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n              } catch (e) {\r\n                // 跨域样式表可能会引发安全错误\r\n                console.debug('[High Contrast Fix] 无法处理样式表', e);\r\n              }\r\n            }\r\n          } catch (e) {\r\n            console.debug('[High Contrast Fix] 处理CSS规则出错', e);\r\n          }\r\n        }\r\n        \r\n        // 添加全局覆盖样式\r\n        function addOverrideStyles() {\r\n          const styleId = 'high-contrast-override-styles';\r\n          if (document.getElementById(styleId)) return;\r\n          \r\n          const style = document.createElement('style');\r\n          style.id = styleId;\r\n          style.textContent = \\`\r\n            /* 防止-ms-high-contrast媒体查询生效 */\r\n            @media (forced-colors: active), (forced-colors: none) {\r\n              * { \r\n                forced-color-adjust: none !important;\r\n                forced-color-adjust: none !important;\r\n              }\r\n            }\r\n            \r\n            /* 现代高对比度模式下保持适当的样式 */\r\n            @media (forced-colors: active) {\r\n              /* 恢复关键交互元素的对比度 */\r\n              a, button, input, select, textarea, [role=\"button\"], .btn {\r\n                forced-color-adjust: auto !important;\r\n              }\r\n            }\r\n          \\`;\r\n          \r\n          document.head.appendChild(style);\r\n        }\r\n        \r\n        // 初始化函数\r\n        function init() {\r\n          addOverrideStyles();\r\n          processCSSRules();\r\n          \r\n          // 设置MutationObserver监听新加载的样式表\r\n          if (typeof MutationObserver !== 'undefined') {\r\n            const observer = new MutationObserver(function(mutations) {\r\n              let styleChanged = false;\r\n              \r\n              for (const mutation of mutations) {\r\n                if (mutation.type === 'childList') {\r\n                  const addedNodes = Array.from(mutation.addedNodes);\r\n                  styleChanged = addedNodes.some(node => \r\n                    node.nodeName === 'STYLE' || \r\n                    (node.nodeName === 'LINK' && node.rel === 'stylesheet')\r\n                  );\r\n                  \r\n                  if (styleChanged) break;\r\n                }\r\n              }\r\n              \r\n              if (styleChanged) {\r\n                // 当样式表变化时重新处理规则\r\n                setTimeout(processCSSRules, 0);\r\n              }\r\n            });\r\n            \r\n            observer.observe(document.head, { childList: true, subtree: true });\r\n            observer.observe(document.body, { childList: true, subtree: true });\r\n          }\r\n        }\r\n        \r\n        // 如果DOM已经准备好，立即初始化；否则等待DOM内容加载\r\n        if (document.readyState === 'loading') {\r\n          document.addEventListener('DOMContentLoaded', init);\r\n        } else {\r\n          init();\r\n        }\r\n        \r\n        // 在window加载完成后再次运行，确保捕获所有动态加载的资源\r\n        window.addEventListener('load', processCSSRules);\r\n      })();\r\n    `;\n        // 将脚本插入到页面\n        document.head.appendChild(script);\n    }, []);\n    // 组件不渲染任何内容\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/HighContrastFixer.jsx\n");

/***/ }),

/***/ "./components/PortRedirector.tsx":
/*!***************************************!*\
  !*** ./components/PortRedirector.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n/**\r\n * 端口重定向组件\r\n * \r\n * 当用户访问错误端口时(例如3000而服务器在3001上)，\r\n * 自动将用户重定向到正确的端口\r\n */ const PortRedirector = ()=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // 只在客户端执行\n        if (true) return;\n        // 获取当前URL和路径\n        const currentUrl = window.location.href;\n        const currentPath = window.location.pathname;\n        // 检查是否有404错误和端口是否为3000\n        const is404 = document.title.includes(\"404\") || document.body.innerHTML.includes(\"404\");\n        const isPort3000 = window.location.port === \"3000\";\n        // 如果是404错误且端口是3000，尝试端口3001\n        if ((is404 || currentPath.endsWith(\"/admin/login\")) && isPort3000) {\n            console.log(\"检测到端口3000上的访问，尝试重定向到端口3001...\");\n            // 构建新的URL，使用端口3001\n            const newUrl = currentUrl.replace(\":3000\", \":3001\");\n            // 重定向到新URL\n            window.location.href = newUrl;\n        }\n    }, []);\n    // 此组件不渲染任何内容\n    return null;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PortRedirector);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/PortRedirector.tsx\n");

/***/ }),

/***/ "./lib/apiUtils.js":
/*!*************************!*\
  !*** ./lib/apiUtils.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchApi: () => (/* binding */ fetchApi),\n/* harmony export */   fixApiUrl: () => (/* binding */ fixApiUrl),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getBaseUrl: () => (/* binding */ getBaseUrl)\n/* harmony export */ });\n/**\r\n * API工具函数\r\n * 用于处理API请求URL和端口问题\r\n */ /**\r\n * 获取当前运行的应用基础URL\r\n * 会自动检测当前端口\r\n */ function getBaseUrl() {\n    // 只在客户端执行\n    if (false) {}\n    // 服务器端，使用环境变量或默认值\n    if (process.env.NEXT_PUBLIC_BASE_URL) {\n        return process.env.NEXT_PUBLIC_BASE_URL;\n    }\n    // 开发环境默认值\n    return  true ? \"http://localhost:3001\" : 0;\n}\n/**\r\n * 构建API URL\r\n * @param {string} path - API路径，不带前导斜杠\r\n * @returns {string} 完整的API URL\r\n */ function getApiUrl(path) {\n    const baseUrl = getBaseUrl();\n    const apiPath = path.startsWith(\"/\") ? path : `/${path}`;\n    return `${baseUrl}/api${apiPath}`;\n}\n/**\r\n * 处理API请求，自动处理URL和错误\r\n * @param {string} path - API路径\r\n * @param {Object} options - fetch选项\r\n * @returns {Promise<Object>} 响应数据\r\n */ async function fetchApi(path, options = {}) {\n    const url = getApiUrl(path);\n    try {\n        const response = await fetch(url, {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...options.headers\n            },\n            ...options\n        });\n        // 尝试解析JSON\n        const data = await response.json();\n        // 检查API响应状态\n        if (!response.ok) {\n            console.error(`API错误 (${response.status}):`, data);\n            throw new Error(data.message || `API请求失败: ${response.statusText}`);\n        }\n        return data;\n    } catch (error) {\n        console.error(`API请求失败 (${path}):`, error);\n        throw error;\n    }\n}\n/**\r\n * 修复API URL，确保使用正确的端口\r\n * @param {string} url - 原始URL\r\n * @returns {string} 修复后的URL\r\n */ function fixApiUrl(url) {\n    if (true) return url;\n    // 只处理相对URL或同源URL\n    if (url.startsWith(\"/\") || url.startsWith(window.location.origin)) {\n        const currentPort = window.location.port;\n        // 如果URL包含端口信息，确保与当前页面端口一致\n        if (url.includes(\":3000/\") && currentPort === \"3001\") {\n            return url.replace(\":3000/\", \":3001/\");\n        }\n        if (url.includes(\":3001/\") && currentPort === \"3000\") {\n            return url.replace(\":3001/\", \":3000/\");\n        }\n    }\n    return url;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./lib/apiUtils.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_PortRedirector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/PortRedirector */ \"./components/PortRedirector.tsx\");\n/* harmony import */ var _components_ApiPortFixer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ApiPortFixer */ \"./components/ApiPortFixer.jsx\");\n/* harmony import */ var _components_DbConnectionMonitor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/DbConnectionMonitor */ \"./components/DbConnectionMonitor.jsx\");\n/* harmony import */ var _components_FloatingCacheCleaner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/FloatingCacheCleaner */ \"./components/FloatingCacheCleaner.jsx\");\n/* harmony import */ var _components_HighContrastFixer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/HighContrastFixer */ \"./components/HighContrastFixer.jsx\");\n/**\n * Custom App component\n * This is needed for Next.js to properly handle the application structure\n * It works together with _document.js to provide the base structure\n */ \n\n\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        session: pageProps.session,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HighContrastFixer__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\pages\\\\_app.js\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PortRedirector__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\pages\\\\_app.js\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ApiPortFixer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\pages\\\\_app.js\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DbConnectionMonitor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\pages\\\\_app.js\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FloatingCacheCleaner__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                onlyInDev: true\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\pages\\\\_app.js\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\pages\\\\_app.js\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AIGC-dm\\\\Cross-border E-commerce Website Project\\\\nextjs\\\\pages\\\\_app.js\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("next-auth/react");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_app.js"));
module.exports = __webpack_exports__;

})();