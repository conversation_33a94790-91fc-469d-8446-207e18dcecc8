const fetch = require('node-fetch');

async function simpleTest() {
  try {
    console.log('🔍 简单测试页面内容...\n');
    
    const response = await fetch('http://localhost:3000/zh/products/motion-sensing-climbing');
    const html = await response.text();
    
    console.log(`页面大小: ${(html.length / 1024).toFixed(2)} KB`);
    
    // 查找关键内容
    const searches = [
      '体感攀岩系统',
      'Product Information Section',
      '产品信息',
      '产品概述',
      '技术规格',
      '应用场景',
      'py-12',
      'gap-8',
      'py-64',
      'scale-125'
    ];
    
    console.log('\n内容搜索结果:');
    searches.forEach(search => {
      const found = html.includes(search);
      console.log(`${found ? '✅' : '❌'} ${search}`);
    });
    
    // 查找页面结构
    console.log('\n页面结构:');
    const divCount = (html.match(/<div/g) || []).length;
    const closingDivCount = (html.match(/<\/div>/g) || []).length;
    console.log(`div标签: ${divCount} 开始, ${closingDivCount} 结束`);
    
    // 查找产品详情容器
    const containerMatch = html.match(/product-detail-container/);
    console.log(`产品详情容器: ${containerMatch ? '找到' : '未找到'}`);
    
    // 查找最后的内容
    console.log('\n页面最后200字符:');
    console.log(html.substring(html.length - 200));
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

simpleTest();
